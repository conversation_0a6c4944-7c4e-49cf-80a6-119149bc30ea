server:
  address: ":1238"
  shutdown.timeout: "10s"
  body.limit: "100G"

logger:
  level: info
  dir: "log"
  filename: "file.log"
  writeToFile: true

# Rest client
http:
  maxIdleConns: 2
  idleConnTimeout: '10s'
  disableCompression: false

graphql:
  websocket:
    keepAlivePingInterval: 10s

auth:
  exclude:
    - "^/api/event/public.*"
    - "^/internal.*"

chat:
  url:
    base: "http://localhost:1235"
    access: "/internal/access"

aaa:
  url:
    base: "http://localhost:8060"
    requestForOnline: "/internal/user/request-for-online"
    userExtended: "/internal/user/extended"

rabbitmq:
  url: "amqp://videoChat:videoChatPazZw0rd@127.0.0.1:36672"

otlp:
  endpoint: "localhost:34317"
