{"scripts": {"dev": "npm run server:dev", "prod": "npm run build && npm run server:prod", "build": "vite build", "server:dev": "node ./server", "server:prod": "cross-env NODE_ENV=production node ./server"}, "dependencies": {"@mdi/font": "^7.4.47", "@opentelemetry/api": "^1.9.0", "@opentelemetry/instrumentation-express": "^0.47.0", "@opentelemetry/instrumentation-http": "^0.57.0", "@opentelemetry/instrumentation-winston": "^0.44.0", "@opentelemetry/propagator-jaeger": "^1.30.0", "@opentelemetry/resources": "^1.29.0", "@opentelemetry/sdk-node": "^0.56.0", "@opentelemetry/semantic-conventions": "^1.28.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/server-renderer": "^3.5.13", "axios": "^1.7.9", "compression": "^1.7.5", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "express": "^4.21.2", "he": "^1.2.0", "lodash": "^4.17.21", "mark.js": "^8.11.1", "mitt": "^3.0.1", "morgan": "^1.10.0", "sirv": "^3.0.0", "sitemap": "^8.0.0", "typeface-roboto": "^1.1.13", "vike": "^0.4.209", "vite": "^6.0.3", "vite-plugin-vuetify": "^2.0.4", "vue": "3.5.13", "vuetify": "3.8.1", "winston": "^3.17.0"}, "type": "module", "devDependencies": {"sass": "1.83.0", "stylus": "^0.64.0"}}