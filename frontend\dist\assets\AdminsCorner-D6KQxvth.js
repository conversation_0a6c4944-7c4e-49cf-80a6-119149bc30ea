import{_ as i,m as n,c as u,w as a,b as e,b2 as f,d9 as l,e as s,t as r,b4 as o,V as c,p as d,s as _,A as b,B as h}from"./appMain-DAGYo8tP.js";const $={computed:{...n(b)},mounted(){const t=this.$vuetify.locale.t("$vuetify.admins_corner");this.chatStore.title=t,_(t)},beforeUnmount(){this.chatStore.title=null}};function p(t,g,m,y,v,k){return h(),u(d,{fluid:""},{default:a(()=>[e(c,null,{default:a(()=>[e(f,null,{default:a(()=>[e(l,null,{default:a(()=>[s(r(t.$vuetify.locale.t("$vuetify.logs")),1)]),_:1}),e(o,{title:"Opensearch Dashboards",href:"/opensearch-dashboards",target:"_blank"}),e(l,null,{default:a(()=>[s(r(t.$vuetify.locale.t("$vuetify.tracing")),1)]),_:1}),e(o,{title:"Jaeger",href:"/jaeger",target:"_blank"}),e(l,null,{default:a(()=>[s(r(t.$vuetify.locale.t("$vuetify.object_storage")),1)]),_:1}),e(o,{title:"Minio",href:"/minio/console",target:"_blank"}),e(l,null,{default:a(()=>[s(r(t.$vuetify.locale.t("$vuetify.queue_broker")),1)]),_:1}),e(o,{title:"RabbitMQ",href:"/rabbitmq/",target:"_blank"}),e(l,null,{default:a(()=>[s(r(t.$vuetify.locale.t("$vuetify.database")),1)]),_:1}),e(o,{title:"PostgreSQL",href:"/postgresql",target:"_blank"})]),_:1})]),_:1})]),_:1})}const S=i($,[["render",p]]);export{S as default};
