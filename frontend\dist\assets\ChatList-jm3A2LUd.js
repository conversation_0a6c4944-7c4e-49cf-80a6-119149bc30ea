import{_ as ae,m as ne,aa as oe,a_ as ue,A as re,ap as k,av as P,aB as de,co as fe,T as b,c as h,w as r,b as c,G as L,a8 as le,a7 as w,b2 as he,a6 as U,b3 as me,B as l,b4 as ce,e as m,t as D,l as v,cp as pe,h as ge,bV as ye,ab as ve,f as C,a as u,aZ as Ce,a5 as Se,n as A,p as Ie,z as n,cq as R,S as T,ah as x,ai as N,cr as O,cs as B,c1 as V,ct as H,c2 as F,ak as z,al as W,cm as q,cu as G,b$ as j,u as E,s as Q,ao as be,c7 as Te,c8 as Z,cv as _e,U as $e,ca as Ae,cw as Ee,cx as Me,cy as ke,a2 as d,g as Le,aY as K,aX as _,v as we,cz as Ue,aL as X,aO as Y,aP as J,O as De,a9 as ee,au as Pe,cd as Re,aq as xe,a0 as Ne,cA as Oe,aV as Be,aU as Ve,cB as te,cC as He,k as g,cD as Fe,cE as ze,bX as S,cF as ie,j as I}from"./appMain-DAGYo8tP.js";import{i as We,h as qe,a as Ge,V as je}from"./hashMixin-OoEehrvU.js";const Qe={mixins:[oe(k),ue()],props:["canResend","isBlog"],data(){return{selection:null}},methods:{className(){return"message-item-context-menu"},onShowContextMenu(e,t){this.selection=this.getSelection(),this.onShowContextMenuBase(e,t)},onCloseContextMenu(){this.selection=null,this.onCloseContextMenuBase()},getContextMenuItems(){var t;const e=[];return this.menuableItem&&(this.isMobile()&&e.push({title:this.$vuetify.locale.t("$vuetify.close"),icon:"mdi-close",action:()=>{this.onCloseContextMenu()}}),b(this.selection)&&e.push({title:this.$vuetify.locale.t("$vuetify.search_by_selected"),icon:"mdi-clipboard-search-outline",action:this.searchBySelected}),this.menuableItem.fileItemUuid&&e.push({title:this.$vuetify.locale.t("$vuetify.attached_message_files"),icon:"mdi-file-download",action:()=>this.$emit("onFilesClicked",this.menuableItem)}),this.menuableItem.canDelete&&e.push({title:this.$vuetify.locale.t("$vuetify.delete_btn"),icon:"mdi-delete",iconColor:"error",action:()=>this.$emit("deleteMessage",this.menuableItem)}),this.menuableItem.canEdit&&e.push({title:this.$vuetify.locale.t("$vuetify.edit"),icon:"mdi-lead-pencil",iconColor:"primary",action:()=>this.$emit("editMessage",this.menuableItem)}),b(this.selection)&&e.push({title:this.$vuetify.locale.t("$vuetify.copy_selected"),icon:"mdi-content-copy",action:this.copySelected}),this.isMobile()||e.push({title:this.$vuetify.locale.t("$vuetify.copy"),icon:"mdi-content-copy",action:this.copy}),e.push({title:this.$vuetify.locale.t("$vuetify.copy_text"),icon:"mdi-content-copy",action:this.copyText}),e.push({title:this.$vuetify.locale.t("$vuetify.users_read"),icon:"mdi-account-supervisor",action:()=>this.$emit("showReadUsers",this.menuableItem)}),this.menuableItem.canPin&&(this.menuableItem.pinned?e.push({title:this.$vuetify.locale.t("$vuetify.remove_from_pinned"),icon:"mdi-pin-off-outline",action:()=>this.$emit("removedFromPinned",this.menuableItem)}):e.push({title:this.$vuetify.locale.t("$vuetify.pin_message"),icon:"mdi-pin",action:()=>this.$emit("pinMessage",this.menuableItem)})),e.push({title:this.$vuetify.locale.t("$vuetify.reply"),icon:"mdi-reply",action:()=>this.$emit("replyOnMessage",this.menuableItem)}),this.canResend&&e.push({title:this.$vuetify.locale.t("$vuetify.resend"),icon:"mdi-share",action:()=>this.$emit("shareMessage",this.menuableItem)}),e.push({title:this.$vuetify.locale.t("$vuetify.copy_link_to_message"),icon:"mdi-link",action:()=>this.copyLink(this.menuableItem)}),!this.menuableItem.blogPost&&this.isBlog&&((t=this.menuableItem.owner)==null?void 0:t.id)==this.chatStore.currentUser.id&&e.push({title:this.$vuetify.locale.t("$vuetify.make_blog_post"),icon:"mdi-postage-stamp",action:()=>this.$emit("makeBlogPost",this.menuableItem)}),this.isBlog&&e.push({title:this.$vuetify.locale.t("$vuetify.go_to_blog_post"),icon:"mdi-postage-stamp",action:()=>this.$emit("goToBlog",this.menuableItem)}),this.areReactionsAllowed&&e.push({title:this.$vuetify.locale.t("$vuetify.add_reaction_on_message"),icon:"mdi-emoticon-outline",action:()=>this.$emit("addReaction",this.menuableItem)}),this.menuableItem.canPublish&&(this.menuableItem.published?(e.push({title:this.$vuetify.locale.t("$vuetify.copy_public_link_to_message"),icon:"mdi-link",action:()=>this.copyPublicLink(this.menuableItem)}),e.push({title:this.$vuetify.locale.t("$vuetify.unpublish_message"),icon:"mdi-lock",action:()=>this.$emit("removePublic",this.menuableItem)})):e.push({title:this.$vuetify.locale.t("$vuetify.publish_message"),icon:"mdi-export",action:()=>this.$emit("publishMessage",this.menuableItem)}))),e},copyLink(e){const t=fe(this.chatId,e.id);navigator.clipboard.writeText(t),this.setTempNotification(this.$vuetify.locale.t("$vuetify.message_link_copied"))},copyPublicLink(e){const t=de(this.chatId,e.id);navigator.clipboard.writeText(t),this.setTempNotification(this.$vuetify.locale.t("$vuetify.published_message_link_copied"))},getSelection(){return window.getSelection().toString()},async copy(){try{if(this.targetEl){const{found:e,el:t}=P(this.targetEl,10,i=>{var o;return(o=i==null?void 0:i.classList)==null?void 0:o.contains("message-item-wrapper")});if(e){const i="text/html",o=new Blob([t.innerHTML],{type:i}),y=new ClipboardItem({[o.type]:o});await navigator.clipboard.write([y]),this.setTempNotification(this.$vuetify.locale.t("$vuetify.message_copied"))}else this.setWarning("element is not found")}}catch(e){this.setError(e,"unable to copy")}},copyText(){try{if(this.targetEl){const{found:e,el:t}=P(this.targetEl,10,i=>{var o;return(o=i==null?void 0:i.classList)==null?void 0:o.contains("message-item-wrapper")});e?(navigator.clipboard.writeText(t.textContent),this.setTempNotification(this.$vuetify.locale.t("$vuetify.message_copied"))):this.setWarning("element is not found")}}catch(e){this.setError(e,"unable to copy")}},copySelected(){try{const e=this.selection;navigator.clipboard.writeText(e),this.setTempNotification(this.$vuetify.locale.t("$vuetify.message_copied"))}catch(e){this.setError(e,"unable to copy")}},searchBySelected(){const e=this.selection;this.searchString=e,this.chatStore.searchType=k}},computed:{chatId(){return this.$route.params.id},...ne(re),areReactionsAllowed(){return this.chatStore.chatDto.canReact}}};function Ze(e,t,i,o,y,a){return l(),h(me,{class:U(a.className()),"model-value":e.showContextMenu,transition:!1,"open-on-click":!1,"open-on-focus":!1,"open-on-hover":!1,"open-delay":0,"close-delay":0,"close-on-back":!1,"onUpdate:modelValue":e.onUpdate},{default:r(()=>[c(he,null,{default:r(()=>[(l(!0),L(w,null,le(a.getContextMenuItems(),(f,s)=>(l(),h(ce,{key:s,onClick:f.action},{prepend:r(()=>[c(v,{color:f.iconColor},{default:r(()=>[m(D(f.icon),1)]),_:2},1032,["color"])]),title:r(()=>[m(D(f.title),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1},8,["class","model-value","onUpdate:modelValue"])}const Ke=ae(Qe,[["render",Ze]]),$=40,Xe=200,M="ChatList";let se;const Ye={mixins:[We(M),qe(),ge(),oe(T),ye("tetATetInChatList"),ve()],props:["embedded"],data(){return{markInstance:null,routeName:null,startingFromItemIdTop:null,startingFromItemIdBottom:null,isLoading:!1,initialized:!1}},computed:{...ne(re),tetAtetParticipants(){return this.getTetATetParticipantIds(this.items)}},methods:{hasLength:b,getMaxItemsLength(){return 240},getReduceToLength(){return 80},reduceBottom(){console.log("reduceBottom"),this.items=this.items.slice(0,this.getReduceToLength()),this.startingFromItemIdBottom=this.findBottomElementId()},reduceTop(){console.log("reduceTop"),this.items=this.items.slice(-this.getReduceToLength()),this.startingFromItemIdTop=this.findTopElementId()},enableHashInRoute(){return!1},convertLoadedFromStoreHash(e){return He+(e==null?void 0:e.id)},extractIdFromElementForStoring(e){return{pinned:e.getAttribute("data-pinned")==="true",lastUpdateDateTime:e.getAttribute("data-last-update-date-time"),id:parseInt(this.getIdFromRouteHash(e.id))}},findBottomElementId(){if(!this.items.length)return null;const e=this.items[this.items.length-1];return{pinned:e.pinned,lastUpdateDateTime:e.lastUpdateDateTime,id:e.id}},findTopElementId(){if(!this.items.length)return null;const e=this.items[0];return{pinned:e.pinned,lastUpdateDateTime:e.lastUpdateDateTime,id:e.id}},updateTopAndBottomIds(){this.startingFromItemIdTop=this.findTopElementId(),this.startingFromItemIdBottom=this.findBottomElementId()},saveScroll(e){const t=e?this.findTopElementId():this.findBottomElementId();t?this.preservedScroll=t.id:this.preservedScroll=null,console.log("Saved scroll",this.preservedScroll,"in ",M)},async scrollTop(){return te(),await this.$nextTick(()=>{this.scrollerDiv.scrollTop=0})},initialDirection(){return Ge},async onFirstLoad(e){await this.doScrollOnFirstLoad(),e===!0&&te()},async fetchItems(e,t,i){const o={includeStartingFrom:!!i,size:$,reverse:t,searchString:this.searchString};e&&(o.pinned=e.pinned,o.lastUpdateDateTime=e.lastUpdateDateTime,o.id=e.id);const a=(await d.get("/api/chat/search",{params:o},{signal:this.requestAbortController.signal})).data.items;return console.log("Get items in ",M,a,"direction",this.aDirection),a.forEach(f=>{this.transformItemOverride(f)}),a},async load(){if(!this.canDrawChats())return Promise.resolve();const{startingFromItemId:e,hasHash:t}=this.prepareHashesForRequest();this.chatStore.incrementProgressCount(),this.isLoading=!0;try{let i=await this.fetchItems(e,this.isTopDirection());return t&&(i=(await this.fetchItems(e,!this.isTopDirection(),!0)).reverse().concat(i)),this.isTopDirection()?Be(this.items,i):Ve(this.items,i),this.sort(this.items),this.updateTopAndBottomIds(),this.isFirstLoad||await this.clearRouteHash(),this.performMarking(),this.requestStatuses(),Promise.resolve(!0)}finally{this.chatStore.decrementProgressCount(),this.isLoading=!1}},afterScrollRestored(e){var t;(t=e==null?void 0:e.parentElement)==null||t.scrollBy({top:this.isTopDirection()?-10:10,behavior:"instant"})},bottomElementSelector(){return".chat-last-element"},topElementSelector(){return".chat-first-element"},getItemId(e){return Oe+e},scrollerSelector(){return".my-chat-scroller"},reset(){this.resetInfiniteScrollVars(),this.startingFromItemIdTop=null,this.startingFromItemIdBottom=null},async onSearchStringChangedDebounced(){await this.onSearchStringChanged()},async onSearchStringChanged(){this.isReady()&&await this.reloadItems()},onWsRestoredRefresh(){this.saveLastVisibleElement(),this.doOnFocus()},async onProfileSet(){await this.initializeHashVariablesAndReloadItems()},async doInitialize(){this.initialized||(this.initialized=!0,await this.onProfileSet())},onLoggedOut(){this.beforeUnload(),this.graphQlUserStatusUnsubscribe(),this.reset()},doUninitialize(){this.initialized&&(this.onLoggedOut(),this.initialized=!1)},canDrawChats(){return!!this.chatStore.currentUser},isSearchResult(e){return(e==null?void 0:e.isResultFromSearch)===!0},getItemClass(e){return e.pinned?"pinned-bold":"chat-normal"},getParticipantsClass(e){let t=["subtitle-thin"];return e.lastMessagePreview&&t.push("my-1"),t},getChatName(e){let t=e.name;return e.tetATet&&(Ne(e)&&(t="<s>"+t+"</s>"),t+=this.getUserName(e)),t},onShowContextMenu(e,t){this.$refs.contextMenuRef.onShowContextMenu(e,t)},openChat(e){this.chatStore.incrementProgressCount();const t=X(this.$route.query);xe(this.$route)&&delete t[k],this.$router.push({name:ee,params:{id:e.id},query:t}).finally(()=>{this.chatStore.decrementProgressCount()})},getLink(e){return Re+"/"+e.id},isActiveChat(e){return!this.isMobile()&&this.$route.name==ee||this.$route.name==Pe?this.$route.params.id==e.id:!1},printParticipants(e){if(b(e.shortInfo))return e.shortInfo;let t="";if(e.tetATet)t+=this.$vuetify.locale.t("$vuetify.tet_a_tet");else{const i=e.participants.map(o=>o.login);t+=i.join(", ")}return this.isSearchResult(e)&&(t=this.$vuetify.locale.t("$vuetify.this_is_search_result")+t),t},pinChat(e){d.put(`/api/chat/${e.id}/pin`,null,{params:{pin:!0},signal:this.requestAbortController.signal})},removedFromPinned(e){d.put(`/api/chat/${e.id}/pin`,null,{params:{pin:!1},signal:this.requestAbortController.signal})},editChat(e){n.emit(De,e)},deleteChat(e){n.emit(Y,{buttonName:this.$vuetify.locale.t("$vuetify.delete_btn"),title:this.$vuetify.locale.t("$vuetify.delete_chat_title",e.id),text:this.$vuetify.locale.t("$vuetify.delete_chat_text",e.name),actionFunction:t=>{t.loading=!0,d.delete(`/api/chat/${e.id}`).then(()=>{n.emit(J)}).finally(()=>{t.loading=!1})}})},leaveChat(e){n.emit(Y,{buttonName:this.$vuetify.locale.t("$vuetify.leave_btn"),title:this.$vuetify.locale.t("$vuetify.leave_chat_title",e.id),text:this.$vuetify.locale.t("$vuetify.leave_chat_text",e.name),actionFunction:t=>{t.loading=!0,d.put(`/api/chat/${e.id}/leave`,null).then(()=>{n.emit(J)}).finally(()=>{t.loading=!1})}})},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.chats"),Q(this.$vuetify.locale.t("$vuetify.chats"))},getTetATetParticipantIds(e){if(!e)return[];const i=X(e).filter(a=>a.tetATet).flatMap(a=>a.participantIds);return[...new Set(i)].sort()},getUserIdsSubscribeTo(){return this.tetAtetParticipants},isNormalTetAtTet(e){return e.participantIds.length==2},withMyselfTetATet(e){return e.participantIds.length==1},filterOutMe(e){return e.participants.filter(t=>{var i;return t.id!=((i=this.chatStore.currentUser)==null?void 0:i.id)})},onUserStatusChanged(e){e&&this.items.forEach(t=>{t.tetATet&&e.forEach(i=>{this.isNormalTetAtTet(t)?(i.online!==null&&this.filterOutMe(t).filter(o=>o.id==i.userId).length&&(t.online=i.online),i.isInVideo!==null&&this.filterOutMe(t).filter(o=>o.id==i.userId).length&&(t.isInVideo=i.isInVideo)):this.withMyselfTetATet(t)&&(i.online!==null&&t.participants.filter(o=>o.id==i.userId).length&&(t.online=i.online),i.isInVideo!==null&&t.participants.filter(o=>o.id==i.userId).length&&(t.isInVideo=i.isInVideo))})})},onChangeUnreadMessages(e){const t=e.chatId;let i=_(this.items,{id:t});i!=-1?(this.items[i].unreadMessages=e.unreadMessages,this.items[i].lastUpdateDateTime=e.lastUpdateDateTime,this.sort(this.items)):console.log("Not found to update unread messages",e)},sort(e){e.sort(Ue("-pinned","-lastUpdateDateTime","-id"))},performMarking(){this.$nextTick(()=>{b(this.searchString)&&(this.markInstance.unmark(),this.markInstance.mark(this.searchString))})},isScrolledToTop(){return this.scrollerDiv?Math.abs(this.scrollerDiv.scrollTop)<Xe:!1},addItem(e){console.log("Adding item",e),this.transformItemOverride(e),this.items.unshift(e),this.sort(this.items),this.reduceListAfterAdd(!1),this.updateTopAndBottomIds()},changeItem(e){console.log("Replacing item",e),K(this.items,e),this.sort(this.items),this.updateTopAndBottomIds()},removeItem(e){console.log("Removing item",e);const t=_(this.items,e);this.items.splice(t,1),this.updateTopAndBottomIds()},onNewChat(e){d.post("/api/chat/filter",{searchString:this.searchString,pageSize:$,chatId:e.id},{signal:this.requestAbortController.signal}).then(({data:t})=>{t.found?(this.addItem(e),this.performMarking()):console.log("Skipping adding",e)})},onEditChat(e){let t=_(this.items,e);if(t!==-1){const i=this.applyStateOverride(this.items[t],e);this.changeItem(i)}else this.addItem(e);this.performMarking()},transformItemOverride(e){this.transformItem(e),e.writingUsers=[],e.usersWritingSubtitleInfo=null},applyStateOverride(e,t){const i=this.applyState(e,t);return i.writingUsers=e.writingUsers,i.usersWritingSubtitleInfo=e.usersWritingSubtitleInfo,i},redrawItem(e){this.searchString==we?this.onEditChat(e):this.onDeleteChat(e)},onDeleteChat(e){this.hasItem(e)?this.removeItem(e):console.log("Item was not been removed",e)},hasItem(e){return _(this.items,e)!==-1},onCoChattedParticipantChanged(e){this.items.forEach(t=>{K(t.participants,e),t.tetATet&&(this.isNormalTetAtTet(t)?this.filterOutMe(t).map(i=>i.id).includes(e.id)&&this.mapParticipant(e,t):this.withMyselfTetATet(t)&&t.participants.map(i=>i.id).includes(e.id)&&this.mapParticipant(e,t))})},mapParticipant(e,t){t.avatar=e.avatar,t.name=e.login,t.shortInfo=e.shortInfo,t.loginColor=e.loginColor,t.additionalData=e.additionalData},onVideoCallChanged(e){this.items.forEach(t=>{t.id==e.chatId&&(t.videoChatUsersCount=e.usersCount)})},onVideoScreenShareChanged(e){this.items.forEach(t=>{t.id==e.chatId&&(t.hasScreenShares=e.hasScreenShares)})},getStyle(e){let t={};return e.tetATet&&(t=Le(e)),this.isSearchResult(e)&&(t.color="gray"),t},requestStatuses(){this.$nextTick(()=>{const t=this.tetAtetParticipants.join(",");this.triggerUsesStatusesEvents(t,this.requestAbortController.signal)})},onFocus(){if(this.chatStore.currentUser&&this.items&&(this.requestStatuses(),this.isScrolledToTop())){const e=this.items.slice(0,$);d.post("/api/chat/fresh",e,{params:{size:$,searchString:this.searchString},signal:this.requestAbortController.signal}).then(t=>{t.data.ok?console.log("No need to update chats"):(console.log("Need to update chats"),this.reloadItems())})}},hasItems(){var e;return!!((e=this.items)!=null&&e.length)},markAsRead(e){d.put(`/api/chat/${e.id}/read`,null,{signal:this.requestAbortController.signal})},markAsReadAll(e){d.put("/api/chat/read",null,{signal:this.requestAbortController.signal})},async doDefaultScroll(){await this.scrollTop()},getPositionFromStore(){return ke()},conditionToSaveLastVisible(){return!this.isScrolledToTop()},itemSelector(){return".chat-item-root"},setPositionToStore(e){Me(e)},beforeUnload(){this.saveLastVisibleElement()},isAppropriateHash(e){return Ee(e)},onUserTyping(e){const t=this.items.find(i=>i.id===e.chatId);t&&(Ae(t.writingUsers,e),t.usersWritingSubtitleInfo=Z(t.writingUsers,this.$vuetify))},shouldShowThirdLine(e){return!!e.lastMessagePreview||!!e.usersWritingSubtitleInfo},thirdLine(e){if(e.usersWritingSubtitleInfo)return e.usersWritingSubtitleInfo;if(e.lastMessagePreview)return e.lastMessagePreview}},components:{MessageItemContextMenu:Ke,ChatListContextMenu:pe},created(){this.routeName=this.$route.name,this.onSearchStringChangedDebounced=$e(this.onSearchStringChangedDebounced,700,{leading:!1,trailing:!0})},watch:{"$vuetify.locale.current":{handler:function(e,t){this.routeName==E&&this.setTopTitle()}},tetAtetParticipants:function(e,t){t.length!==0&&e.length===0?this.graphQlUserStatusUnsubscribe():_e(t,e)||this.graphQlUserStatusSubscribe()}},async mounted(){this.markInstance=new be("div#chat-list-items .chat-name"),addEventListener("beforeunload",this.beforeUnload),n.on(R+"."+T,this.onSearchStringChangedDebounced),n.on(x,this.doInitialize),n.on(N,this.doUninitialize),n.on(O,this.onChangeUnreadMessages),n.on(B,this.onNewChat),n.on(V,this.onEditChat),n.on(H,this.redrawItem),n.on(F,this.onDeleteChat),n.on(z,this.onCoChattedParticipantChanged),n.on(W,this.onWsRestoredRefresh),n.on(q,this.onVideoCallChanged),n.on(G,this.onVideoScreenShareChanged),n.on(j,this.onUserTyping),this.routeName==E&&(this.setTopTitle(),this.chatStore.isShowSearch=!0,this.chatStore.searchType=T),this.canDrawChats()&&await this.doInitialize(),se=setInterval(()=>{for(const e of this.items)e.writingUsers=Te(e.writingUsers),e.writingUsers.length==0?e.usersWritingSubtitleInfo=null:e.usersWritingSubtitleInfo=Z(e.writingUsers,this.$vuetify)},500),this.installOnFocus()},beforeUnmount(){this.saveLastVisibleElement(),removeEventListener("beforeunload",this.beforeUnload),this.uninstallOnFocus(),this.doUninitialize(),this.uninstallScroller(),n.off(R+"."+T,this.onSearchStringChangedDebounced),n.off(x,this.doInitialize),n.off(N,this.doUninitialize),n.off(O,this.onChangeUnreadMessages),n.off(B,this.onNewChat),n.off(V,this.onEditChat),n.off(H,this.redrawItem),n.off(F,this.onDeleteChat),n.off(z,this.onCoChattedParticipantChanged),n.off(W,this.onWsRestoredRefresh),n.off(q,this.onVideoCallChanged),n.off(G,this.onVideoScreenShareChanged),n.off(j,this.onUserTyping),clearInterval(se),this.routeName==E&&(Q(null),this.chatStore.title=null,this.chatStore.isShowSearch=!1)}},Je={class:"item-avatar"},et=["src"],tt=["innerHTML"];function it(e,t,i,o,y,a){const f=Se("ChatListContextMenu");return l(),h(Ie,{style:A(e.heightWithoutAppBar),fluid:"",class:"ma-0 pa-0"},{default:r(()=>[c(he,{id:"chat-list-items",class:"my-chat-scroller",onScrollPassive:e.onScroll},{default:r(()=>[t[5]||(t[5]=C("div",{class:"chat-first-element",style:{"min-height":"1px",background:"white"}},null,-1)),(l(!0),L(w,null,le(e.items,(s,at)=>(l(),h(ce,{key:s.id,id:a.getItemId(s.id),"data-pinned":s.pinned,"data-last-update-date-time":s.lastUpdateDateTime,class:"list-item-prepend-spacer pb-2 chat-item-root",onContextmenu:g(p=>a.onShowContextMenu(p,s),["stop"]),onClick:g(p=>a.openChat(s),["prevent"]),href:a.getLink(s),active:a.isActiveChat(s),color:"primary"},Fe({default:r(()=>[c(ze,null,{default:r(()=>[C("span",{class:U(["chat-name",a.getItemClass(s)]),style:A(a.getStyle(s)),innerHTML:a.getChatName(s)},null,14,tt),s.unreadMessages?(l(),h(S,{key:0,color:"primary",inline:"",content:s.unreadMessages,class:"mt-0",title:e.$vuetify.locale.t("$vuetify.unread_messages")},null,8,["content","title"])):u("",!0),s.videoChatUsersCount?(l(),h(S,{key:1,color:"success",icon:"mdi-phone",inline:"",class:"mt-0",title:e.$vuetify.locale.t("$vuetify.call_in_process")},null,8,["title"])):u("",!0),s.hasScreenShares?(l(),h(S,{key:2,color:"primary",icon:"mdi-monitor-share",inline:"",class:"mt-0",title:e.$vuetify.locale.t("$vuetify.screen_share_in_process")},null,8,["title"])):u("",!0),s.blog?(l(),h(S,{key:3,color:"grey",icon:"mdi-postage-stamp",inline:"",class:"mt-0",title:e.$vuetify.locale.t("$vuetify.blog")},null,8,["title"])):u("",!0)]),_:2},1024),c(ie,{style:A(a.isSearchResult(s)?{color:"gray"}:{}),class:U(a.getParticipantsClass(s)),innerHTML:a.printParticipants(s)},null,8,["style","class","innerHTML"]),a.shouldShowThirdLine(s)?(l(),h(ie,{key:0,class:"subtitle-thin my-1",innerHTML:a.thirdLine(s)},null,8,["innerHTML"])):u("",!0)]),_:2},[a.hasLength(s.avatar)?{name:"prepend",fn:r(()=>[c(S,{color:e.getUserBadgeColor(s),dot:"",location:"right bottom",overlap:"",bordered:"","model-value":s.online},{default:r(()=>[C("span",Je,[C("img",{src:s.avatar},null,8,et)])]),_:2},1032,["color","model-value"])]),key:"0"}:void 0,!e.isMobile()&&!i.embedded?{name:"append",fn:r(()=>[c(je,null,{default:r(()=>[s.isResultFromSearch?u("",!0):(l(),L(w,{key:0},[s.pinned?(l(),h(I,{key:0,variant:"flat",icon:"",onClick:g(p=>a.removedFromPinned(s),["stop","prevent"]),title:e.$vuetify.locale.t("$vuetify.remove_from_pinned")},{default:r(()=>[c(v,{size:"large"},{default:r(()=>t[0]||(t[0]=[m("mdi-pin-off-outline")])),_:1})]),_:2},1032,["onClick","title"])):(l(),h(I,{key:1,variant:"flat",icon:"",onClick:g(p=>a.pinChat(s),["stop","prevent"]),title:e.$vuetify.locale.t("$vuetify.pin_chat")},{default:r(()=>[c(v,{size:"large"},{default:r(()=>t[1]||(t[1]=[m("mdi-pin")])),_:1})]),_:2},1032,["onClick","title"]))],64)),s.canEdit?(l(),h(I,{key:1,variant:"flat",icon:"",onClick:g(p=>a.editChat(s),["stop","prevent"]),title:e.$vuetify.locale.t("$vuetify.edit_chat")},{default:r(()=>[c(v,{color:"primary",size:"large"},{default:r(()=>t[2]||(t[2]=[m("mdi-lead-pencil")])),_:1})]),_:2},1032,["onClick","title"])):u("",!0),s.canDelete?(l(),h(I,{key:2,variant:"flat",icon:"",onClick:g(p=>a.deleteChat(s),["stop","prevent"]),title:e.$vuetify.locale.t("$vuetify.delete_chat")},{default:r(()=>[c(v,{color:"red",size:"large"},{default:r(()=>t[3]||(t[3]=[m("mdi-delete")])),_:1})]),_:2},1032,["onClick","title"])):u("",!0),s.canLeave?(l(),h(I,{key:3,variant:"flat",icon:"",onClick:g(p=>a.leaveChat(s),["stop","prevent"]),title:e.$vuetify.locale.t("$vuetify.leave_chat")},{default:r(()=>[c(v,{size:"large"},{default:r(()=>t[4]||(t[4]=[m("mdi-exit-run")])),_:1})]),_:2},1032,["onClick","title"])):u("",!0)]),_:2},1024)]),key:"1"}:void 0]),1032,["id","data-pinned","data-last-update-date-time","onContextmenu","onClick","href","active"]))),128)),e.items.length==0&&!y.isLoading?(l(),h(Ce,{key:0,class:"mx-2"},{default:r(()=>[m(D(e.$vuetify.locale.t("$vuetify.chats_not_found")),1)]),_:1})):u("",!0),t[6]||(t[6]=C("div",{class:"chat-last-element",style:{"min-height":"1px",background:"white"}},null,-1))]),_:1},8,["onScrollPassive"]),c(f,{ref:"contextMenuRef",onEditChat:this.editChat,onDeleteChat:this.deleteChat,onLeaveChat:this.leaveChat,onPinChat:this.pinChat,onRemovedFromPinned:this.removedFromPinned,onMarkAsRead:a.markAsRead,onMarkAsReadAll:a.markAsReadAll},null,8,["onEditChat","onDeleteChat","onLeaveChat","onPinChat","onRemovedFromPinned","onMarkAsRead","onMarkAsReadAll"])]),_:1},8,["style"])}const st=ae(Ye,[["render",it],["__scopeId","data-v-c7f52029"]]),rt=Object.freeze(Object.defineProperty({__proto__:null,default:st},Symbol.toStringTag,{value:"Module"}));export{st as C,Ke as M,rt as a};
