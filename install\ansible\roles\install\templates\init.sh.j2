#!/bin/bash

set -e

HOST=$1
PORT=$2

echo "Got $HOST $PORT"

# https://stackoverflow.com/a/********
echo "Disabling threshold"
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_cluster/settings" -H 'Content-Type: application/json' -d'
{
  "persistent": {
    "cluster": {
      "routing": {
        "allocation.disk.threshold_enabled": false
      }
    }
  }
}'
echo "Disabling finished"

echo "Configuring task interval"
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_cluster/settings?pretty=true" -H 'Content-Type: application/json' -d'
{
  "persistent" : {
    "plugins.index_state_management.job_interval": {{ opensearch_scheduler_task_interval_minutes }}
  }
}
'
echo "Configuring task interval finished"


echo "Creating log index template"
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_index_template/log_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": [
    "log-*"
  ],
  "template": {
    "aliases": {
      "log": {}
    },
    "settings": {
      "number_of_shards": 1,
      "number_of_replicas": 0
    }
  }
}
'
echo "Creating log index template finished"

echo "Creating Jaeger span index template"
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_index_template/jaeger_span_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": [
    "jaeger-main-jaeger-span-*"
  ],
  "template": {
    "aliases": {
      "span": {}
    },
    "settings": {
      "number_of_shards": 1,
      "number_of_replicas": 0
    },
    "mappings": {
      "properties": {
        "tags": {
          "dynamic": false,
          "type": "nested",
          "properties": {
            "value": {
              "ignore_above": 256,
              "type": "keyword"
            },
            "key": {
              "ignore_above": 256,
              "type": "keyword"
            }
          }
        }
      }
    }
  }
}
'
echo "Creating Jaeger span index template finished"

echo "Creating Jaeger service index template"
# mapping is to fix getting services (https://github.com/jaegertracing/jaeger/issues/2718#issuecomment-758037851)
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_index_template/jaeger_service_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": [
    "jaeger-main-jaeger-service-*"
  ],
  "template": {
    "aliases": {
      "service": {}
    },
    "settings": {
      "number_of_shards": 1,
      "number_of_replicas": 0
    },
    "mappings": {
      "properties": {
        "serviceName": {
          "type": "keyword"
        }
      }
    }
  }
}
'
echo "Creating Jaeger service index template finished"


echo "Creating log cleaning policy"
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_plugins/_ism/policies/delete_old_log_indexes_policy?pretty" -H 'Content-Type: application/json' -d'
{
  "policy": {
    "description": "delete old log indexes",
    "default_state": "hot",
    "schema_version": 1,
    "states": [
      {
        "name": "hot",
        "transitions": [
          {
            "state_name": "delete",
            "conditions": {
              "min_index_age": "{{ opensearch_log_retention }}"
            }
          }
        ]
      },
      {
        "name": "delete",
        "actions": [
          {
            "delete": {}
          }
        ]
      }
    ],
    "ism_template": {
      "index_patterns": ["log-*"],
      "priority": 100
    }
  }
}
'
echo "Creating log cleaning policy finished"

echo "Creating Jaeger span cleaning policy"
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_plugins/_ism/policies/delete_old_jaeger_span_indexes_policy?pretty" -H 'Content-Type: application/json' -d'
{
  "policy": {
    "description": "delete old Jaeger span indexes",
    "default_state": "hot",
    "schema_version": 1,
    "states": [
      {
        "name": "hot",
        "transitions": [
          {
            "state_name": "delete",
            "conditions": {
              "min_index_age": "{{ opensearch_span_retention }}"
            }
          }
        ]
      },
      {
        "name": "delete",
        "actions": [
          {
            "delete": {}
          }
        ]
      }
    ],
    "ism_template": {
      "index_patterns": ["jaeger-main-jaeger-span-*"],
      "priority": 100
    }
  }
}
'
echo "Creating Jaeger span cleaning policy finished"



echo "Creating Jaeger service cleaning policy"
curl --fail-with-body -i -Ss -X PUT "http://$HOST:$PORT/_plugins/_ism/policies/delete_old_jaeger_service_indexes_policy?pretty" -H 'Content-Type: application/json' -d'
{
  "policy": {
    "description": "delete old Jaeger service indexes",
    "default_state": "hot",
    "schema_version": 1,
    "states": [
      {
        "name": "hot",
        "transitions": [
          {
            "state_name": "delete",
            "conditions": {
              "min_index_age": "{{ opensearch_service_retention }}"
            }
          }
        ]
      },
      {
        "name": "delete",
        "actions": [
          {
            "delete": {}
          }
        ]
      }
    ],
    "ism_template": {
      "index_patterns": ["jaeger-main-jaeger-service-*"],
      "priority": 100
    }
  }
}
'
echo "Creating Jaeger service cleaning policy finished"
