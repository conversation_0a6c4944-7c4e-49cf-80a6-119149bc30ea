import{_ as S,g as p,m as c,h as C,c as B,a as w,w as a,b as r,V,d as b,e as i,f as T,t as n,n as d,i as k,j as u,k as o,l as f,o as A,p as $,s as h,q as g,r as v,u as y,S as z,v as m,x as I,y as U,z as x,O as F,A as M,B as N}from"./appMain-DAGYo8tP.js";const j={mixins:[C()],computed:{...c(M)},methods:{getLoginColoredStyle:p,createChat(){x.emit(F,null)},findUser(){this.$router.push({name:U})},getUser(){return I},availableForSearchChats(){this.$router.push({name:y,hash:null,query:{[z]:m}})},getAvailableForSearchChats(){return v+"?"+z+"="+m},chats(){this.$router.push({name:y})},getChats(){return v},goBlog(){window.location.href=g},getBlog(){return g},getBtnSize(){if(this.isMobile())return"large"},getIconSize(){if(this.isMobile())return"large"},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.welcome"),h(this.$vuetify.locale.t("$vuetify.welcome"))}},watch:{"$vuetify.locale.current":{handler:function(l,e){this.setTopTitle()}}},mounted(){this.setTopTitle()},beforeUnmount(){h(null),this.chatStore.title=null}};function E(l,e,D,H,O,t){return l.chatStore.currentUser?(N(),B($,{key:0,"fill-height":"",fluid:"",style:d(l.heightWithoutAppBar)},{default:a(()=>[r(A,{align:"center",justify:"center",style:{height:"100%"}},{default:a(()=>[r(V,null,{default:a(()=>[r(b,{class:"d-flex justify-center with-space"},{default:a(()=>{var s;return[i(n(l.$vuetify.locale.t("$vuetify.welcome_participant")),1),T("span",{style:d(t.getLoginColoredStyle(l.chatStore.currentUser))},n((s=l.chatStore.currentUser)==null?void 0:s.login),5),e[5]||(e[5]=i("!"))]}),_:1}),r(k,{class:"d-flex justify-space-around flex-wrap flex-row"},{default:a(()=>[r(u,{size:t.getBtnSize(),onClick:e[0]||(e[0]=o(s=>t.findUser(),["prevent"])),text:"",variant:"outlined",href:t.getUser()},{prepend:a(()=>[r(f,{size:t.getIconSize()},{default:a(()=>e[6]||(e[6]=[i("mdi-account-group")])),_:1},8,["size"])]),default:a(()=>[i(n(l.$vuetify.locale.t("$vuetify.users")),1)]),_:1},8,["size","href"]),r(u,{size:t.getBtnSize(),color:"primary",onClick:e[1]||(e[1]=o(s=>t.createChat(),["prevent"])),text:"",variant:"outlined"},{prepend:a(()=>[r(f,{size:t.getIconSize()},{default:a(()=>e[7]||(e[7]=[i("mdi-plus")])),_:1},8,["size"])]),default:a(()=>[i(n(l.$vuetify.locale.t("$vuetify.new_chat")),1)]),_:1},8,["size"]),r(u,{size:t.getBtnSize(),onClick:e[2]||(e[2]=o(s=>t.chats(),["prevent"])),text:"",variant:"outlined",href:t.getChats()},{prepend:a(()=>[r(f,{size:t.getIconSize()},{default:a(()=>e[8]||(e[8]=[i("mdi-forum")])),_:1},8,["size"])]),default:a(()=>[i(n(l.$vuetify.locale.t("$vuetify.chats")),1)]),_:1},8,["size","href"]),r(u,{size:t.getBtnSize(),onClick:e[3]||(e[3]=o(s=>t.availableForSearchChats(),["prevent"])),text:"",variant:"outlined",href:t.getAvailableForSearchChats()},{prepend:a(()=>[r(f,{size:t.getIconSize()},{default:a(()=>e[9]||(e[9]=[i("mdi-forum")])),_:1},8,["size"])]),default:a(()=>[i(n(l.$vuetify.locale.t("$vuetify.public_chats")),1)]),_:1},8,["size","href"]),r(u,{size:t.getBtnSize(),onClick:e[4]||(e[4]=o(s=>t.goBlog(),["prevent"])),text:"",variant:"outlined",href:t.getBlog()},{prepend:a(()=>[r(f,{size:t.getIconSize()},{default:a(()=>e[10]||(e[10]=[i("mdi-postage-stamp")])),_:1},8,["size"])]),default:a(()=>[i(n(l.$vuetify.locale.t("$vuetify.blogs")),1)]),_:1},8,["size","href"])]),_:1})]),_:1})]),_:1})]),_:1},8,["style"])):w("",!0)}const q=S(j,[["render",E]]);export{q as default};
