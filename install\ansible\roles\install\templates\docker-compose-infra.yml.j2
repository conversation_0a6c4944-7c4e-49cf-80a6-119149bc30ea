version: '3.9'

services:
  traefik:
    image: traefik:v3.4.1
    hostname: traefik
    environment:
      - OTEL_PROPAGATORS=jaeger
    # The Static Configuration
    command: --configFile=/etc/traefik/traefik.yml
    ports:
    - target: 80
      published: 80
      protocol: tcp
      mode: host
    - target: 443
      published: 443
      protocol: tcp
      mode: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./traefik:/etc/traefik
    networks:
      - backend
    logging:
      driver: "fluentd"
      options:
        fluentd-address: unix://{{ dir_prefix }}/fluent-bit/var/run/socket
        fluentd-async: "true" # in order not to fail in case traefik started faster than fluent-bit
        tag: videochat.infra.traefik
        fluentd-sub-second-precision: "true"

  postgresql:
    image: postgres:17.0-alpine3.20
    hostname: postgresql
    volumes:
      - ./postgresql/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
      - /mnt/chat-postgresql:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql

  postgresql-citus-coordinator-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-coordinator-1
    volumes:
      - ./postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - ./postgresql-citus/coordinator/docker-entrypoint-initdb.d/003-coordinator-init.sh:/docker-entrypoint-initdb.d/003-coordinator-init.sh:z
      - ./scripts/wait-for-it.sh:/sbin/wait-for-it.sh:z
      - /mnt/chat-citus-coordinator-1:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-coordinator-1
  postgresql-citus-worker-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-1
    volumes:
      - ./postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - /mnt/chat-citus-worker-1:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-worker-1
  postgresql-citus-worker-2:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-2
    volumes:
      - ./postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - /mnt/chat-citus-worker-2:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-postgresql-citus-worker-2

  redis:
    image: valkey/valkey:8.1.0-alpine3.21
    hostname: redis
    volumes:
      - redis_data_dir:/data
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-redis

  minio:
    image: bitnami/minio:2024.1.29-debian-11-r0
    hostname: minio
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.services.minio-service.loadbalancer.server.port=9000"
        - "traefik.http.routers.minio-router.rule=PathPrefix(`/api/s3`) && !PathPrefix(`/api/s3/minio`) && Host(`{{ domain }}`)"
        - "traefik.http.routers.minio-router.middlewares=minio-strip-prefix-middleware,retry-middleware@file,minio-fix-host-middleware"
        - "traefik.http.routers.minio-router.entrypoints=https"
        - "traefik.http.routers.minio-router.tls=true"
        - "traefik.http.routers.minio-router.tls.certresolver=myresolver"
        - "traefik.http.routers.minio-router.service=minio-service"
        - "traefik.http.middlewares.minio-strip-prefix-middleware.stripprefix.prefixes=/api/s3"
        - "traefik.http.middlewares.minio-fix-host-middleware.headers.customrequestheaders.Host=minio:9000"

        - "traefik.http.services.minio-ui-service.loadbalancer.server.port=9001"
        - "traefik.http.routers.minio-ui-router.rule=PathPrefix(`/minio`) && Host(`{{ domain }}`)"
        - "traefik.http.routers.minio-ui-router.entrypoints=https"
        - "traefik.http.routers.minio-ui-router.tls=true"
        - "traefik.http.routers.minio-ui-router.tls.certresolver=myresolver"
        - "traefik.http.routers.minio-ui-router.middlewares=auth-middleware@file,retry-middleware@file,minio-ui-stripprefix-middleware"
        - "traefik.http.routers.minio-ui-router.service=minio-ui-service"
        - "traefik.http.middlewares.minio-ui-stripprefix-middleware.stripprefix.prefixes=/minio"

    environment:
      - MINIO_ROOT_USER={{ minio_user }}
      - MINIO_ROOT_PASSWORD={{ minio_password }}
      - MINIO_NOTIFY_AMQP_ENABLE_primary=on
      - MINIO_NOTIFY_AMQP_URL_primary=amqp://videoChat:videoChatPazZw0rd@rabbitmq:5672
      - MINIO_NOTIFY_AMQP_EXCHANGE_primary=minio-events
      - MINIO_NOTIFY_AMQP_EXCHANGE_TYPE_primary=direct
      - MINIO_NOTIFY_AMQP_MANDATORY_primary=off
      - MINIO_NOTIFY_AMQP_DURABLE_primary=on
      - MINIO_NOTIFY_AMQP_NO_WAIT_primary=off
      - MINIO_NOTIFY_AMQP_AUTO_DELETED_primary=off
      - MINIO_NOTIFY_AMQP_DELIVERY_MODE_primary=2
      - BITNAMI_DEBUG=true
      - MINIO_PROMETHEUS_AUTH_TYPE=public
      - MINIO_BROWSER_REDIRECT_URL=https://{{ domain }}/minio

    volumes:
      - /mnt/chat-minio/data:/bitnami/minio/data:z
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-minio

  rabbitmq:
    image: rabbitmq:4.0.9-management-alpine
    hostname: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=videoChat
      - RABBITMQ_DEFAULT_PASS=videoChatPazZw0rd
    volumes:
      - rabbitmq_data_dir:/var/lib/rabbitmq/mnesia
      - ./rabbitmq/additional.conf:/etc/rabbitmq/conf.d/additional.conf:ro,z
    networks:
      - backend
    deploy:
      labels:
        - "traefik.enable=true"

        - "traefik.http.services.rabbitmq-ui-service.loadbalancer.server.port=15672" # ui port
        - "traefik.http.routers.rabbitmq-ui-router.rule=PathPrefix(`/rabbitmq`) && Host(`{{ domain }}`)"
        - "traefik.http.routers.rabbitmq-ui-router.entrypoints=https"
        - "traefik.http.routers.rabbitmq-ui-router.tls=true"
        - "traefik.http.routers.rabbitmq-ui-router.tls.certresolver=myresolver"
        - "traefik.http.routers.rabbitmq-ui-router.middlewares=auth-middleware@file,retry-middleware@file"

    logging:
      driver: "journald"
      options:
        tag: chat-rabbitmq

  livekit:
    image: livekit/livekit-server:v1.9.0
    command: --config /etc/livekit.yaml
    volumes:
      - ./livekit/livekit.yaml:/etc/livekit.yaml
    networks:
      - backend
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.services.livekit-service.loadbalancer.server.port=7880"
        - "traefik.http.routers.livekit-router.rule=PathPrefix(`/api/livekit`) && Host(`{{ domain }}`)"
        - "traefik.http.routers.livekit-router.entrypoints=https"
        - "traefik.http.routers.livekit-router.tls=true"
        - "traefik.http.routers.livekit-router.tls.certresolver=myresolver"
        - "traefik.http.middlewares.livekit-stripprefix-middleware.stripprefix.prefixes=/api/livekit"
        - "traefik.http.routers.livekit-router.middlewares=auth-middleware@file,livekit-stripprefix-middleware,retry-middleware@file"
    ports:
      # turn udp port
#      - target: 3478
#        published: 3478
#        protocol: udp
#        mode: host

      # turn tls port
#      - target: 5349
#        published: 5349
#        protocol: tcp
#        mode: host

      # webrtc udp port
      - target: 7882
        published: 7882
        protocol: udp
        mode: host

      # webrtc tcp port
      - target: 7881
        published: 7881
        protocol: tcp
        mode: host

    logging:
      driver: "journald"
      options:
        tag: chat-livekit

  egress:
    image: livekit/egress:v1.9.0
    networks:
      - backend
    environment:
      - EGRESS_CONFIG_FILE=/etc/egress/config.yaml
    volumes:
      - ./egress/config.yaml:/etc/egress/config.yaml
      - egress_tmp:/home/<USER>/tmp:z
    cap_add:
      - SYS_ADMIN
    logging:
      driver: "journald"
      options:
        tag: chat-egress

  jaeger:
    # https://www.jaegertracing.io/docs/2.0/getting-started/
    image: jaegertracing/jaeger:2.7.0
    hostname: jaeger
    networks:
      - backend
    deploy:
      labels:
        - "traefik.enable=true"

        - "traefik.http.services.jaeger-ui-service.loadbalancer.server.port=16686" # ui port
        - "traefik.http.routers.jaeger-ui-router.rule=PathPrefix(`/jaeger`) && Host(`{{ domain }}`)"
        - "traefik.http.routers.jaeger-ui-router.entrypoints=https"
        - "traefik.http.routers.jaeger-ui-router.tls=true"
        - "traefik.http.routers.jaeger-ui-router.tls.certresolver=myresolver"
        - "traefik.http.routers.jaeger-ui-router.middlewares=auth-middleware@file,retry-middleware@file"

    # https://www.jaegertracing.io/docs/2.7/deployment/configuration/
    # https://github.com/jaegertracing/jaeger/blob/v2.7.0/cmd/jaeger/internal/all-in-one.yaml
    # https://github.com/jaegertracing/jaeger/blob/v2.7.0/cmd/jaeger/config-opensearch.yaml
    volumes:
      - ./jaeger/config.yaml:/jaeger/config.yaml
    environment:
      - JAEGER_LISTEN_HOST=0.0.0.0
    command: [
      "--config",
      "/jaeger/config.yaml",
    ]
    logging:
      driver: "journald"
      options:
        tag: chat-jaeger

  pgadmin:
    image: dcagatay/pwless-pgadmin4:8.13
    hostname: pgadmin
    networks:
      - backend
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.services.pgadmin-ui-service.loadbalancer.server.port=8080" # ui port
        - "traefik.http.routers.pgadmin-ui-router.rule=PathPrefix(`/postgresql`) && Host(`{{ domain }}`)"
        - "traefik.http.routers.pgadmin-ui-router.entrypoints=https"
        - "traefik.http.routers.pgadmin-ui-router.tls=true"
        - "traefik.http.routers.pgadmin-ui-router.tls.certresolver=myresolver"
        - "traefik.http.routers.pgadmin-ui-router.middlewares=auth-middleware@file,retry-middleware@file"
    environment:
      - POSTGRES_USER_1=postgres
      - POSTGRES_PASSWORD_1=postgresqlPassword
      - POSTGRES_HOST_1=postgresql
      - POSTGRES_PORT_1=5432
      - POSTGRES_USER_2=postgres
      - POSTGRES_PASSWORD_2=postgresqlPassword
      - POSTGRES_HOST_2=postgresql-citus-coordinator-1
      - POSTGRES_PORT_2=5432
      - PGADMIN_DISABLE_POSTFIX=1
      - PGADMIN_LISTEN_ADDRESS=0.0.0.0
      - PGADMIN_LISTEN_PORT=8080
      - SCRIPT_NAME=/postgresql
    logging:
      driver: "journald"
      options:
        tag: chat-pgadmin

  fluentbit:
    image: fluent/fluent-bit:4.0.4
    hostname: fluentbit
    volumes:
      - ./fluent-bit/etc:/fluent-bit/etc:z
      - ./fluent-bit/var/run:/var/run/fluent-bit:z
    networks:
      - backend
    logging:
      driver: "journald"
      options:
        tag: chat-fluentbit

  opensearch:
    image: opensearchproject/opensearch:2.18.0
    hostname: opensearch
    environment:
      - node.name=opensearch
      - discovery.type=single-node
      - bootstrap.memory_lock=true # along with the memlock settings below, disables swapping
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m" # minimum and maximum Java heap size, recommend setting both to 50% of system RAM
      # - OPENSEARCH_INITIAL_ADMIN_PASSWORD=yourStrongPassword123!
      # https://opensearch.org/docs/latest/install-and-configure/install-opensearch/docker/
      - DISABLE_SECURITY_PLUGIN=true
      - DISABLE_INSTALL_DEMO_CONFIG=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536 # maximum number of open files for the OpenSearch user, set to at least 65536 on modern systems
        hard: 65536
    networks:
      - backend
    volumes:
      - opensearch_data_dir:/usr/share/opensearch/data
      - ./opensearch/docker-entrypoint-init.d.sh:/docker-entrypoint-init.d.sh:z
      - ./opensearch/docker-entrypoint-init.d:/docker-entrypoint-init.d:z
    logging:
      driver: "journald"
      options:
        tag: chat-opensearch
    entrypoint: ["/docker-entrypoint-init.d.sh"]

  dashboards:
    image: opensearchproject/opensearch-dashboards:2.18.0
    hostname: dashboards
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.services.opensearch-dashboards-ui-service.loadbalancer.server.port=5601" # ui port
        - "traefik.http.routers.opensearch-dashboards-ui-router.rule=PathPrefix(`/opensearch-dashboards`) && Host(`{{ domain }}`)"
        - "traefik.http.routers.opensearch-dashboards-ui-router.entrypoints=https"
        - "traefik.http.routers.opensearch-dashboards-ui-router.tls=true"
        - "traefik.http.routers.opensearch-dashboards-ui-router.tls.certresolver=myresolver"
        - "traefik.http.routers.opensearch-dashboards-ui-router.middlewares=auth-middleware@file,retry-middleware@file"
    networks:
      - backend
    environment:
      OPENSEARCH_HOSTS: '["http://opensearch:9200"]'
      # https://stackoverflow.com/a/74178195
      DISABLE_SECURITY_DASHBOARDS_PLUGIN: "true"
      SERVER_BASEPATH: "/opensearch-dashboards"
      SERVER_REWRITEBASEPATH: "true"
    logging:
      driver: "journald"
      options:
        tag: chat-opensearch-dashboards

volumes:
  redis_data_dir:
  rabbitmq_data_dir:
  egress_tmp:
  opensearch_data_dir:

networks:
  backend:
    driver: overlay
    attachable: true
