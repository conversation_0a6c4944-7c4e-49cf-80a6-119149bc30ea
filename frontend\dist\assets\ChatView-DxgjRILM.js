import{C as Hc,D as _e,E as Nt,F as Tt,G as ae,H as zo,I as bi,J as qo,K as Kc,n as Ws,L as $c,M as Jc,c as H,N as St,P as Qc,Q as Cn,B as F,R as Yc,_ as mt,T as Be,g as Xc,m as pt,U as Hs,W as pi,X as Ai,Y as Gt,Z as Zc,$ as ed,a0 as Go,a1 as Wo,a2 as se,a3 as td,a4 as Zn,A as ft,a as W,f as ge,k as Te,b as K,w as U,t as ve,a5 as Re,e as ie,l as ce,p as ur,a6 as be,a7 as Xt,a8 as hn,a9 as Pi,j as Ce,aa as Ho,ab as Ko,z as T,ac as es,ad as ts,ae as is,af as ns,ag as ss,ah as en,ai as tn,aj as rs,ak as nn,al as sn,am as os,an as as,ao as id,ap as fi,aq as cs,ar as nd,as as sd,at as hr,au as Ei,av as xi,aw as rd,ax as yi,ay as od,az as ad,aA as mr,aB as cd,aC as Ut,aD as dd,aE as ld,aF as ud,aG as hd,aH as pr,aI as fr,aJ as gr,aK as ds,aL as md,aM as pd,aN as vr,aO as fd,aP as gd,aQ as vd,aR as br,aS as bd,aT as yd,aU as Sd,aV as kd,aW as Cd,aX as Td,aY as Pd,aZ as Ed,a_ as $o,a$ as Di,b0 as Ii,b1 as Si,b2 as Jo,b3 as Qo,b4 as Yo,b5 as Pt,b6 as Et,b7 as Xo,b8 as Id,b9 as Rd,ba as wd,bb as _d,bc as Md,bd as Dd,be as ls,bf as Od,bg as Ad,bh as xd,bi as Ld,bj as Nd,bk as yr,bl as Sr,bm as kr,bn as Ud,bo as It,bp as Fd,bq as Vd,br as Bd,bs as jd,bt as zd,bu as qd,bv as Gd,bw as Cr,bx as Wd,by as Hd,bz as Tr,bA as Pr,bB as Kd,bC as $d,bD as Jd,bE as Zo,bF as Qd,bG as Yd,bH as Xd,bI as Zd,bJ as el,bK as tl,bL as il,bM as nl,bN as sl,bO as Er,bP as rl,bQ as Ir,bR as ol,bS as al,bT as cl,bU as dl,h as ll,bV as ul,bW as hl,bX as ml,bY as Tn,bZ as Pn,b_ as En,b$ as Rr,c0 as In,c1 as wr,c2 as _r,c3 as Mr,c4 as Rn,c5 as pl,c6 as fl,c7 as gl,c8 as Dr,c9 as vl,ca as bl,cb as yl,cc as Sl,s as Or,u as wn,cd as kl,ce as Cl,cf as Tl,cg as Pl,ch as El,ci as Il,cj as Rl,ck as wl,cl as _l,cm as Ml,S as Dl,cn as Ol}from"./appMain-DAGYo8tP.js";import{M as Al,C as xl}from"./ChatList-jm3A2LUd.js";import{i as Ll,h as Nl,d as Ul}from"./hashMixin-OoEehrvU.js";const ea={__name:"splitpanes",props:{horizontal:{type:Boolean},pushOtherPanes:{type:Boolean,default:!0},dblClickSplitter:{type:Boolean,default:!0},rtl:{type:Boolean,default:!1},firstSplitter:{type:Boolean}},emits:["ready","resize","resized","pane-click","pane-maximize","pane-add","pane-remove","splitter-click"],setup(t,{emit:e}){const i=e,n=t,s=Jc(),r=Tt([]),o=_e(()=>r.value.reduce((C,w)=>(C[~~w.id]=w)&&C,{})),a=_e(()=>r.value.length),c=Tt(null),d=Tt(!1),l=Tt({mouseDown:!1,dragging:!1,activeSplitter:null,cursorOffset:0}),u=Tt({splitter:null,timeoutId:null}),h=_e(()=>({[`splitpanes splitpanes--${n.horizontal?"horizontal":"vertical"}`]:!0,"splitpanes--dragging":l.value.dragging})),p=()=>{document.addEventListener("mousemove",y,{passive:!1}),document.addEventListener("mouseup",S),"ontouchstart"in window&&(document.addEventListener("touchmove",y,{passive:!1}),document.addEventListener("touchend",S))},g=()=>{document.removeEventListener("mousemove",y,{passive:!1}),document.removeEventListener("mouseup",S),"ontouchstart"in window&&(document.removeEventListener("touchmove",y,{passive:!1}),document.removeEventListener("touchend",S))},f=(C,w)=>{const L=C.target.closest(".splitpanes__splitter");if(L){const{left:R,top:N}=L.getBoundingClientRect(),{clientX:G,clientY:Z}="ontouchstart"in window&&C.touches?C.touches[0]:C;l.value.cursorOffset=n.horizontal?Z-N:G-R}p(),l.value.mouseDown=!0,l.value.activeSplitter=w},y=C=>{l.value.mouseDown&&(C.preventDefault(),l.value.dragging=!0,requestAnimationFrame(()=>{j(k(C)),i("resize",r.value.map(w=>({min:w.min,max:w.max,size:w.size})))}))},S=()=>{l.value.dragging&&i("resized",r.value.map(C=>({min:C.min,max:C.max,size:C.size}))),l.value.mouseDown=!1,setTimeout(()=>{l.value.dragging=!1,g()},100)},x=(C,w)=>{"ontouchstart"in window&&(C.preventDefault(),n.dblClickSplitter&&(u.value.splitter===w?(clearTimeout(u.value.timeoutId),u.value.timeoutId=null,M(C,w),u.value.splitter=null):(u.value.splitter=w,u.value.timeoutId=setTimeout(()=>u.value.splitter=null,500)))),l.value.dragging||i("splitter-click",r.value[w])},M=(C,w)=>{let L=0;r.value=r.value.map((R,N)=>(R.size=N===w?R.max:R.min,N!==w&&(L+=R.min),R)),r.value[w].size-=L,i("pane-maximize",r.value[w]),i("resized",r.value.map(R=>({min:R.min,max:R.max,size:R.size})))},b=(C,w)=>{i("pane-click",o.value[w])},k=C=>{const w=c.value.getBoundingClientRect(),{clientX:L,clientY:R}="ontouchstart"in window&&C.touches?C.touches[0]:C;return{x:L-(n.horizontal?0:l.value.cursorOffset)-w.left,y:R-(n.horizontal?l.value.cursorOffset:0)-w.top}},_=C=>{C=C[n.horizontal?"y":"x"];const w=c.value[n.horizontal?"clientHeight":"clientWidth"];return n.rtl&&!n.horizontal&&(C=w-C),C*100/w},j=C=>{const w=l.value.activeSplitter;let L={prevPanesSize:V(w),nextPanesSize:B(w),prevReachedMinPanes:0,nextReachedMinPanes:0};const R=0+(n.pushOtherPanes?0:L.prevPanesSize),N=100-(n.pushOtherPanes?0:L.nextPanesSize),G=Math.max(Math.min(_(C),N),R);let Z=[w,w+1],Ee=r.value[Z[0]]||null,Ue=r.value[Z[1]]||null;const dr=Ee.max<100&&G>=Ee.max+L.prevPanesSize,Wc=Ue.max<100&&G<=100-(Ue.max+B(w+1));if(dr||Wc){dr?(Ee.size=Ee.max,Ue.size=Math.max(100-Ee.max-L.prevPanesSize-L.nextPanesSize,0)):(Ee.size=Math.max(100-Ue.max-L.prevPanesSize-B(w+1),0),Ue.size=Ue.max);return}if(n.pushOtherPanes){const lr=z(L,G);if(!lr)return;({sums:L,panesToResize:Z}=lr),Ee=r.value[Z[0]]||null,Ue=r.value[Z[1]]||null}Ee!==null&&(Ee.size=Math.min(Math.max(G-L.prevPanesSize-L.prevReachedMinPanes,Ee.min),Ee.max)),Ue!==null&&(Ue.size=Math.min(Math.max(100-G-L.nextPanesSize-L.nextReachedMinPanes,Ue.min),Ue.max))},z=(C,w)=>{const L=l.value.activeSplitter,R=[L,L+1];return w<C.prevPanesSize+r.value[R[0]].min&&(R[0]=q(L).index,C.prevReachedMinPanes=0,R[0]<L&&r.value.forEach((N,G)=>{G>R[0]&&G<=L&&(N.size=N.min,C.prevReachedMinPanes+=N.min)}),C.prevPanesSize=V(R[0]),R[0]===void 0)?(C.prevReachedMinPanes=0,r.value[0].size=r.value[0].min,r.value.forEach((N,G)=>{G>0&&G<=L&&(N.size=N.min,C.prevReachedMinPanes+=N.min)}),r.value[R[1]].size=100-C.prevReachedMinPanes-r.value[0].min-C.prevPanesSize-C.nextPanesSize,null):w>100-C.nextPanesSize-r.value[R[1]].min&&(R[1]=X(L).index,C.nextReachedMinPanes=0,R[1]>L+1&&r.value.forEach((N,G)=>{G>L&&G<R[1]&&(N.size=N.min,C.nextReachedMinPanes+=N.min)}),C.nextPanesSize=B(R[1]-1),R[1]===void 0)?(C.nextReachedMinPanes=0,r.value.forEach((N,G)=>{G<a.value-1&&G>=L+1&&(N.size=N.min,C.nextReachedMinPanes+=N.min)}),r.value[R[0]].size=100-C.prevPanesSize-B(R[0]-1),null):{sums:C,panesToResize:R}},V=C=>r.value.reduce((w,L,R)=>w+(R<C?L.size:0),0),B=C=>r.value.reduce((w,L,R)=>w+(R>C+1?L.size:0),0),q=C=>[...r.value].reverse().find(w=>w.index<C&&w.size>w.min)||{},X=C=>r.value.find(w=>w.index>C+1&&w.size>w.min)||{},ye=()=>{var C;Array.from(((C=c.value)==null?void 0:C.children)||[]).forEach(w=>{const L=w.classList.contains("splitpanes__pane"),R=w.classList.contains("splitpanes__splitter");!L&&!R&&(w.remove(),console.warn("Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed."))})},Ne=(C,w,L=!1)=>{const R=C-1,N=document.createElement("div");N.classList.add("splitpanes__splitter"),L||(N.onmousedown=G=>f(G,R),typeof window<"u"&&"ontouchstart"in window&&(N.ontouchstart=G=>f(G,R)),N.onclick=G=>x(G,R+1)),n.dblClickSplitter&&(N.ondblclick=G=>M(G,R+1)),w.parentNode.insertBefore(N,w)},xt=C=>{C.onmousedown=void 0,C.onclick=void 0,C.ondblclick=void 0,C.remove()},bt=()=>{var C;const w=Array.from(((C=c.value)==null?void 0:C.children)||[]);w.forEach(R=>{R.className.includes("splitpanes__splitter")&&xt(R)});let L=0;w.forEach(R=>{R.className.includes("splitpanes__pane")&&(!L&&n.firstSplitter?Ne(L,R,!0):L&&Ne(L,R),L++)})},ni=({uid:C,...w})=>{const L=o.value[C];Object.entries(w).forEach(([R,N])=>L[R]=N)},si=C=>{var w;let L=-1;Array.from(((w=c.value)==null?void 0:w.children)||[]).some(R=>(R.className.includes("splitpanes__pane")&&L++,R.isSameNode(C.el))),r.value.splice(L,0,{...C,index:L}),r.value.forEach((R,N)=>R.index=N),d.value&&Cn(()=>{bt(),yt({addedPane:r.value[L]}),i("pane-add",{index:L,panes:r.value.map(R=>({min:R.min,max:R.max,size:R.size}))})})},ri=C=>{const w=r.value.findIndex(R=>R.id===C),L=r.value.splice(w,1)[0];r.value.forEach((R,N)=>R.index=N),Cn(()=>{bt(),yt({removedPane:{...L}}),i("pane-remove",{removed:L,panes:r.value.map(R=>({min:R.min,max:R.max,size:R.size}))})})},yt=(C={})=>{!C.addedPane&&!C.removedPane?ai():r.value.some(w=>w.givenSize!==null||w.min||w.max<100)?kn(C):oi(),d.value&&i("resized",r.value.map(w=>({min:w.min,max:w.max,size:w.size})))},oi=()=>{const C=100/a.value;let w=0;const L=[],R=[];r.value.forEach(N=>{N.size=Math.max(Math.min(C,N.max),N.min),w-=N.size,N.size>=N.max&&L.push(N.id),N.size<=N.min&&R.push(N.id)}),w>.1&&Lt(w,L,R)},ai=()=>{let C=100;const w=[],L=[];let R=0;r.value.forEach(G=>{C-=G.size,G.givenSize!==null&&R++,G.size>=G.max&&w.push(G.id),G.size<=G.min&&L.push(G.id)});let N=100;C>.1&&(r.value.forEach(G=>{G.givenSize===null&&(G.size=Math.max(Math.min(C/(a.value-R),G.max),G.min)),N-=G.size}),N>.1&&Lt(N,w,L))},kn=({addedPane:C,removedPane:w}={})=>{let L=100/a.value,R=0;const N=[],G=[];((C==null?void 0:C.givenSize)??null)!==null&&(L=(100-C.givenSize)/(a.value-1).value),r.value.forEach(Z=>{R-=Z.size,Z.size>=Z.max&&N.push(Z.id),Z.size<=Z.min&&G.push(Z.id)}),!(Math.abs(R)<.1)&&(r.value.forEach(Z=>{(C==null?void 0:C.givenSize)!==null&&(C==null?void 0:C.id)===Z.id||(Z.size=Math.max(Math.min(L,Z.max),Z.min)),R-=Z.size,Z.size>=Z.max&&N.push(Z.id),Z.size<=Z.min&&G.push(Z.id)}),R>.1&&Lt(R,N,G))},Lt=(C,w,L)=>{let R;C>0?R=C/(a.value-w.length):R=C/(a.value-L.length),r.value.forEach((N,G)=>{if(C>0&&!w.includes(N.id)){const Z=Math.max(Math.min(N.size+R,N.max),N.min),Ee=Z-N.size;C-=Ee,N.size=Z}else if(!L.includes(N.id)){const Z=Math.max(Math.min(N.size+R,N.max),N.min),Ee=Z-N.size;C-=Ee,N.size=Z}}),Math.abs(C)>.1&&Cn(()=>{d.value&&console.warn("Splitpanes: Could not resize panes correctly due to their constraints.")})};bi(()=>n.firstSplitter,()=>bt()),bi(()=>n.dblClickSplitter,C=>{[...c.value.querySelectorAll(".splitpanes__splitter")].forEach((w,L)=>{w.ondblclick=C?R=>M(R,L):void 0})}),qo(()=>d.value=!1),zo(()=>{ye(),bt(),yt(),i("ready"),d.value=!0});const ci=()=>{var C;return Yc("div",{ref:c,class:h.value},(C=s.default)==null?void 0:C.call(s))};return St("panes",r),St("indexedPanes",o),St("horizontal",_e(()=>n.horizontal)),St("requestUpdate",ni),St("onPaneAdd",si),St("onPaneRemove",ri),St("onPaneClick",b),(C,w)=>(F(),H(Qc(ci)))}},ta={__name:"pane",props:{size:{type:[Number,String]},minSize:{type:[Number,String],default:0},maxSize:{type:[Number,String],default:100}},setup(t){var e;const i=t,n=Nt("requestUpdate"),s=Nt("onPaneAdd"),r=Nt("horizontal"),o=Nt("onPaneRemove"),a=Nt("onPaneClick"),c=(e=Hc())==null?void 0:e.uid,d=Nt("indexedPanes"),l=_e(()=>d.value[c]),u=Tt(null),h=_e(()=>{const y=isNaN(i.size)||i.size===void 0?0:parseFloat(i.size);return Math.max(Math.min(y,g.value),p.value)}),p=_e(()=>{const y=parseFloat(i.minSize);return isNaN(y)?0:y}),g=_e(()=>{const y=parseFloat(i.maxSize);return isNaN(y)?100:y}),f=_e(()=>{var y;return`${r.value?"height":"width"}: ${(y=l.value)==null?void 0:y.size}%`});return zo(()=>{s({id:c,el:u.value,min:p.value,max:g.value,givenSize:i.size===void 0?null:h.value,size:h.value})}),bi(()=>h.value,y=>n({uid:c,size:y})),bi(()=>p.value,y=>n({uid:c,min:y})),bi(()=>g.value,y=>n({uid:c,max:y})),qo(()=>o(c)),(y,S)=>(F(),ae("div",{ref_key:"paneEl",ref:u,class:"splitpanes__pane",onClick:S[0]||(S[0]=x=>$c(a)(x,y._.uid)),style:Ws(f.value)},[Kc(y.$slots,"default")],4))}},Fl={props:["id","item","chatId","my","highlight","isCompact"],computed:{...pt(ft),areReactionsAllowed(){return this.chatStore.chatDto.canReact}},methods:{getLoginColoredStyle:Xc,hasLength:Be,getOwnerRoute(t){var e;return{name:Zn,params:{id:(e=t.owner)==null?void 0:e.id}}},getOwnerLink(t){var e;return td+"/"+((e=t.owner)==null?void 0:e.id)},onProfileClick(t){const e=this.getOwnerRoute(t);this.$router.push(e)},onMessageClick(t,e){this.isMobile()&&this.emitContextmenuEvent(t,e),this.sendRead(e)},onMessageMouseMove(t){this.sendRead(t)},sendRead(t){se.put(`/api/chat/${this.chatId}/message/read/${t.id}`)},deleteMessage(t){this.$emit("deleteMessage",t)},editMessage(t){this.$emit("editMessage",t)},replyOnMessage(t){this.$emit("replyOnMessage",t)},reactionOnMessage(t){this.$emit("addReaction",t)},onFilesClicked(t){this.$emit("onFilesClicked",t)},getBlogLink(){return Wo(this.chatId)},getOwner(t){let e=t==null?void 0:t.login;return e&&Go(t)&&(e="<s>"+e+"</s>"),e},getDate(t){return ed(t.createDateTime)},getMessageLink(t){const e=Zc(this.chatId,t.id);return e.name=this.$route.name,e.query=this.$route.query,e},getEmbedLinkTo(t){let e,i,n;return t.embedMessage.embedType==pi?(e=this.chatId,i=this.$route.name,n=this.$route.query):t.embedMessage.embedType==Ai&&t.embedMessage.isParticipant&&(e=t.embedMessage.chatId,i=Pi),{name:i,params:{id:e},query:n,hash:Gt+t.embedMessage.id}},canRenderLinkToSource(t){return t.embedMessage.embedType==pi?!0:!!(t.embedMessage.embedType==Ai&&t.embedMessage.isParticipant&&t.embedMessage.chatName)},getEmbedHead(t){if(t.embedMessage.embedType==pi)return this.getOwner(t.embedMessage.owner);if(t.embedMessage.embedType==Ai)return this.getOwner(t.embedMessage.owner)+this.$vuetify.locale.t("$vuetify.in")+t.embedMessage.chatName},getEmbedHeadLite(t){if(t.embedMessage.embedType==Ai)return this.getOwner(t.embedMessage.owner)},shouldShowReactions(t){var e;return(e=t==null?void 0:t.reactions)==null?void 0:e.length},shouldShowMainContainer(t){return t.embedMessage==null||t.embedMessage.embedType==pi},onShowContextMenu(t,e){this.isMobile()||this.emitContextmenuEvent(t,e)},emitContextmenuEvent(t,e){this.$emit("customcontextmenu",t,e)},embedClass(){return this.isMobile()?["message-item-text","message-item-text-mobile"]:["message-item-text"]},messageClass(t){let e=["message-item-text","ml-0"];return this.isMobile()&&e.push("message-item-text-mobile"),t.embedMessage&&e.push("after-embed"),this.shouldShowReactions(t)&&e.push("pb-2"),e},onExistingReactionClick(t){this.areReactionsAllowed&&this.$emit("onreactionclick",{id:this.item.id,reaction:t})},reactionClass(t){const e=[];return e.push("mb-2"),t>0&&e.push("ml-2"),e},getReactedUsers(t){var e;return(e=t.users)==null?void 0:e.map(i=>i.login).join(", ")},buildEmbedHtml(t){return t.embedMessage.collapsed?t.embedMessage.collapsedText:t.embedMessage.text}},created(){this.onMessageMouseMove=Hs(this.onMessageMouseMove,1e3,{leading:!0,trailing:!1})}},Vl=["id"],Bl={key:0,class:"item-avatar ml-1 mr-2 mt-1"},jl=["href"],zl=["src"],ql={class:"message-item-with-buttons-wrapper"},Gl=["href","title","innerHTML"],Wl={key:0,class:"with-space"},Hl={class:"mr-1"},Kl={key:2,class:"ma-0 pa-0 message-quick-buttons"},$l=["href","title"],Jl={key:0,class:"embedded-message"},Ql={key:1,class:"caption-small"},Yl=["innerHTML"],Xl={key:2,class:"mt-0 ml-2 mr-4 reactions"},Zl={key:0,class:"text-body-1 with-space"},eu={class:"text-h6"};function tu(t,e,i,n,s,r){var a,c,d;const o=Re("router-link");return F(),ae("div",{class:be(["px-1 mx-1 mt-4 message-item-root",i.isCompact?"message-item-root-compact":""]),id:i.id},[r.hasLength((c=(a=i.item)==null?void 0:a.owner)==null?void 0:c.avatar)?(F(),ae("div",Bl,[ge("a",{href:r.getOwnerLink(i.item),class:"user-link",onClick:e[0]||(e[0]=Te(l=>r.onProfileClick(i.item),["prevent","stop"]))},[ge("img",{src:(d=i.item.owner)==null?void 0:d.avatar},null,8,zl)],8,jl)])):W("",!0),ge("div",ql,[K(ur,{class:"ma-0 pa-0 d-flex align-center caption-small"},{default:U(()=>[ge("a",{href:r.getOwnerLink(i.item),class:"nodecorated-link",onClick:e[1]||(e[1]=Te(l=>r.onProfileClick(i.item),["prevent","stop"])),style:Ws(r.getLoginColoredStyle(i.item.owner,!0)),title:r.getDate(i.item),innerHTML:r.getOwner(i.item.owner)},null,12,Gl),i.isCompact?W("",!0):(F(),ae("span",Wl,ve(t.$vuetify.locale.t("$vuetify.time_at")),1)),i.isCompact?W("",!0):(F(),H(o,{key:1,class:"gray-link",to:r.getMessageLink(i.item),title:t.$vuetify.locale.t("$vuetify.link")},{default:U(()=>[ge("span",Hl,ve(r.getDate(i.item)),1)]),_:1},8,["to","title"])),!t.isMobile()&&!i.isCompact?(F(),ae("span",Kl,[i.item.fileItemUuid?(F(),H(ce,{key:0,class:"mx-1",onClick:e[2]||(e[2]=l=>r.onFilesClicked(i.item)),size:"small",title:t.$vuetify.locale.t("$vuetify.attached_message_files")},{default:U(()=>e[12]||(e[12]=[ie("mdi-file-download")])),_:1},8,["title"])):W("",!0),i.item.canDelete?(F(),H(ce,{key:1,class:"mx-1",color:"red",onClick:e[3]||(e[3]=l=>r.deleteMessage(i.item)),dark:"",size:"small",title:t.$vuetify.locale.t("$vuetify.delete_btn")},{default:U(()=>e[13]||(e[13]=[ie("mdi-delete")])),_:1},8,["title"])):W("",!0),i.item.canEdit?(F(),H(ce,{key:2,class:"mx-1",color:"primary",onClick:e[4]||(e[4]=l=>r.editMessage(i.item)),dark:"",size:"small",title:t.$vuetify.locale.t("$vuetify.edit")},{default:U(()=>e[14]||(e[14]=[ie("mdi-lead-pencil")])),_:1},8,["title"])):W("",!0),K(ce,{class:"mx-1",size:"small",title:t.$vuetify.locale.t("$vuetify.reply"),onClick:e[5]||(e[5]=l=>r.replyOnMessage(i.item))},{default:U(()=>e[15]||(e[15]=[ie("mdi-reply")])),_:1},8,["title"]),r.areReactionsAllowed?(F(),H(ce,{key:3,class:"mx-1",size:"small",title:t.$vuetify.locale.t("$vuetify.add_reaction_on_message"),onClick:e[6]||(e[6]=l=>r.reactionOnMessage(i.item))},{default:U(()=>e[16]||(e[16]=[ie("mdi-emoticon-outline")])),_:1},8,["title"])):W("",!0),i.item.blogPost?(F(),ae("a",{key:4,class:"mx-1 colored-link",href:r.getBlogLink(i.item),title:t.$vuetify.locale.t("$vuetify.go_to_blog_post")},[K(ce,{size:"small"},{default:U(()=>e[17]||(e[17]=[ie("mdi-postage-stamp")])),_:1})],8,$l)):W("",!0)])):W("",!0)]),_:1}),ge("div",{class:be(["pa-0 ma-0 mt-1 message-item-wrapper",{my:i.my,highlight:i.highlight}]),onClick:e[9]||(e[9]=l=>r.onMessageClick(l,i.item)),onMousemove:e[10]||(e[10]=l=>r.onMessageMouseMove(i.item)),onContextmenu:e[11]||(e[11]=l=>r.onShowContextMenu(l,i.item))},[i.item.embedMessage?(F(),ae("div",Jl,[r.canRenderLinkToSource(i.item)?(F(),H(o,{key:0,class:"caption-small",to:r.getEmbedLinkTo(i.item)},{default:U(()=>[ie(ve(r.getEmbedHead(i.item)),1)]),_:1},8,["to"])):(F(),ae("div",Ql,ve(r.getEmbedHeadLite(i.item)),1)),ge("div",{class:be(r.embedClass())},[ge("span",{innerHTML:r.buildEmbedHtml(i.item)},null,8,Yl),i.item.embedMessage.initiallyCollapsed?(F(),ae(Xt,{key:0},[i.item.embedMessage.collapsed?(F(),ae("span",{key:0,class:"caption-small",onClick:e[7]||(e[7]=l=>i.item.embedMessage.collapsed=!1),style:{cursor:"pointer"}}," ..."+ve(t.$vuetify.locale.t("$vuetify.expand")),1)):W("",!0),i.item.embedMessage.collapsed?W("",!0):(F(),ae("span",{key:1,class:"caption-small",onClick:e[8]||(e[8]=l=>i.item.embedMessage.collapsed=!0),style:{cursor:"pointer"}}," ..."+ve(t.$vuetify.locale.t("$vuetify.collapse")),1))],64)):W("",!0)],2)])):W("",!0),r.shouldShowMainContainer(i.item)?(F(),H(ur,{key:1,innerHTML:i.item.text,class:be(r.messageClass(i.item))},null,8,["innerHTML","class"])):W("",!0),r.shouldShowReactions(i.item)?(F(),ae("div",Xl,[(F(!0),ae(Xt,null,hn(i.item.reactions,(l,u)=>(F(),H(Ce,{variant:"flat",size:"small",height:"32px",rounded:"",class:be(r.reactionClass(u)),onClick:h=>r.onExistingReactionClick(l.reaction),title:r.getReactedUsers(l)},{default:U(()=>[l.count>1?(F(),ae("span",Zl,ve(""+l.count+" "),1)):W("",!0),ge("span",eu,ve(l.reaction),1)]),_:2},1032,["class","onClick","title"]))),256))])):W("",!0)],34)])],10,Vl)}const iu=mt(Fl,[["render",tu],["__scopeId","data-v-5c3e6fad"]]),_n=40,nu=200,su=600,Mn="MessageList",Dn="video-converting",Ar="data-for-original",ru={mixins:[Ll(Mn),Nl(),Ho(fi),Ko()],props:["isCompact"],data(){return{markInstance:null,storedChatId:null,isLoading:!1,initialized:!1}},computed:{...pt(ft),chatId(){return this.$route.params.id}},components:{MessageItemContextMenu:Al,MessageItem:iu},methods:{addItem(t){console.log("Adding item",t),this.transformItem(t),this.items.unshift(t),this.reduceListAfterAdd(!0),this.updateTopAndBottomIds()},changeItem(t){console.log("Replacing item",t),this.transformItem(t),Pd(this.items,t),this.updateTopAndBottomIds()},removeItem(t){console.log("Removing item",t);const e=Td(this.items,t);this.items.splice(e,1),this.updateTopAndBottomIds()},onNewMessage(t){const e=t.chatId==this.chatId,i=this.isScrolledToBottom(),n=!Be(this.searchString);e&&i&&n?(this.addItem(t),this.performMarking(),this.scrollDown()):e&&i?se.post(`/api/chat/${this.chatId}/message/filter`,{searchString:this.searchString,messageId:t.id},{signal:this.requestAbortController.signal}).then(({data:s})=>{s.found&&(this.addItem(t),this.performMarking(),this.scrollDown())}):console.log("Skipping",t,e,i,n)},onDeleteMessage(t){t.chatId==this.chatId?this.removeItem(t):console.log("Skipping",t)},onEditMessage(t){t.chatId==this.chatId?(this.changeItem(t),this.performMarking()):console.log("Skipping",t)},getMaxItemsLength(){return 200},getReduceToLength(){return 100},reduceBottom(){this.items=this.items.slice(-this.getReduceToLength()),this.startingFromItemIdBottom=this.getMaximumItemId()},reduceTop(){this.items=this.items.slice(0,this.getReduceToLength()),this.startingFromItemIdTop=this.getMinimumItemId()},enableHashInRoute(){return!0},convertLoadedFromRouteHash(t){return Gt+t},convertLoadedFromStoreHash(t){return Gt+t},extractIdFromElementForStoring(t){return this.getIdFromRouteHash(t.id)},saveScroll(t){this.preservedScroll=t?this.getMinimumItemId():this.getMaximumItemId(),console.log("Saved scroll",this.preservedScroll,"in ",Mn)},initialDirection(){return Ul},async onFirstLoad(t){await this.doScrollOnFirstLoad(),t===!0&&br(this.chatId)},getPositionFromStore(){return Cd(this.chatId)},async doDefaultScroll(){await this.scrollDown()},async fetchItems(t,e,i,n){const s=await se.get(`/api/chat/${this.chatId}/message/search`,{params:{startingFromItemId:e,includeStartingFrom:!!n,size:_n,reverse:i,searchString:t},signal:this.requestAbortController.signal});if(s.status==204)return[];const r=s.data.items;return console.log("Get items in ",Mn,r,"page",this.startingFromItemIdTop,this.startingFromItemIdBottom,"chosen",e),r.forEach(o=>{this.transformItem(o)}),r},async load(){if(!this.canDrawMessages())return Promise.resolve();this.chatStore.incrementProgressCount(),this.isLoading=!0;const{startingFromItemId:t,hasHash:e}=this.prepareHashesForRequest();try{let i=await this.fetchItems(this.searchString,t,this.isTopDirection());return e&&(i=(await this.fetchItems(this.searchString,t,!this.isTopDirection(),!0)).reverse().concat(i),yd(i,{id:t})===-1&&(console.log("Trying to search without searchString"),i=await this.fetchItems(null,t,this.isTopDirection()),i=(await this.fetchItems(null,t,!this.isTopDirection(),!0)).reverse().concat(i))),this.isTopDirection()?Sd(this.items,i):kd(this.items,i),this.updateTopAndBottomIds(),(!t||this.isFirstLoad)&&this.items.length&&se.put(`/api/chat/${this.chatId}/message/read/${this.startingFromItemIdBottom}`,null,{signal:this.requestAbortController.signal}),this.isFirstLoad||await this.clearRouteHash(),this.performMarking(),Promise.resolve(!0)}finally{this.chatStore.decrementProgressCount(),this.isLoading=!1}},transformItem(t){if(t.embedMessage){t.embedMessage.initiallyCollapsed=!1;const{initiallyCollapsed:e,collapsedText:i}=bd(t);e&&(t.embedMessage.initiallyCollapsed=!0,t.embedMessage.collapsedText=i),t.embedMessage.collapsed=t.embedMessage.initiallyCollapsed}},afterScrollRestored(t){var e;(e=t==null?void 0:t.parentElement)==null||e.scrollBy({top:this.isTopDirection()?-20:14,behavior:"instant"})},bottomElementSelector(){return".message-first-element"},topElementSelector(){return".message-last-element"},getItemId(t){return hr+t},async scrollDown(){return br(this.chatId),await this.$nextTick(()=>{this.scrollerDiv.scrollTop=0})},scrollerSelector(){return".my-messages-scroller"},reset(){this.resetInfiniteScrollVars(),this.chatStore.showScrollDown=!1,this.startingFromItemIdTop=null,this.startingFromItemIdBottom=null},async onSearchStringChangedDebounced(){await this.onSearchStringChanged()},async onSearchStringChanged(){Be(this.highlightItemId)||await this.reloadItems()},async onProfileSet(){await this.initializeHashVariablesAndReloadItems()},async doInitialize(){this.initialized||(this.initialized=!0,await this.onProfileSet())},onLoggedOut(){this.beforeUnload(),this.reset()},doUninitialize(){this.initialized&&(this.onLoggedOut(),this.initialized=!1)},canDrawMessages(){return!!this.chatStore.currentUser},async onScrollDownButton(){await this.clearRouteHash(),await this.reloadItems()},onMessagesReload(){this.reloadItems()},onScrollCallback(){this.chatStore.showScrollDown=!this.isScrolledToBottom()},isScrolledToBottom(){if(this.scrollerDiv){const t=this.isMobile()?su:nu;return Math.abs(this.scrollerDiv.scrollTop)<t}else return!1},updateTopAndBottomIds(){this.startingFromItemIdTop=this.getMinimumItemId(),this.startingFromItemIdBottom=this.getMaximumItemId()},conditionToSaveLastVisible(){return!this.isScrolledToBottom()},itemSelector(){return".message-item-root"},setPositionToStore(t,e){vd(e,t)},beforeUnload(){this.saveLastVisibleElement(this.chatId)},performMarking(){this.$nextTick(()=>{Be(this.searchString)&&(this.markInstance.unmark(),this.markInstance.mark(this.searchString))})},deleteMessage(t){T.emit(fd,{buttonName:this.$vuetify.locale.t("$vuetify.delete_btn"),title:this.$vuetify.locale.t("$vuetify.delete_message_title",t.id),text:this.$vuetify.locale.t("$vuetify.delete_message_text"),actionFunction:e=>{e.loading=!0,se.delete(`/api/chat/${this.chatId}/message/${t.id}`,{signal:this.requestAbortController.signal}).then(()=>{T.emit(gd)}).finally(()=>{e.loading=!1})}})},editMessage(t){var i;const e=md(t);pd(t)&&pr(e,t.embedMessage.text,(i=t.embedMessage.owner)==null?void 0:i.login),this.isMobile()?T.emit(ds,{dto:e,actionType:vr}):T.emit(fr,{dto:e,actionType:vr})},replyOnMessage(t){var i;const e={embedMessage:{id:t.id,embedType:pi}};pr(e,t.text,(i=t.owner)==null?void 0:i.login),this.isMobile()?T.emit(ds,{dto:e,actionType:gr}):T.emit(fr,{dto:e,actionType:gr})},onFilesClicked(t){const e={chatId:this.chatId,fileItemUuid:t.fileItemUuid};this.meIsOwnerOfMessage(t)&&(e.messageIdToDetachFiles=t.id),T.emit(hd,e)},meIsOwnerOfMessage(t){var e,i;return((e=t.owner)==null?void 0:e.id)===((i=this.chatStore.currentUser)==null?void 0:i.id)},showReadUsers(t){var e;T.emit(ud,{chatId:t.chatId,messageId:t.id,ownerId:(e=t.owner)==null?void 0:e.id})},pinMessage(t){se.put(`/api/chat/${this.chatId}/message/${t.id}/pin`,null,{params:{pin:!0},signal:this.requestAbortController.signal})},removedFromPinned(t){se.put(`/api/chat/${this.chatId}/message/${t.id}/pin`,null,{params:{pin:!1},signal:this.requestAbortController.signal})},shareMessage(t){T.emit(ld,t)},onExistingReactionClick(t){se.put(`/api/chat/${this.chatId}/message/${t.id}/reaction`,{reaction:t.reaction},{signal:this.requestAbortController.signal})},addReaction(t){T.emit(dd,{addSmileyCallback:e=>{se.put(`/api/chat/${this.chatId}/message/${t.id}/reaction`,{reaction:e},{signal:this.requestAbortController.signal})},title:this.$vuetify.locale.t("$vuetify.add_reaction_on_message")})},onShowContextMenu(t,e){!Ut(t==null?void 0:t.target,1,i=>{var n;return((n=i==null?void 0:i.tagName)==null?void 0:n.toLowerCase())=="img"})&&!Ut(t==null?void 0:t.target,1,i=>{var n;return((n=i==null?void 0:i.tagName)==null?void 0:n.toLowerCase())=="video"})&&!Ut(t==null?void 0:t.target,1,i=>{var n;return((n=i==null?void 0:i.tagName)==null?void 0:n.toLowerCase())=="audio"})&&!Ut(t==null?void 0:t.target,1,i=>{var n;return((n=i==null?void 0:i.tagName)==null?void 0:n.toLowerCase())=="a"})&&!Ut(t==null?void 0:t.target,3,i=>{var n;return(n=i==null?void 0:i.classList)==null?void 0:n.contains("reactions")})&&!Ut(t==null?void 0:t.target,1,i=>{var n;return(n=i==null?void 0:i.classList)==null?void 0:n.contains("media-in-message-wrapper")})?this.$refs.contextMenuRef.onShowContextMenu(t,e):this.isMobile()&&this.onClickTrap(t)},onCoChattedParticipantChanged(t){this.items.forEach(e=>{var i;((i=e.owner)==null?void 0:i.id)==t.id&&(e.owner=t)})},getBlogLink(){return Wo(this.chatId)},makeBlogPost(t){se.put(`/api/chat/${this.chatId}/message/${t.id}/blog-post`,null,{signal:this.requestAbortController.signal})},goToBlog(){window.location.href=this.getBlogLink()},onWsRestoredRefresh(){this.saveLastVisibleElement(this.storedChatId),this.doOnFocus()},onReactionChanged(t){const e=this.items.find(i=>i.id==t.messageId);if(e){const i=e.reactions.find(n=>n.reaction==t.reaction.reaction);i?(i.count=t.reaction.count,i.users=t.reaction.users):e.reactions.push(t.reaction)}},onReactionRemoved(t){const e=this.items.find(i=>i.id==t.messageId);e&&(e.reactions=e.reactions.filter(i=>i.reaction!=t.reaction.reaction))},publishMessage(t){se.put(`/api/chat/${this.chatId}/message/${t.id}/publish`,null,{params:{publish:!0},signal:this.requestAbortController.signal}).then(()=>{const e=cd(this.chatId,t.id);navigator.clipboard.writeText(e),this.setTempNotification(this.$vuetify.locale.t("$vuetify.published_message_link_copied"))})},removePublic(t){se.put(`/api/chat/${this.chatId}/message/${t.id}/publish`,null,{params:{publish:!1},signal:this.requestAbortController.signal})},onClickTrap(t){var i,n,s,r;const e=[xi(t==null?void 0:t.target,0,o=>{var a,c;return((a=o==null?void 0:o.tagName)==null?void 0:a.toLowerCase())=="img"&&!((c=o==null?void 0:o.parentElement.classList)!=null&&c.contains("media-in-message-wrapper"))}),xi(t==null?void 0:t.target,0,o=>{var a,c;return((a=o==null?void 0:o.tagName)==null?void 0:a.toLowerCase())=="span"&&((c=o==null?void 0:o.classList)==null?void 0:c.contains("media-in-message-button-open"))}),xi(t==null?void 0:t.target,0,o=>{var a,c;return((a=o==null?void 0:o.tagName)==null?void 0:a.toLowerCase())=="span"&&((c=o==null?void 0:o.classList)==null?void 0:c.contains("media-in-message-button-replace"))}),xi(t==null?void 0:t.target,1,o=>{var a;return((a=o==null?void 0:o.tagName)==null?void 0:a.toLowerCase())=="a"})].filter(o=>o.found);if(e.length){t.preventDefault();const o=e[e.length-1].el;switch((i=o==null?void 0:o.tagName)==null?void 0:i.toLowerCase()){case"img":{const a=Be(o.getAttribute("data-original"))?o.getAttribute("data-original"):o.src;T.emit(mr,{canShowAsImage:!0,url:a,canSwitch:!0});break}case"span":{const a=o.parentElement;if(a.classList.contains("media-in-message-wrapper")){if((n=o.classList)!=null&&n.contains("media-in-message-button-open")){const c=Array.from(a==null?void 0:a.children).find(d=>{var l;return((l=d==null?void 0:d.tagName)==null?void 0:l.toLowerCase())=="img"});if(c&&!c.classList.contains(Dn)){const d={canSwitch:!0,url:c.getAttribute("data-original"),previewUrl:c.src};a.classList.contains("media-in-message-wrapper-video")?d.canPlayAsVideo=!0:a.classList.contains("media-in-message-wrapper-audio")&&(d.canPlayAsAudio=!0),T.emit(mr,d)}}else if((s=o.classList)!=null&&s.contains("media-in-message-button-replace")){const c=Array.from(a==null?void 0:a.children).find(d=>{var l;return((l=d==null?void 0:d.tagName)==null?void 0:l.toLowerCase())=="img"});if(c){const d=c.src,l=c.getAttribute("data-original");if(a.classList.contains("media-in-message-wrapper-video")){a.removeChild(c),a.removeChild(o);const u=Array.from(a.children).find(p=>{var g;return(g=p==null?void 0:p.classList)==null?void 0:g.contains("media-in-message-button-open")});u&&a.removeChild(u);const h=this.createVideoReplacementElement(l,d);a.appendChild(h),se.post("/api/storage/view/status",{url:l},{signal:this.requestAbortController.signal}).then(p=>{if(p.data.status=="converting"){a.removeChild(h);const g=document.createElement("IMG");g.src=p.data.statusImage,g.setAttribute(Ar,l),g.className="video-custom-class "+Dn,a.appendChild(g)}})}else if(a.classList.contains("media-in-message-wrapper-audio")){a.removeChild(c),a.removeChild(o);const u=Array.from(a==null?void 0:a.children).find(p=>{var g;return(g=p==null?void 0:p.classList)==null?void 0:g.contains("media-in-message-button-open")});a.removeChild(u);const h=this.createAudioReplacementElement(l);a.appendChild(h),se.post("/api/storage/view/status",{url:l},{signal:this.requestAbortController.signal}).then(p=>{var f;const g=document.createElement("P");g.textContent=(f=p.data)==null?void 0:f.filename,a.prepend(g)})}else if(a.classList.contains("media-in-message-wrapper-iframe")){const u=c.getAttribute("data-width"),h=c.getAttribute("data-height"),p=c.getAttribute("data-allowfullscreen");a.removeChild(c),a.removeChild(o);const g=this.createIframeReplacementElement(l,u,h,p);a.appendChild(g)}else console.info("no case for it")}else console.info("holder is not found")}}break}case"a":{const a=o.getAttribute("href");if((r=o.classList)!=null&&r.contains("mention")){const c=o.getAttribute("data-id");if(Be(c)){const d={name:Zn,params:{id:c}};this.$router.push(d)}break}else if(a.startsWith("/")){console.info("examining internal link",a);const c=rd(a);if(c){console.info("href",a,"is recognized as message",c);const h={name:this.isVideoRoute()?Ei:Pi,params:{id:c.chatId},hash:Gt+c.id};yi(this.$route,this.$router,h);break}const d=od(a);if(d){console.info("href",a,"is recognized as chat",d);const h={name:Pi,params:{id:d.chatId}};yi(this.$route,this.$router,h);break}const l=ad(a);if(l){console.info("href",a,"is recognized as user",l);const h={name:Zn,params:{id:l.userId}};yi(this.$route,this.$router,h);break}}window.open(a,"_blank").focus()}}}},isVideoRoute(){return this.$route.name==Ei},onFileCreatedEvent(t){if(t.fileInfoDto.canPlayAsVideo&&sd(t.fileInfoDto.filename)){const e=this.items.find(i=>t.fileInfoDto.fileItemUuid==i.fileItemUuid);if(e){const n=document.getElementById(hr+e.id).getElementsByClassName(Dn);for(const s of n)if(s.getAttribute(Ar)==t.fileInfoDto.url){const r=s.parentElement;r.removeChild(s);const o=this.createVideoReplacementElement(t.fileInfoDto.url,t.fileInfoDto.previewUrl);r.appendChild(o)}}}},createVideoReplacementElement(t,e){const i=document.createElement("VIDEO");return i.src=t,i.poster=e,i.playsinline=!0,i.controls=!0,i.className="video-custom-class",i},createAudioReplacementElement(t){const e=document.createElement("AUDIO");return e.src=t,e.controls=!0,e.className="audio-custom-class",e},createIframeReplacementElement(t,e,i,n){const s=document.createElement("IFRAME");return s.src=t,s.setAttribute("width",e),s.setAttribute("height",i),n&&s.setAttribute("allowFullScreen",""),s.className="iframe-custom-class",s},getMaximumItemId(){return this.items.length?Math.max(...this.items.map(t=>t.id)):null},getMinimumItemId(){return this.items.length?Math.min(...this.items.map(t=>t.id)):null},isAppropriateHash(t){return nd(t)},onFocus(){if(this.chatStore.currentUser&&this.items&&this.isScrolledToBottom()){const t=this.items.slice(0,_n);this.chatStore.canShowPinnedLink=!1,se.post(`/api/chat/${this.chatId}/message/fresh`,t,{params:{size:_n,searchString:this.searchString},signal:this.requestAbortController.signal}).then(e=>{e.data.ok?console.log("No need to update messages"):(console.log("Need to update messages"),this.reloadItems())}).finally(()=>{this.chatStore.canShowPinnedLink=!0})}}},created(){this.onSearchStringChangedDebounced=Hs(this.onSearchStringChangedDebounced,700,{leading:!1,trailing:!0})},watch:{$route:{handler:async function(t,e){if(t.params.id!=e.params.id&&(t.params.id&&(this.storedChatId=t.params.id),console.debug("Chat id has been changed",e.params.id,"->",t.params.id),this.saveLastVisibleElement(e.params.id),cs(t)&&Be(t.params.id))){await this.onProfileSet();return}const i=t.query[fi],n=e.query[fi];if(cs(t)&&Be(t.hash)&&this.isAppropriateHash(t.hash)&&t.hash!=e.hash){console.log("Changed route hash, going to scroll",t.hash),await this.scrollToOrLoad(t.hash,i==n);return}if(i!=n){this.onSearchStringChangedDebounced();return}}}},async mounted(){this.markInstance=new id(this.scrollerSelector()+" .message-item-text"),addEventListener("beforeunload",this.beforeUnload),this.storedChatId=this.chatId,T.on(en,this.doInitialize),T.on(tn,this.doUninitialize),T.on(rs,this.onScrollDownButton),T.on(es,this.onNewMessage),T.on(ts,this.onDeleteMessage),T.on(is,this.onEditMessage),T.on(ns,this.onReactionChanged),T.on(ss,this.onReactionRemoved),T.on(nn,this.onCoChattedParticipantChanged),T.on(sn,this.onWsRestoredRefresh),T.on(os,this.onMessagesReload),T.on(as,this.onFileCreatedEvent),this.chatStore.searchType=fi,this.canDrawMessages()&&await this.doInitialize(),this.installOnFocus()},beforeUnmount(){this.saveLastVisibleElement(this.storedChatId),this.uninstallOnFocus(),this.doUninitialize(),this.markInstance.unmark(),this.markInstance=null,removeEventListener("beforeunload",this.beforeUnload),this.storedChatId=null,this.uninstallScroller(),T.off(es,this.onNewMessage),T.off(ts,this.onDeleteMessage),T.off(is,this.onEditMessage),T.off(ns,this.onReactionChanged),T.off(ss,this.onReactionRemoved),T.off(en,this.doInitialize),T.off(tn,this.doUninitialize),T.off(rs,this.onScrollDownButton),T.off(nn,this.onCoChattedParticipantChanged),T.off(sn,this.onWsRestoredRefresh),T.off(os,this.onMessagesReload),T.off(as,this.onFileCreatedEvent)}};function ou(t,e,i,n,s,r){const o=Re("MessageItem"),a=Re("MessageItemContextMenu");return F(),ae("div",{class:"ma-0 px-0 pt-0 pb-2 my-messages-scroller",onScrollPassive:e[0]||(e[0]=(...c)=>t.onScroll&&t.onScroll(...c))},[e[1]||(e[1]=ge("div",{class:"message-first-element",style:{"min-height":"1px",background:"white"}},null,-1)),(F(!0),ae(Xt,null,hn(t.items,c=>(F(),H(o,{id:r.getItemId(c.id),key:c.id,item:c,chatId:r.chatId,my:r.meIsOwnerOfMessage(c),highlight:c.id==t.highlightItemId,isCompact:i.isCompact,onCustomcontextmenu:Te(d=>r.onShowContextMenu(d,c),["stop"]),onDeleteMessage:r.deleteMessage,onEditMessage:r.editMessage,onReplyOnMessage:r.replyOnMessage,onOnFilesClicked:r.onFilesClicked,onAddReaction:r.addReaction,onOnreactionclick:r.onExistingReactionClick,onClick:r.onClickTrap},null,8,["id","item","chatId","my","highlight","isCompact","onCustomcontextmenu","onDeleteMessage","onEditMessage","onReplyOnMessage","onOnFilesClicked","onAddReaction","onOnreactionclick","onClick"]))),128)),t.items.length==0&&!s.isLoading?(F(),H(Ed,{key:0,class:"mx-2"},{default:U(()=>[ie(ve(t.$vuetify.locale.t("$vuetify.messages_not_found")),1)]),_:1})):W("",!0),e[2]||(e[2]=ge("div",{class:"message-last-element",style:{"min-height":"1px",background:"white"}},null,-1)),K(a,{ref:"contextMenuRef",canResend:t.chatStore.chatDto.canResend,isBlog:t.chatStore.chatDto.blog,onDeleteMessage:this.deleteMessage,onEditMessage:this.editMessage,onReplyOnMessage:this.replyOnMessage,onOnFilesClicked:r.onFilesClicked,onShowReadUsers:this.showReadUsers,onPinMessage:this.pinMessage,onRemovedFromPinned:this.removedFromPinned,onShareMessage:this.shareMessage,onMakeBlogPost:r.makeBlogPost,onGoToBlog:r.goToBlog,onAddReaction:r.addReaction,onPublishMessage:r.publishMessage,onRemovePublic:r.removePublic},null,8,["canResend","isBlog","onDeleteMessage","onEditMessage","onReplyOnMessage","onOnFilesClicked","onShowReadUsers","onPinMessage","onRemovedFromPinned","onShareMessage","onMakeBlogPost","onGoToBlog","onAddReaction","onPublishMessage","onRemovePublic"])],32)}const au=mt(ru,[["render",ou]]);var xr={};function cu(t,e){return e.forEach(function(i){i&&typeof i!="string"&&!Array.isArray(i)&&Object.keys(i).forEach(function(n){if(n!=="default"&&!(n in t)){var s=Object.getOwnPropertyDescriptor(i,n);Object.defineProperty(t,n,s.get?s:{enumerable:!0,get:function(){return i[n]}})}})}),Object.freeze(t)}var du=Object.defineProperty,lu=(t,e,i)=>e in t?du(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,Lr=(t,e,i)=>lu(t,typeof e!="symbol"?e+"":e,i);class Me{constructor(){Lr(this,"_locking"),Lr(this,"_locks"),this._locking=Promise.resolve(),this._locks=0}isLocked(){return this._locks>0}lock(){this._locks+=1;let e;const i=new Promise(s=>e=()=>{this._locks-=1,s()}),n=this._locking.then(()=>e);return this._locking=this._locking.then(()=>i),n}}function ue(t,e){if(!t)throw new Error(e)}const uu=34028234663852886e22,hu=-34028234663852886e22,mu=4294967295,pu=2147483647,fu=-2147483648;function Bi(t){if(typeof t!="number")throw new Error("invalid int 32: "+typeof t);if(!Number.isInteger(t)||t>pu||t<fu)throw new Error("invalid int 32: "+t)}function us(t){if(typeof t!="number")throw new Error("invalid uint 32: "+typeof t);if(!Number.isInteger(t)||t>mu||t<0)throw new Error("invalid uint 32: "+t)}function ia(t){if(typeof t!="number")throw new Error("invalid float 32: "+typeof t);if(Number.isFinite(t)&&(t>uu||t<hu))throw new Error("invalid float 32: "+t)}const na=Symbol("@bufbuild/protobuf/enum-type");function gu(t){const e=t[na];return ue(e,"missing enum type on enum object"),e}function sa(t,e,i,n){t[na]=ra(e,i.map(s=>({no:s.no,name:s.name,localName:t[s.no]})))}function ra(t,e,i){const n=Object.create(null),s=Object.create(null),r=[];for(const o of e){const a=oa(o);r.push(a),n[o.name]=a,s[o.no]=a}return{typeName:t,values:r,findName(o){return n[o]},findNumber(o){return s[o]}}}function vu(t,e,i){const n={};for(const s of e){const r=oa(s);n[r.localName]=r.no,n[r.no]=r.localName}return sa(n,t,e),n}function oa(t){return"localName"in t?t:Object.assign(Object.assign({},t),{localName:t.name})}class Ks{equals(e){return this.getType().runtime.util.equals(this.getType(),this,e)}clone(){return this.getType().runtime.util.clone(this)}fromBinary(e,i){const n=this.getType(),s=n.runtime.bin,r=s.makeReadOptions(i);return s.readMessage(this,r.readerFactory(e),e.byteLength,r),this}fromJson(e,i){const n=this.getType(),s=n.runtime.json,r=s.makeReadOptions(i);return s.readMessage(n,e,r,this),this}fromJsonString(e,i){let n;try{n=JSON.parse(e)}catch(s){throw new Error("cannot decode ".concat(this.getType().typeName," from JSON: ").concat(s instanceof Error?s.message:String(s)))}return this.fromJson(n,i)}toBinary(e){const i=this.getType(),n=i.runtime.bin,s=n.makeWriteOptions(e),r=s.writerFactory();return n.writeMessage(this,r,s),r.finish()}toJson(e){const i=this.getType(),n=i.runtime.json,s=n.makeWriteOptions(e);return n.writeMessage(this,s)}toJsonString(e){var i;const n=this.toJson(e);return JSON.stringify(n,null,(i=e==null?void 0:e.prettySpaces)!==null&&i!==void 0?i:0)}toJSON(){return this.toJson({emitDefaultValues:!0})}getType(){return Object.getPrototypeOf(this).constructor}}function bu(t,e,i,n){var s;const r=(s=n==null?void 0:n.localName)!==null&&s!==void 0?s:e.substring(e.lastIndexOf(".")+1),o={[r]:function(a){t.util.initFields(this),t.util.initPartial(a,this)}}[r];return Object.setPrototypeOf(o.prototype,new Ks),Object.assign(o,{runtime:t,typeName:e,fields:t.util.newFieldList(i),fromBinary(a,c){return new o().fromBinary(a,c)},fromJson(a,c){return new o().fromJson(a,c)},fromJsonString(a,c){return new o().fromJsonString(a,c)},equals(a,c){return t.util.equals(o,a,c)}}),o}function yu(){let t=0,e=0;for(let n=0;n<28;n+=7){let s=this.buf[this.pos++];if(t|=(s&127)<<n,(s&128)==0)return this.assertBounds(),[t,e]}let i=this.buf[this.pos++];if(t|=(i&15)<<28,e=(i&112)>>4,(i&128)==0)return this.assertBounds(),[t,e];for(let n=3;n<=31;n+=7){let s=this.buf[this.pos++];if(e|=(s&127)<<n,(s&128)==0)return this.assertBounds(),[t,e]}throw new Error("invalid varint")}function On(t,e,i){for(let r=0;r<28;r=r+7){const o=t>>>r,a=!(!(o>>>7)&&e==0),c=(a?o|128:o)&255;if(i.push(c),!a)return}const n=t>>>28&15|(e&7)<<4,s=e>>3!=0;if(i.push((s?n|128:n)&255),!!s){for(let r=3;r<31;r=r+7){const o=e>>>r,a=!!(o>>>7),c=(a?o|128:o)&255;if(i.push(c),!a)return}i.push(e>>>31&1)}}const ji=4294967296;function Nr(t){const e=t[0]==="-";e&&(t=t.slice(1));const i=1e6;let n=0,s=0;function r(o,a){const c=Number(t.slice(o,a));s*=i,n=n*i+c,n>=ji&&(s=s+(n/ji|0),n=n%ji)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),e?ca(n,s):$s(n,s)}function Su(t,e){let i=$s(t,e);const n=i.hi&2147483648;n&&(i=ca(i.lo,i.hi));const s=aa(i.lo,i.hi);return n?"-"+s:s}function aa(t,e){if({lo:t,hi:e}=ku(t,e),e<=2097151)return String(ji*e+t);const i=t&16777215,n=(t>>>24|e<<8)&16777215,s=e>>16&65535;let r=i+n*6777216+s*6710656,o=n+s*8147497,a=s*2;const c=1e7;return r>=c&&(o+=Math.floor(r/c),r%=c),o>=c&&(a+=Math.floor(o/c),o%=c),a.toString()+Ur(o)+Ur(r)}function ku(t,e){return{lo:t>>>0,hi:e>>>0}}function $s(t,e){return{lo:t|0,hi:e|0}}function ca(t,e){return e=~e,t?t=~t+1:e+=1,$s(t,e)}const Ur=t=>{const e=String(t);return"0000000".slice(e.length)+e};function Fr(t,e){if(t>=0){for(;t>127;)e.push(t&127|128),t=t>>>7;e.push(t)}else{for(let i=0;i<9;i++)e.push(t&127|128),t=t>>7;e.push(1)}}function Cu(){let t=this.buf[this.pos++],e=t&127;if((t&128)==0)return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<7,(t&128)==0)return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<14,(t&128)==0)return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(t&127)<<21,(t&128)==0)return this.assertBounds(),e;t=this.buf[this.pos++],e|=(t&15)<<28;for(let i=5;(t&128)!==0&&i<10;i++)t=this.buf[this.pos++];if((t&128)!=0)throw new Error("invalid varint");return this.assertBounds(),e>>>0}function Tu(){const t=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof t.getBigInt64=="function"&&typeof t.getBigUint64=="function"&&typeof t.setBigInt64=="function"&&typeof t.setBigUint64=="function"&&(typeof process!="object"||typeof xr!="object"||xr.BUF_BIGINT_DISABLE!=="1")){const s=BigInt("-9223372036854775808"),r=BigInt("9223372036854775807"),o=BigInt("0"),a=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(c){const d=typeof c=="bigint"?c:BigInt(c);if(d>r||d<s)throw new Error("int64 invalid: ".concat(c));return d},uParse(c){const d=typeof c=="bigint"?c:BigInt(c);if(d>a||d<o)throw new Error("uint64 invalid: ".concat(c));return d},enc(c){return t.setBigInt64(0,this.parse(c),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},uEnc(c){return t.setBigInt64(0,this.uParse(c),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},dec(c,d){return t.setInt32(0,c,!0),t.setInt32(4,d,!0),t.getBigInt64(0,!0)},uDec(c,d){return t.setInt32(0,c,!0),t.setInt32(4,d,!0),t.getBigUint64(0,!0)}}}const i=s=>ue(/^-?[0-9]+$/.test(s),"int64 invalid: ".concat(s)),n=s=>ue(/^[0-9]+$/.test(s),"uint64 invalid: ".concat(s));return{zero:"0",supported:!1,parse(s){return typeof s!="string"&&(s=s.toString()),i(s),s},uParse(s){return typeof s!="string"&&(s=s.toString()),n(s),s},enc(s){return typeof s!="string"&&(s=s.toString()),i(s),Nr(s)},uEnc(s){return typeof s!="string"&&(s=s.toString()),n(s),Nr(s)},dec(s,r){return Su(s,r)},uDec(s,r){return aa(s,r)}}}const de=Tu();var E;(function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"})(E||(E={}));var ut;(function(t){t[t.BIGINT=0]="BIGINT",t[t.STRING=1]="STRING"})(ut||(ut={}));function st(t,e,i){if(e===i)return!0;if(t==E.BYTES){if(!(e instanceof Uint8Array)||!(i instanceof Uint8Array)||e.length!==i.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==i[n])return!1;return!0}switch(t){case E.UINT64:case E.FIXED64:case E.INT64:case E.SFIXED64:case E.SINT64:return e==i}return!1}function Zt(t,e){switch(t){case E.BOOL:return!1;case E.UINT64:case E.FIXED64:case E.INT64:case E.SFIXED64:case E.SINT64:return e==0?de.zero:"0";case E.DOUBLE:case E.FLOAT:return 0;case E.BYTES:return new Uint8Array(0);case E.STRING:return"";default:return 0}}function da(t,e){switch(t){case E.BOOL:return e===!1;case E.STRING:return e==="";case E.BYTES:return e instanceof Uint8Array&&!e.byteLength;default:return e==0}}var me;(function(t){t[t.Varint=0]="Varint",t[t.Bit64=1]="Bit64",t[t.LengthDelimited=2]="LengthDelimited",t[t.StartGroup=3]="StartGroup",t[t.EndGroup=4]="EndGroup",t[t.Bit32=5]="Bit32"})(me||(me={}));class Pu{constructor(e){this.stack=[],this.textEncoder=e??new TextEncoder,this.chunks=[],this.buf=[]}finish(){this.chunks.push(new Uint8Array(this.buf));let e=0;for(let s=0;s<this.chunks.length;s++)e+=this.chunks[s].length;let i=new Uint8Array(e),n=0;for(let s=0;s<this.chunks.length;s++)i.set(this.chunks[s],n),n+=this.chunks[s].length;return this.chunks=[],i}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let e=this.finish(),i=this.stack.pop();if(!i)throw new Error("invalid state, fork stack empty");return this.chunks=i.chunks,this.buf=i.buf,this.uint32(e.byteLength),this.raw(e)}tag(e,i){return this.uint32((e<<3|i)>>>0)}raw(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}uint32(e){for(us(e);e>127;)this.buf.push(e&127|128),e=e>>>7;return this.buf.push(e),this}int32(e){return Bi(e),Fr(e,this.buf),this}bool(e){return this.buf.push(e?1:0),this}bytes(e){return this.uint32(e.byteLength),this.raw(e)}string(e){let i=this.textEncoder.encode(e);return this.uint32(i.byteLength),this.raw(i)}float(e){ia(e);let i=new Uint8Array(4);return new DataView(i.buffer).setFloat32(0,e,!0),this.raw(i)}double(e){let i=new Uint8Array(8);return new DataView(i.buffer).setFloat64(0,e,!0),this.raw(i)}fixed32(e){us(e);let i=new Uint8Array(4);return new DataView(i.buffer).setUint32(0,e,!0),this.raw(i)}sfixed32(e){Bi(e);let i=new Uint8Array(4);return new DataView(i.buffer).setInt32(0,e,!0),this.raw(i)}sint32(e){return Bi(e),e=(e<<1^e>>31)>>>0,Fr(e,this.buf),this}sfixed64(e){let i=new Uint8Array(8),n=new DataView(i.buffer),s=de.enc(e);return n.setInt32(0,s.lo,!0),n.setInt32(4,s.hi,!0),this.raw(i)}fixed64(e){let i=new Uint8Array(8),n=new DataView(i.buffer),s=de.uEnc(e);return n.setInt32(0,s.lo,!0),n.setInt32(4,s.hi,!0),this.raw(i)}int64(e){let i=de.enc(e);return On(i.lo,i.hi,this.buf),this}sint64(e){let i=de.enc(e),n=i.hi>>31,s=i.lo<<1^n,r=(i.hi<<1|i.lo>>>31)^n;return On(s,r,this.buf),this}uint64(e){let i=de.uEnc(e);return On(i.lo,i.hi,this.buf),this}}class Eu{constructor(e,i){this.varint64=yu,this.uint32=Cu,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.textDecoder=i??new TextDecoder}tag(){let e=this.uint32(),i=e>>>3,n=e&7;if(i<=0||n<0||n>5)throw new Error("illegal tag: field no "+i+" wire type "+n);return[i,n]}skip(e,i){let n=this.pos;switch(e){case me.Varint:for(;this.buf[this.pos++]&128;);break;case me.Bit64:this.pos+=4;case me.Bit32:this.pos+=4;break;case me.LengthDelimited:let s=this.uint32();this.pos+=s;break;case me.StartGroup:for(;;){const[r,o]=this.tag();if(o===me.EndGroup){if(i!==void 0&&r!==i)throw new Error("invalid end group tag");break}this.skip(o,r)}break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(n,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return this.uint32()|0}sint32(){let e=this.uint32();return e>>>1^-(e&1)}int64(){return de.dec(...this.varint64())}uint64(){return de.uDec(...this.varint64())}sint64(){let[e,i]=this.varint64(),n=-(e&1);return e=(e>>>1|(i&1)<<31)^n,i=i>>>1^n,de.dec(e,i)}bool(){let[e,i]=this.varint64();return e!==0||i!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return de.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return de.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),i=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(i,i+e)}string(){return this.textDecoder.decode(this.bytes())}}function Iu(t,e,i,n){let s;return{typeName:e,extendee:i,get field(){if(!s){const r=typeof n=="function"?n():n;r.name=e.split(".").pop(),r.jsonName="[".concat(e,"]"),s=t.util.newFieldList([r]).list()[0]}return s},runtime:t}}function la(t){const e=t.field.localName,i=Object.create(null);return i[e]=Ru(t),[i,()=>i[e]]}function Ru(t){const e=t.field;if(e.repeated)return[];if(e.default!==void 0)return e.default;switch(e.kind){case"enum":return e.T.values[0].no;case"scalar":return Zt(e.T,e.L);case"message":const i=e.T,n=new i;return i.fieldWrapper?i.fieldWrapper.unwrapField(n):n;case"map":throw"map fields are not allowed to be extensions"}}function wu(t,e){if(!e.repeated&&(e.kind=="enum"||e.kind=="scalar")){for(let i=t.length-1;i>=0;--i)if(t[i].no==e.no)return[t[i]];return[]}return t.filter(i=>i.no===e.no)}let et="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),mn=[];for(let t=0;t<et.length;t++)mn[et[t].charCodeAt(0)]=t;mn[45]=et.indexOf("+");mn[95]=et.indexOf("/");const ua={dec(t){let e=t.length*3/4;t[t.length-2]=="="?e-=2:t[t.length-1]=="="&&(e-=1);let i=new Uint8Array(e),n=0,s=0,r,o=0;for(let a=0;a<t.length;a++){if(r=mn[t.charCodeAt(a)],r===void 0)switch(t[a]){case"=":s=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string.")}switch(s){case 0:o=r,s=1;break;case 1:i[n++]=o<<2|(r&48)>>4,o=r,s=2;break;case 2:i[n++]=(o&15)<<4|(r&60)>>2,o=r,s=3;break;case 3:i[n++]=(o&3)<<6|r,s=0;break}}if(s==1)throw Error("invalid base64 string.");return i.subarray(0,n)},enc(t){let e="",i=0,n,s=0;for(let r=0;r<t.length;r++)switch(n=t[r],i){case 0:e+=et[n>>2],s=(n&3)<<4,i=1;break;case 1:e+=et[s|n>>4],s=(n&15)<<2,i=2;break;case 2:e+=et[s|n>>6],e+=et[n&63],i=0;break}return i&&(e+=et[s],e+="=",i==1&&(e+="=")),e}};function _u(t,e,i){ma(e,t);const n=e.runtime.bin.makeReadOptions(i),s=wu(t.getType().runtime.bin.listUnknownFields(t),e.field),[r,o]=la(e);for(const a of s)e.runtime.bin.readField(r,n.readerFactory(a.data),e.field,a.wireType,n);return o()}function Mu(t,e,i,n){ma(e,t);const s=e.runtime.bin.makeReadOptions(n),r=e.runtime.bin.makeWriteOptions(n);if(ha(t,e)){const d=t.getType().runtime.bin.listUnknownFields(t).filter(l=>l.no!=e.field.no);t.getType().runtime.bin.discardUnknownFields(t);for(const l of d)t.getType().runtime.bin.onUnknownField(t,l.no,l.wireType,l.data)}const o=r.writerFactory();let a=e.field;!a.opt&&!a.repeated&&(a.kind=="enum"||a.kind=="scalar")&&(a=Object.assign(Object.assign({},e.field),{opt:!0})),e.runtime.bin.writeField(a,i,o,r);const c=s.readerFactory(o.finish());for(;c.pos<c.len;){const[d,l]=c.tag(),u=c.skip(l,d);t.getType().runtime.bin.onUnknownField(t,d,l,u)}}function ha(t,e){const i=t.getType();return e.extendee.typeName===i.typeName&&!!i.runtime.bin.listUnknownFields(t).find(n=>n.no==e.field.no)}function ma(t,e){ue(t.extendee.typeName==e.getType().typeName,"extension ".concat(t.typeName," can only be applied to message ").concat(t.extendee.typeName))}function pa(t,e){const i=t.localName;if(t.repeated)return e[i].length>0;if(t.oneof)return e[t.oneof.localName].case===i;switch(t.kind){case"enum":case"scalar":return t.opt||t.req?e[i]!==void 0:t.kind=="enum"?e[i]!==t.T.values[0].no:!da(t.T,e[i]);case"message":return e[i]!==void 0;case"map":return Object.keys(e[i]).length>0}}function Vr(t,e){const i=t.localName,n=!t.opt&&!t.req;if(t.repeated)e[i]=[];else if(t.oneof)e[t.oneof.localName]={case:void 0};else switch(t.kind){case"map":e[i]={};break;case"enum":e[i]=n?t.T.values[0].no:void 0;break;case"scalar":e[i]=n?Zt(t.T,t.L):void 0;break;case"message":e[i]=void 0;break}}function Rt(t,e){if(t===null||typeof t!="object"||!Object.getOwnPropertyNames(Ks.prototype).every(n=>n in t&&typeof t[n]=="function"))return!1;const i=t.getType();return i===null||typeof i!="function"||!("typeName"in i)||typeof i.typeName!="string"?!1:e===void 0?!0:i.typeName==e.typeName}function fa(t,e){return Rt(e)||!t.fieldWrapper?e:t.fieldWrapper.wrapField(e)}E.DOUBLE,E.FLOAT,E.INT64,E.UINT64,E.INT32,E.UINT32,E.BOOL,E.STRING,E.BYTES;const Br={ignoreUnknownFields:!1},jr={emitDefaultValues:!1,enumAsInteger:!1,useProtoFieldName:!1,prettySpaces:0};function Du(t){return t?Object.assign(Object.assign({},Br),t):Br}function Ou(t){return t?Object.assign(Object.assign({},jr),t):jr}const rn=Symbol(),zi=Symbol();function Au(){return{makeReadOptions:Du,makeWriteOptions:Ou,readMessage(t,e,i,n){if(e==null||Array.isArray(e)||typeof e!="object")throw new Error("cannot decode message ".concat(t.typeName," from JSON: ").concat(Ke(e)));n=n??new t;const s=new Map,r=i.typeRegistry;for(const[o,a]of Object.entries(e)){const c=t.fields.findJsonName(o);if(c){if(c.oneof){if(a===null&&c.kind=="scalar")continue;const d=s.get(c.oneof);if(d!==void 0)throw new Error("cannot decode message ".concat(t.typeName,' from JSON: multiple keys for oneof "').concat(c.oneof.name,'" present: "').concat(d,'", "').concat(o,'"'));s.set(c.oneof,o)}zr(n,a,c,i,t)}else{let d=!1;if(r!=null&&r.findExtension&&o.startsWith("[")&&o.endsWith("]")){const l=r.findExtension(o.substring(1,o.length-1));if(l&&l.extendee.typeName==t.typeName){d=!0;const[u,h]=la(l);zr(u,a,l.field,i,l),Mu(n,l,h(),i)}}if(!d&&!i.ignoreUnknownFields)throw new Error("cannot decode message ".concat(t.typeName,' from JSON: key "').concat(o,'" is unknown'))}}return n},writeMessage(t,e){const i=t.getType(),n={};let s;try{for(s of i.fields.byNumber()){if(!pa(s,t)){if(s.req)throw"required field not set";if(!e.emitDefaultValues||!Lu(s))continue}const o=s.oneof?t[s.oneof.localName].value:t[s.localName],a=qr(s,o,e);a!==void 0&&(n[e.useProtoFieldName?s.name:s.jsonName]=a)}const r=e.typeRegistry;if(r!=null&&r.findExtensionFor)for(const o of i.runtime.bin.listUnknownFields(t)){const a=r.findExtensionFor(i.typeName,o.no);if(a&&ha(t,a)){const c=_u(t,a,e),d=qr(a.field,c,e);d!==void 0&&(n[a.field.jsonName]=d)}}}catch(r){const o=s?"cannot encode field ".concat(i.typeName,".").concat(s.name," to JSON"):"cannot encode message ".concat(i.typeName," to JSON"),a=r instanceof Error?r.message:String(r);throw new Error(o+(a.length>0?": ".concat(a):""))}return n},readScalar(t,e,i){return ki(t,e,i??ut.BIGINT,!0)},writeScalar(t,e,i){if(e!==void 0&&(i||da(t,e)))return qi(t,e)},debug:Ke}}function Ke(t){if(t===null)return"null";switch(typeof t){case"object":return Array.isArray(t)?"array":"object";case"string":return t.length>100?"string":'"'.concat(t.split('"').join('\\"'),'"');default:return String(t)}}function zr(t,e,i,n,s){let r=i.localName;if(i.repeated){if(ue(i.kind!="map"),e===null)return;if(!Array.isArray(e))throw new Error("cannot decode field ".concat(s.typeName,".").concat(i.name," from JSON: ").concat(Ke(e)));const o=t[r];for(const a of e){if(a===null)throw new Error("cannot decode field ".concat(s.typeName,".").concat(i.name," from JSON: ").concat(Ke(a)));switch(i.kind){case"message":o.push(i.T.fromJson(a,n));break;case"enum":const c=An(i.T,a,n.ignoreUnknownFields,!0);c!==zi&&o.push(c);break;case"scalar":try{o.push(ki(i.T,a,i.L,!0))}catch(d){let l="cannot decode field ".concat(s.typeName,".").concat(i.name," from JSON: ").concat(Ke(a));throw d instanceof Error&&d.message.length>0&&(l+=": ".concat(d.message)),new Error(l)}break}}}else if(i.kind=="map"){if(e===null)return;if(typeof e!="object"||Array.isArray(e))throw new Error("cannot decode field ".concat(s.typeName,".").concat(i.name," from JSON: ").concat(Ke(e)));const o=t[r];for(const[a,c]of Object.entries(e)){if(c===null)throw new Error("cannot decode field ".concat(s.typeName,".").concat(i.name," from JSON: map value null"));let d;try{d=xu(i.K,a)}catch(l){let u="cannot decode map key for field ".concat(s.typeName,".").concat(i.name," from JSON: ").concat(Ke(e));throw l instanceof Error&&l.message.length>0&&(u+=": ".concat(l.message)),new Error(u)}switch(i.V.kind){case"message":o[d]=i.V.T.fromJson(c,n);break;case"enum":const l=An(i.V.T,c,n.ignoreUnknownFields,!0);l!==zi&&(o[d]=l);break;case"scalar":try{o[d]=ki(i.V.T,c,ut.BIGINT,!0)}catch(u){let h="cannot decode map value for field ".concat(s.typeName,".").concat(i.name," from JSON: ").concat(Ke(e));throw u instanceof Error&&u.message.length>0&&(h+=": ".concat(u.message)),new Error(h)}break}}}else switch(i.oneof&&(t=t[i.oneof.localName]={case:r},r="value"),i.kind){case"message":const o=i.T;if(e===null&&o.typeName!="google.protobuf.Value")return;let a=t[r];Rt(a)?a.fromJson(e,n):(t[r]=a=o.fromJson(e,n),o.fieldWrapper&&!i.oneof&&(t[r]=o.fieldWrapper.unwrapField(a)));break;case"enum":const c=An(i.T,e,n.ignoreUnknownFields,!1);switch(c){case rn:Vr(i,t);break;case zi:break;default:t[r]=c;break}break;case"scalar":try{const d=ki(i.T,e,i.L,!1);switch(d){case rn:Vr(i,t);break;default:t[r]=d;break}}catch(d){let l="cannot decode field ".concat(s.typeName,".").concat(i.name," from JSON: ").concat(Ke(e));throw d instanceof Error&&d.message.length>0&&(l+=": ".concat(d.message)),new Error(l)}break}}function xu(t,e){if(t===E.BOOL)switch(e){case"true":e=!0;break;case"false":e=!1;break}return ki(t,e,ut.BIGINT,!0).toString()}function ki(t,e,i,n){if(e===null)return n?Zt(t,i):rn;switch(t){case E.DOUBLE:case E.FLOAT:if(e==="NaN")return Number.NaN;if(e==="Infinity")return Number.POSITIVE_INFINITY;if(e==="-Infinity")return Number.NEGATIVE_INFINITY;if(e===""||typeof e=="string"&&e.trim().length!==e.length||typeof e!="string"&&typeof e!="number")break;const s=Number(e);if(Number.isNaN(s)||!Number.isFinite(s))break;return t==E.FLOAT&&ia(s),s;case E.INT32:case E.FIXED32:case E.SFIXED32:case E.SINT32:case E.UINT32:let r;if(typeof e=="number"?r=e:typeof e=="string"&&e.length>0&&e.trim().length===e.length&&(r=Number(e)),r===void 0)break;return t==E.UINT32||t==E.FIXED32?us(r):Bi(r),r;case E.INT64:case E.SFIXED64:case E.SINT64:if(typeof e!="number"&&typeof e!="string")break;const o=de.parse(e);return i?o.toString():o;case E.FIXED64:case E.UINT64:if(typeof e!="number"&&typeof e!="string")break;const a=de.uParse(e);return i?a.toString():a;case E.BOOL:if(typeof e!="boolean")break;return e;case E.STRING:if(typeof e!="string")break;try{encodeURIComponent(e)}catch{throw new Error("invalid UTF8")}return e;case E.BYTES:if(e==="")return new Uint8Array(0);if(typeof e!="string")break;return ua.dec(e)}throw new Error}function An(t,e,i,n){if(e===null)return t.typeName=="google.protobuf.NullValue"?0:n?t.values[0].no:rn;switch(typeof e){case"number":if(Number.isInteger(e))return e;break;case"string":const s=t.findName(e);if(s!==void 0)return s.no;if(i)return zi;break}throw new Error("cannot decode enum ".concat(t.typeName," from JSON: ").concat(Ke(e)))}function Lu(t){return t.repeated||t.kind=="map"?!0:!(t.oneof||t.kind=="message"||t.opt||t.req)}function qr(t,e,i){if(t.kind=="map"){ue(typeof e=="object"&&e!=null);const n={},s=Object.entries(e);switch(t.V.kind){case"scalar":for(const[o,a]of s)n[o.toString()]=qi(t.V.T,a);break;case"message":for(const[o,a]of s)n[o.toString()]=a.toJson(i);break;case"enum":const r=t.V.T;for(const[o,a]of s)n[o.toString()]=xn(r,a,i.enumAsInteger);break}return i.emitDefaultValues||s.length>0?n:void 0}if(t.repeated){ue(Array.isArray(e));const n=[];switch(t.kind){case"scalar":for(let s=0;s<e.length;s++)n.push(qi(t.T,e[s]));break;case"enum":for(let s=0;s<e.length;s++)n.push(xn(t.T,e[s],i.enumAsInteger));break;case"message":for(let s=0;s<e.length;s++)n.push(e[s].toJson(i));break}return i.emitDefaultValues||n.length>0?n:void 0}switch(t.kind){case"scalar":return qi(t.T,e);case"enum":return xn(t.T,e,i.enumAsInteger);case"message":return fa(t.T,e).toJson(i)}}function xn(t,e,i){var n;if(ue(typeof e=="number"),t.typeName=="google.protobuf.NullValue")return null;if(i)return e;const s=t.findNumber(e);return(n=s==null?void 0:s.name)!==null&&n!==void 0?n:e}function qi(t,e){switch(t){case E.INT32:case E.SFIXED32:case E.SINT32:case E.FIXED32:case E.UINT32:return ue(typeof e=="number"),e;case E.FLOAT:case E.DOUBLE:return ue(typeof e=="number"),Number.isNaN(e)?"NaN":e===Number.POSITIVE_INFINITY?"Infinity":e===Number.NEGATIVE_INFINITY?"-Infinity":e;case E.STRING:return ue(typeof e=="string"),e;case E.BOOL:return ue(typeof e=="boolean"),e;case E.UINT64:case E.FIXED64:case E.INT64:case E.SFIXED64:case E.SINT64:return ue(typeof e=="bigint"||typeof e=="string"||typeof e=="number"),e.toString();case E.BYTES:return ue(e instanceof Uint8Array),ua.enc(e)}}const Ft=Symbol("@bufbuild/protobuf/unknown-fields"),Gr={readUnknownFields:!0,readerFactory:t=>new Eu(t)},Wr={writeUnknownFields:!0,writerFactory:()=>new Pu};function Nu(t){return t?Object.assign(Object.assign({},Gr),t):Gr}function Uu(t){return t?Object.assign(Object.assign({},Wr),t):Wr}function Fu(){return{makeReadOptions:Nu,makeWriteOptions:Uu,listUnknownFields(t){var e;return(e=t[Ft])!==null&&e!==void 0?e:[]},discardUnknownFields(t){delete t[Ft]},writeUnknownFields(t,e){const n=t[Ft];if(n)for(const s of n)e.tag(s.no,s.wireType).raw(s.data)},onUnknownField(t,e,i,n){const s=t;Array.isArray(s[Ft])||(s[Ft]=[]),s[Ft].push({no:e,wireType:i,data:n})},readMessage(t,e,i,n,s){const r=t.getType(),o=s?e.len:e.pos+i;let a,c;for(;e.pos<o&&([a,c]=e.tag(),!(s===!0&&c==me.EndGroup));){const d=r.fields.find(a);if(!d){const l=e.skip(c,a);n.readUnknownFields&&this.onUnknownField(t,a,c,l);continue}Hr(t,e,d,c,n)}if(s&&(c!=me.EndGroup||a!==i))throw new Error("invalid end group tag")},readField:Hr,writeMessage(t,e,i){const n=t.getType();for(const s of n.fields.byNumber()){if(!pa(s,t)){if(s.req)throw new Error("cannot encode field ".concat(n.typeName,".").concat(s.name," to binary: required field not set"));continue}const r=s.oneof?t[s.oneof.localName].value:t[s.localName];Kr(s,r,e,i)}return i.writeUnknownFields&&this.writeUnknownFields(t,e),e},writeField(t,e,i,n){e!==void 0&&Kr(t,e,i,n)}}}function Hr(t,e,i,n,s){let{repeated:r,localName:o}=i;switch(i.oneof&&(t=t[i.oneof.localName],t.case!=o&&delete t.value,t.case=o,o="value"),i.kind){case"scalar":case"enum":const a=i.kind=="enum"?E.INT32:i.T;let c=on;if(i.kind=="scalar"&&i.L>0&&(c=Bu),r){let h=t[o];if(n==me.LengthDelimited&&a!=E.STRING&&a!=E.BYTES){let g=e.uint32()+e.pos;for(;e.pos<g;)h.push(c(e,a))}else h.push(c(e,a))}else t[o]=c(e,a);break;case"message":const d=i.T;r?t[o].push(Gi(e,new d,s,i)):Rt(t[o])?Gi(e,t[o],s,i):(t[o]=Gi(e,new d,s,i),d.fieldWrapper&&!i.oneof&&!i.repeated&&(t[o]=d.fieldWrapper.unwrapField(t[o])));break;case"map":let[l,u]=Vu(i,e,s);t[o][l]=u;break}}function Gi(t,e,i,n){const s=e.getType().runtime.bin,r=n==null?void 0:n.delimited;return s.readMessage(e,t,r?n.no:t.uint32(),i,r),e}function Vu(t,e,i){const n=e.uint32(),s=e.pos+n;let r,o;for(;e.pos<s;){const[a]=e.tag();switch(a){case 1:r=on(e,t.K);break;case 2:switch(t.V.kind){case"scalar":o=on(e,t.V.T);break;case"enum":o=e.int32();break;case"message":o=Gi(e,new t.V.T,i,void 0);break}break}}if(r===void 0&&(r=Zt(t.K,ut.BIGINT)),typeof r!="string"&&typeof r!="number"&&(r=r.toString()),o===void 0)switch(t.V.kind){case"scalar":o=Zt(t.V.T,ut.BIGINT);break;case"enum":o=t.V.T.values[0].no;break;case"message":o=new t.V.T;break}return[r,o]}function Bu(t,e){const i=on(t,e);return typeof i=="bigint"?i.toString():i}function on(t,e){switch(e){case E.STRING:return t.string();case E.BOOL:return t.bool();case E.DOUBLE:return t.double();case E.FLOAT:return t.float();case E.INT32:return t.int32();case E.INT64:return t.int64();case E.UINT64:return t.uint64();case E.FIXED64:return t.fixed64();case E.BYTES:return t.bytes();case E.FIXED32:return t.fixed32();case E.SFIXED32:return t.sfixed32();case E.SFIXED64:return t.sfixed64();case E.SINT64:return t.sint64();case E.UINT32:return t.uint32();case E.SINT32:return t.sint32()}}function Kr(t,e,i,n){ue(e!==void 0);const s=t.repeated;switch(t.kind){case"scalar":case"enum":let r=t.kind=="enum"?E.INT32:t.T;if(s)if(ue(Array.isArray(e)),t.packed)zu(i,r,t.no,e);else for(const o of e)Ci(i,r,t.no,o);else Ci(i,r,t.no,e);break;case"message":if(s){ue(Array.isArray(e));for(const o of e)$r(i,n,t,o)}else $r(i,n,t,e);break;case"map":ue(typeof e=="object"&&e!=null);for(const[o,a]of Object.entries(e))ju(i,n,t,o,a);break}}function ju(t,e,i,n,s){t.tag(i.no,me.LengthDelimited),t.fork();let r=n;switch(i.K){case E.INT32:case E.FIXED32:case E.UINT32:case E.SFIXED32:case E.SINT32:r=Number.parseInt(n);break;case E.BOOL:ue(n=="true"||n=="false"),r=n=="true";break}switch(Ci(t,i.K,1,r),i.V.kind){case"scalar":Ci(t,i.V.T,2,s);break;case"enum":Ci(t,E.INT32,2,s);break;case"message":ue(s!==void 0),t.tag(2,me.LengthDelimited).bytes(s.toBinary(e));break}t.join()}function $r(t,e,i,n){const s=fa(i.T,n);i.delimited?t.tag(i.no,me.StartGroup).raw(s.toBinary(e)).tag(i.no,me.EndGroup):t.tag(i.no,me.LengthDelimited).bytes(s.toBinary(e))}function Ci(t,e,i,n){ue(n!==void 0);let[s,r]=ga(e);t.tag(i,s)[r](n)}function zu(t,e,i,n){if(!n.length)return;t.tag(i,me.LengthDelimited).fork();let[,s]=ga(e);for(let r=0;r<n.length;r++)t[s](n[r]);t.join()}function ga(t){let e=me.Varint;switch(t){case E.BYTES:case E.STRING:e=me.LengthDelimited;break;case E.DOUBLE:case E.FIXED64:case E.SFIXED64:e=me.Bit64;break;case E.FIXED32:case E.SFIXED32:case E.FLOAT:e=me.Bit32;break}const i=E[t].toLowerCase();return[e,i]}function qu(){return{setEnumType:sa,initPartial(t,e){if(t===void 0)return;const i=e.getType();for(const n of i.fields.byMember()){const s=n.localName,r=e,o=t;if(o[s]!=null)switch(n.kind){case"oneof":const a=o[s].case;if(a===void 0)continue;const c=n.findField(a);let d=o[s].value;c&&c.kind=="message"&&!Rt(d,c.T)?d=new c.T(d):c&&c.kind==="scalar"&&c.T===E.BYTES&&(d=di(d)),r[s]={case:a,value:d};break;case"scalar":case"enum":let l=o[s];n.T===E.BYTES&&(l=n.repeated?l.map(di):di(l)),r[s]=l;break;case"map":switch(n.V.kind){case"scalar":case"enum":if(n.V.T===E.BYTES)for(const[p,g]of Object.entries(o[s]))r[s][p]=di(g);else Object.assign(r[s],o[s]);break;case"message":const h=n.V.T;for(const p of Object.keys(o[s])){let g=o[s][p];h.fieldWrapper||(g=new h(g)),r[s][p]=g}break}break;case"message":const u=n.T;if(n.repeated)r[s]=o[s].map(h=>Rt(h,u)?h:new u(h));else{const h=o[s];u.fieldWrapper?u.typeName==="google.protobuf.BytesValue"?r[s]=di(h):r[s]=h:r[s]=Rt(h,u)?h:new u(h)}break}}},equals(t,e,i){return e===i?!0:!e||!i?!1:t.fields.byMember().every(n=>{const s=e[n.localName],r=i[n.localName];if(n.repeated){if(s.length!==r.length)return!1;switch(n.kind){case"message":return s.every((o,a)=>n.T.equals(o,r[a]));case"scalar":return s.every((o,a)=>st(n.T,o,r[a]));case"enum":return s.every((o,a)=>st(E.INT32,o,r[a]))}throw new Error("repeated cannot contain ".concat(n.kind))}switch(n.kind){case"message":return n.T.equals(s,r);case"enum":return st(E.INT32,s,r);case"scalar":return st(n.T,s,r);case"oneof":if(s.case!==r.case)return!1;const o=n.findField(s.case);if(o===void 0)return!0;switch(o.kind){case"message":return o.T.equals(s.value,r.value);case"enum":return st(E.INT32,s.value,r.value);case"scalar":return st(o.T,s.value,r.value)}throw new Error("oneof cannot contain ".concat(o.kind));case"map":const a=Object.keys(s).concat(Object.keys(r));switch(n.V.kind){case"message":const c=n.V.T;return a.every(l=>c.equals(s[l],r[l]));case"enum":return a.every(l=>st(E.INT32,s[l],r[l]));case"scalar":const d=n.V.T;return a.every(l=>st(d,s[l],r[l]))}break}})},clone(t){const e=t.getType(),i=new e,n=i;for(const s of e.fields.byMember()){const r=t[s.localName];let o;if(s.repeated)o=r.map(Li);else if(s.kind=="map"){o=n[s.localName];for(const[a,c]of Object.entries(r))o[a]=Li(c)}else s.kind=="oneof"?o=s.findField(r.case)?{case:r.case,value:Li(r.value)}:{case:void 0}:o=Li(r);n[s.localName]=o}for(const s of e.runtime.bin.listUnknownFields(t))e.runtime.bin.onUnknownField(n,s.no,s.wireType,s.data);return i}}}function Li(t){if(t===void 0)return t;if(Rt(t))return t.clone();if(t instanceof Uint8Array){const e=new Uint8Array(t.byteLength);return e.set(t),e}return t}function di(t){return t instanceof Uint8Array?t:new Uint8Array(t)}function Gu(t,e,i){return{syntax:t,json:Au(),bin:Fu(),util:Object.assign(Object.assign({},qu()),{newFieldList:e,initFields:i}),makeMessageType(n,s,r){return bu(this,n,s,r)},makeEnum:vu,makeEnumType:ra,getEnumType:gu,makeExtension(n,s,r){return Iu(this,n,s,r)}}}class Wu{constructor(e,i){this._fields=e,this._normalizer=i}findJsonName(e){if(!this.jsonNames){const i={};for(const n of this.list())i[n.jsonName]=i[n.name]=n;this.jsonNames=i}return this.jsonNames[e]}find(e){if(!this.numbers){const i={};for(const n of this.list())i[n.no]=n;this.numbers=i}return this.numbers[e]}list(){return this.all||(this.all=this._normalizer(this._fields)),this.all}byNumber(){return this.numbersAsc||(this.numbersAsc=this.list().concat().sort((e,i)=>e.no-i.no)),this.numbersAsc}byMember(){if(!this.members){this.members=[];const e=this.members;let i;for(const n of this.list())n.oneof?n.oneof!==i&&(i=n.oneof,e.push(i)):e.push(n)}return this.members}}function va(t,e){const i=ba(t);return e?i:Yu(Qu(i))}function Hu(t){return va(t,!1)}const Ku=ba;function ba(t){let e=!1;const i=[];for(let n=0;n<t.length;n++){let s=t.charAt(n);switch(s){case"_":e=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":i.push(s),e=!1;break;default:e&&(e=!1,s=s.toUpperCase()),i.push(s);break}}return i.join("")}const $u=new Set(["constructor","toString","toJSON","valueOf"]),Ju=new Set(["getType","clone","equals","fromBinary","fromJson","fromJsonString","toBinary","toJson","toJsonString","toObject"]),ya=t=>"".concat(t,"$"),Qu=t=>Ju.has(t)?ya(t):t,Yu=t=>$u.has(t)?ya(t):t;class Xu{constructor(e){this.kind="oneof",this.repeated=!1,this.packed=!1,this.opt=!1,this.req=!1,this.default=void 0,this.fields=[],this.name=e,this.localName=Hu(e)}addField(e){ue(e.oneof===this,"field ".concat(e.name," not one of ").concat(this.name)),this.fields.push(e)}findField(e){if(!this._lookup){this._lookup=Object.create(null);for(let i=0;i<this.fields.length;i++)this._lookup[this.fields[i].localName]=this.fields[i]}return this._lookup[e]}}function Zu(t,e){var i,n,s,r,o,a;const c=[];let d;for(const l of typeof t=="function"?t():t){const u=l;if(u.localName=va(l.name,l.oneof!==void 0),u.jsonName=(i=l.jsonName)!==null&&i!==void 0?i:Ku(l.name),u.repeated=(n=l.repeated)!==null&&n!==void 0?n:!1,l.kind=="scalar"&&(u.L=(s=l.L)!==null&&s!==void 0?s:ut.BIGINT),u.delimited=(r=l.delimited)!==null&&r!==void 0?r:!1,u.req=(o=l.req)!==null&&o!==void 0?o:!1,u.opt=(a=l.opt)!==null&&a!==void 0?a:!1,l.packed===void 0&&(u.packed=l.kind=="enum"||l.kind=="scalar"&&l.T!=E.BYTES&&l.T!=E.STRING),l.oneof!==void 0){const h=typeof l.oneof=="string"?l.oneof:l.oneof.name;(!d||d.name!=h)&&(d=new Xu(h)),u.oneof=d,d.addField(u)}c.push(u)}return c}const v=Gu("proto3",t=>new Wu(t,e=>Zu(e)),t=>{for(const e of t.getType().fields.byMember()){if(e.opt)continue;const i=e.localName,n=t;if(e.repeated){n[i]=[];continue}switch(e.kind){case"oneof":n[i]={case:void 0};break;case"enum":n[i]=0;break;case"map":n[i]={};break;case"scalar":n[i]=Zt(e.T,e.L);break}}});class we extends Ks{constructor(e){super(),this.seconds=de.zero,this.nanos=0,v.util.initPartial(e,this)}fromJson(e,i){if(typeof e!="string")throw new Error("cannot decode google.protobuf.Timestamp from JSON: ".concat(v.json.debug(e)));const n=e.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!n)throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");const s=Date.parse(n[1]+"-"+n[2]+"-"+n[3]+"T"+n[4]+":"+n[5]+":"+n[6]+(n[8]?n[8]:"Z"));if(Number.isNaN(s))throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");if(s<Date.parse("0001-01-01T00:00:00Z")||s>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot decode message google.protobuf.Timestamp from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");return this.seconds=de.parse(s/1e3),this.nanos=0,n[7]&&(this.nanos=parseInt("1"+n[7]+"0".repeat(9-n[7].length))-1e9),this}toJson(e){const i=Number(this.seconds)*1e3;if(i<Date.parse("0001-01-01T00:00:00Z")||i>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot encode google.protobuf.Timestamp to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");if(this.nanos<0)throw new Error("cannot encode google.protobuf.Timestamp to JSON: nanos must not be negative");let n="Z";if(this.nanos>0){const s=(this.nanos+1e9).toString().substring(1);s.substring(3)==="000000"?n="."+s.substring(0,3)+"Z":s.substring(6)==="000"?n="."+s.substring(0,6)+"Z":n="."+s+"Z"}return new Date(i).toISOString().replace(".000Z",n)}toDate(){return new Date(Number(this.seconds)*1e3+Math.ceil(this.nanos/1e6))}static now(){return we.fromDate(new Date)}static fromDate(e){const i=e.getTime();return new we({seconds:de.parse(Math.floor(i/1e3)),nanos:i%1e3*1e6})}static fromBinary(e,i){return new we().fromBinary(e,i)}static fromJson(e,i){return new we().fromJson(e,i)}static fromJsonString(e,i){return new we().fromJsonString(e,i)}static equals(e,i){return v.util.equals(we,e,i)}}we.runtime=v;we.typeName="google.protobuf.Timestamp";we.fields=v.util.newFieldList(()=>[{no:1,name:"seconds",kind:"scalar",T:3},{no:2,name:"nanos",kind:"scalar",T:5}]);const eh=v.makeMessageType("livekit.MetricsBatch",()=>[{no:1,name:"timestamp_ms",kind:"scalar",T:3},{no:2,name:"normalized_timestamp",kind:"message",T:we},{no:3,name:"str_data",kind:"scalar",T:9,repeated:!0},{no:4,name:"time_series",kind:"message",T:th,repeated:!0},{no:5,name:"events",kind:"message",T:nh,repeated:!0}]),th=v.makeMessageType("livekit.TimeSeriesMetric",()=>[{no:1,name:"label",kind:"scalar",T:13},{no:2,name:"participant_identity",kind:"scalar",T:13},{no:3,name:"track_sid",kind:"scalar",T:13},{no:4,name:"samples",kind:"message",T:ih,repeated:!0},{no:5,name:"rid",kind:"scalar",T:13}]),ih=v.makeMessageType("livekit.MetricSample",()=>[{no:1,name:"timestamp_ms",kind:"scalar",T:3},{no:2,name:"normalized_timestamp",kind:"message",T:we},{no:3,name:"value",kind:"scalar",T:2}]),nh=v.makeMessageType("livekit.EventMetric",()=>[{no:1,name:"label",kind:"scalar",T:13},{no:2,name:"participant_identity",kind:"scalar",T:13},{no:3,name:"track_sid",kind:"scalar",T:13},{no:4,name:"start_timestamp_ms",kind:"scalar",T:3},{no:5,name:"end_timestamp_ms",kind:"scalar",T:3,opt:!0},{no:6,name:"normalized_start_timestamp",kind:"message",T:we},{no:7,name:"normalized_end_timestamp",kind:"message",T:we,opt:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"rid",kind:"scalar",T:13}]),Sa=v.makeEnum("livekit.BackupCodecPolicy",[{no:0,name:"PREFER_REGRESSION"},{no:1,name:"SIMULCAST"},{no:2,name:"REGRESSION"}]),ze=v.makeEnum("livekit.TrackType",[{no:0,name:"AUDIO"},{no:1,name:"VIDEO"},{no:2,name:"DATA"}]),pe=v.makeEnum("livekit.TrackSource",[{no:0,name:"UNKNOWN"},{no:1,name:"CAMERA"},{no:2,name:"MICROPHONE"},{no:3,name:"SCREEN_SHARE"},{no:4,name:"SCREEN_SHARE_AUDIO"}]),Wt=v.makeEnum("livekit.VideoQuality",[{no:0,name:"LOW"},{no:1,name:"MEDIUM"},{no:2,name:"HIGH"},{no:3,name:"OFF"}]),gi=v.makeEnum("livekit.ConnectionQuality",[{no:0,name:"POOR"},{no:1,name:"GOOD"},{no:2,name:"EXCELLENT"},{no:3,name:"LOST"}]),Ri=v.makeEnum("livekit.ClientConfigSetting",[{no:0,name:"UNSET"},{no:1,name:"DISABLED"},{no:2,name:"ENABLED"}]),Ge=v.makeEnum("livekit.DisconnectReason",[{no:0,name:"UNKNOWN_REASON"},{no:1,name:"CLIENT_INITIATED"},{no:2,name:"DUPLICATE_IDENTITY"},{no:3,name:"SERVER_SHUTDOWN"},{no:4,name:"PARTICIPANT_REMOVED"},{no:5,name:"ROOM_DELETED"},{no:6,name:"STATE_MISMATCH"},{no:7,name:"JOIN_FAILURE"},{no:8,name:"MIGRATION"},{no:9,name:"SIGNAL_CLOSE"},{no:10,name:"ROOM_CLOSED"},{no:11,name:"USER_UNAVAILABLE"},{no:12,name:"USER_REJECTED"},{no:13,name:"SIP_TRUNK_FAILURE"},{no:14,name:"CONNECTION_TIMEOUT"}]),Vt=v.makeEnum("livekit.ReconnectReason",[{no:0,name:"RR_UNKNOWN"},{no:1,name:"RR_SIGNAL_DISCONNECTED"},{no:2,name:"RR_PUBLISHER_FAILED"},{no:3,name:"RR_SUBSCRIBER_FAILED"},{no:4,name:"RR_SWITCH_CANDIDATE"}]),sh=v.makeEnum("livekit.SubscriptionError",[{no:0,name:"SE_UNKNOWN"},{no:1,name:"SE_CODEC_UNSUPPORTED"},{no:2,name:"SE_TRACK_NOTFOUND"}]),Se=v.makeEnum("livekit.AudioTrackFeature",[{no:0,name:"TF_STEREO"},{no:1,name:"TF_NO_DTX"},{no:2,name:"TF_AUTO_GAIN_CONTROL"},{no:3,name:"TF_ECHO_CANCELLATION"},{no:4,name:"TF_NOISE_SUPPRESSION"},{no:5,name:"TF_ENHANCED_NOISE_CANCELLATION"},{no:6,name:"TF_PRECONNECT_BUFFER"}]),pn=v.makeMessageType("livekit.Room",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"empty_timeout",kind:"scalar",T:13},{no:14,name:"departure_timeout",kind:"scalar",T:13},{no:4,name:"max_participants",kind:"scalar",T:13},{no:5,name:"creation_time",kind:"scalar",T:3},{no:15,name:"creation_time_ms",kind:"scalar",T:3},{no:6,name:"turn_password",kind:"scalar",T:9},{no:7,name:"enabled_codecs",kind:"message",T:an,repeated:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"num_participants",kind:"scalar",T:13},{no:11,name:"num_publishers",kind:"scalar",T:13},{no:10,name:"active_recording",kind:"scalar",T:8},{no:13,name:"version",kind:"message",T:Oa}]),an=v.makeMessageType("livekit.Codec",()=>[{no:1,name:"mime",kind:"scalar",T:9},{no:2,name:"fmtp_line",kind:"scalar",T:9}]),rh=v.makeMessageType("livekit.ParticipantPermission",()=>[{no:1,name:"can_subscribe",kind:"scalar",T:8},{no:2,name:"can_publish",kind:"scalar",T:8},{no:3,name:"can_publish_data",kind:"scalar",T:8},{no:9,name:"can_publish_sources",kind:"enum",T:v.getEnumType(pe),repeated:!0},{no:7,name:"hidden",kind:"scalar",T:8},{no:8,name:"recorder",kind:"scalar",T:8},{no:10,name:"can_update_metadata",kind:"scalar",T:8},{no:11,name:"agent",kind:"scalar",T:8},{no:12,name:"can_subscribe_metrics",kind:"scalar",T:8}]),_t=v.makeMessageType("livekit.ParticipantInfo",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"identity",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:v.getEnumType(Ht)},{no:4,name:"tracks",kind:"message",T:jt,repeated:!0},{no:5,name:"metadata",kind:"scalar",T:9},{no:6,name:"joined_at",kind:"scalar",T:3},{no:17,name:"joined_at_ms",kind:"scalar",T:3},{no:9,name:"name",kind:"scalar",T:9},{no:10,name:"version",kind:"scalar",T:13},{no:11,name:"permission",kind:"message",T:rh},{no:12,name:"region",kind:"scalar",T:9},{no:13,name:"is_publisher",kind:"scalar",T:8},{no:14,name:"kind",kind:"enum",T:v.getEnumType(wi)},{no:15,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:16,name:"disconnect_reason",kind:"enum",T:v.getEnumType(Ge)},{no:18,name:"kind_details",kind:"enum",T:v.getEnumType(oh),repeated:!0}]),Ht=v.makeEnum("livekit.ParticipantInfo.State",[{no:0,name:"JOINING"},{no:1,name:"JOINED"},{no:2,name:"ACTIVE"},{no:3,name:"DISCONNECTED"}]),wi=v.makeEnum("livekit.ParticipantInfo.Kind",[{no:0,name:"STANDARD"},{no:1,name:"INGRESS"},{no:2,name:"EGRESS"},{no:3,name:"SIP"},{no:4,name:"AGENT"}]),oh=v.makeEnum("livekit.ParticipantInfo.KindDetail",[{no:0,name:"CLOUD_AGENT"},{no:1,name:"FORWARDED"}]),xe=v.makeEnum("livekit.Encryption.Type",[{no:0,name:"NONE"},{no:1,name:"GCM"},{no:2,name:"CUSTOM"}]),ah=v.makeMessageType("livekit.SimulcastCodecInfo",()=>[{no:1,name:"mime_type",kind:"scalar",T:9},{no:2,name:"mid",kind:"scalar",T:9},{no:3,name:"cid",kind:"scalar",T:9},{no:4,name:"layers",kind:"message",T:wt,repeated:!0}]),jt=v.makeMessageType("livekit.TrackInfo",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"type",kind:"enum",T:v.getEnumType(ze)},{no:3,name:"name",kind:"scalar",T:9},{no:4,name:"muted",kind:"scalar",T:8},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"simulcast",kind:"scalar",T:8},{no:8,name:"disable_dtx",kind:"scalar",T:8},{no:9,name:"source",kind:"enum",T:v.getEnumType(pe)},{no:10,name:"layers",kind:"message",T:wt,repeated:!0},{no:11,name:"mime_type",kind:"scalar",T:9},{no:12,name:"mid",kind:"scalar",T:9},{no:13,name:"codecs",kind:"message",T:ah,repeated:!0},{no:14,name:"stereo",kind:"scalar",T:8},{no:15,name:"disable_red",kind:"scalar",T:8},{no:16,name:"encryption",kind:"enum",T:v.getEnumType(xe)},{no:17,name:"stream",kind:"scalar",T:9},{no:18,name:"version",kind:"message",T:Oa},{no:19,name:"audio_features",kind:"enum",T:v.getEnumType(Se),repeated:!0},{no:20,name:"backup_codec_policy",kind:"enum",T:v.getEnumType(Sa)}]),wt=v.makeMessageType("livekit.VideoLayer",()=>[{no:1,name:"quality",kind:"enum",T:v.getEnumType(Wt)},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13},{no:4,name:"bitrate",kind:"scalar",T:13},{no:5,name:"ssrc",kind:"scalar",T:13}]),Ie=v.makeMessageType("livekit.DataPacket",()=>[{no:1,name:"kind",kind:"enum",T:v.getEnumType(ee)},{no:4,name:"participant_identity",kind:"scalar",T:9},{no:5,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:2,name:"user",kind:"message",T:Ca,oneof:"value"},{no:3,name:"speaker",kind:"message",T:ch,oneof:"value"},{no:6,name:"sip_dtmf",kind:"message",T:Ta,oneof:"value"},{no:7,name:"transcription",kind:"message",T:dh,oneof:"value"},{no:8,name:"metrics",kind:"message",T:eh,oneof:"value"},{no:9,name:"chat_message",kind:"message",T:hs,oneof:"value"},{no:10,name:"rpc_request",kind:"message",T:Pa,oneof:"value"},{no:11,name:"rpc_ack",kind:"message",T:Ea,oneof:"value"},{no:12,name:"rpc_response",kind:"message",T:Ia,oneof:"value"},{no:13,name:"stream_header",kind:"message",T:ps,oneof:"value"},{no:14,name:"stream_chunk",kind:"message",T:fs,oneof:"value"},{no:15,name:"stream_trailer",kind:"message",T:gs,oneof:"value"}]),ee=v.makeEnum("livekit.DataPacket.Kind",[{no:0,name:"RELIABLE"},{no:1,name:"LOSSY"}]),ch=v.makeMessageType("livekit.ActiveSpeakerUpdate",()=>[{no:1,name:"speakers",kind:"message",T:ka,repeated:!0}]),ka=v.makeMessageType("livekit.SpeakerInfo",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"level",kind:"scalar",T:2},{no:3,name:"active",kind:"scalar",T:8}]),Ca=v.makeMessageType("livekit.UserPacket",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:5,name:"participant_identity",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:12},{no:3,name:"destination_sids",kind:"scalar",T:9,repeated:!0},{no:6,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:4,name:"topic",kind:"scalar",T:9,opt:!0},{no:8,name:"id",kind:"scalar",T:9,opt:!0},{no:9,name:"start_time",kind:"scalar",T:4,opt:!0},{no:10,name:"end_time",kind:"scalar",T:4,opt:!0},{no:11,name:"nonce",kind:"scalar",T:12}]),Ta=v.makeMessageType("livekit.SipDTMF",()=>[{no:3,name:"code",kind:"scalar",T:13},{no:4,name:"digit",kind:"scalar",T:9}]),dh=v.makeMessageType("livekit.Transcription",()=>[{no:2,name:"transcribed_participant_identity",kind:"scalar",T:9},{no:3,name:"track_id",kind:"scalar",T:9},{no:4,name:"segments",kind:"message",T:lh,repeated:!0}]),lh=v.makeMessageType("livekit.TranscriptionSegment",()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"text",kind:"scalar",T:9},{no:3,name:"start_time",kind:"scalar",T:4},{no:4,name:"end_time",kind:"scalar",T:4},{no:5,name:"final",kind:"scalar",T:8},{no:6,name:"language",kind:"scalar",T:9}]),hs=v.makeMessageType("livekit.ChatMessage",()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"timestamp",kind:"scalar",T:3},{no:3,name:"edit_timestamp",kind:"scalar",T:3,opt:!0},{no:4,name:"message",kind:"scalar",T:9},{no:5,name:"deleted",kind:"scalar",T:8},{no:6,name:"generated",kind:"scalar",T:8}]),Pa=v.makeMessageType("livekit.RpcRequest",()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"method",kind:"scalar",T:9},{no:3,name:"payload",kind:"scalar",T:9},{no:4,name:"response_timeout_ms",kind:"scalar",T:13},{no:5,name:"version",kind:"scalar",T:13}]),Ea=v.makeMessageType("livekit.RpcAck",()=>[{no:1,name:"request_id",kind:"scalar",T:9}]),Ia=v.makeMessageType("livekit.RpcResponse",()=>[{no:1,name:"request_id",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:9,oneof:"value"},{no:3,name:"error",kind:"message",T:Ra,oneof:"value"}]),Ra=v.makeMessageType("livekit.RpcError",()=>[{no:1,name:"code",kind:"scalar",T:13},{no:2,name:"message",kind:"scalar",T:9},{no:3,name:"data",kind:"scalar",T:9}]),wa=v.makeMessageType("livekit.ParticipantTracks",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sids",kind:"scalar",T:9,repeated:!0}]),uh=v.makeMessageType("livekit.ServerInfo",()=>[{no:1,name:"edition",kind:"enum",T:v.getEnumType(_a)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"region",kind:"scalar",T:9},{no:5,name:"node_id",kind:"scalar",T:9},{no:6,name:"debug_info",kind:"scalar",T:9},{no:7,name:"agent_protocol",kind:"scalar",T:5}]),_a=v.makeEnum("livekit.ServerInfo.Edition",[{no:0,name:"Standard"},{no:1,name:"Cloud"}]),hh=v.makeMessageType("livekit.ClientInfo",()=>[{no:1,name:"sdk",kind:"enum",T:v.getEnumType(Ma)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"os",kind:"scalar",T:9},{no:5,name:"os_version",kind:"scalar",T:9},{no:6,name:"device_model",kind:"scalar",T:9},{no:7,name:"browser",kind:"scalar",T:9},{no:8,name:"browser_version",kind:"scalar",T:9},{no:9,name:"address",kind:"scalar",T:9},{no:10,name:"network",kind:"scalar",T:9},{no:11,name:"other_sdks",kind:"scalar",T:9}]),Ma=v.makeEnum("livekit.ClientInfo.SDK",[{no:0,name:"UNKNOWN"},{no:1,name:"JS"},{no:2,name:"SWIFT"},{no:3,name:"ANDROID"},{no:4,name:"FLUTTER"},{no:5,name:"GO"},{no:6,name:"UNITY"},{no:7,name:"REACT_NATIVE"},{no:8,name:"RUST"},{no:9,name:"PYTHON"},{no:10,name:"CPP"},{no:11,name:"UNITY_WEB"},{no:12,name:"NODE"},{no:13,name:"UNREAL"}]),Da=v.makeMessageType("livekit.ClientConfiguration",()=>[{no:1,name:"video",kind:"message",T:Jr},{no:2,name:"screen",kind:"message",T:Jr},{no:3,name:"resume_connection",kind:"enum",T:v.getEnumType(Ri)},{no:4,name:"disabled_codecs",kind:"message",T:mh},{no:5,name:"force_relay",kind:"enum",T:v.getEnumType(Ri)}]),Jr=v.makeMessageType("livekit.VideoConfiguration",()=>[{no:1,name:"hardware_encoder",kind:"enum",T:v.getEnumType(Ri)}]),mh=v.makeMessageType("livekit.DisabledCodecs",()=>[{no:1,name:"codecs",kind:"message",T:an,repeated:!0},{no:2,name:"publish",kind:"message",T:an,repeated:!0}]),Oa=v.makeMessageType("livekit.TimedVersion",()=>[{no:1,name:"unix_micro",kind:"scalar",T:3},{no:2,name:"ticks",kind:"scalar",T:5}]),ms=v.makeEnum("livekit.DataStream.OperationType",[{no:0,name:"CREATE"},{no:1,name:"UPDATE"},{no:2,name:"DELETE"},{no:3,name:"REACTION"}]),Aa=v.makeMessageType("livekit.DataStream.TextHeader",()=>[{no:1,name:"operation_type",kind:"enum",T:v.getEnumType(ms)},{no:2,name:"version",kind:"scalar",T:5},{no:3,name:"reply_to_stream_id",kind:"scalar",T:9},{no:4,name:"attached_stream_ids",kind:"scalar",T:9,repeated:!0},{no:5,name:"generated",kind:"scalar",T:8}],{localName:"DataStream_TextHeader"}),xa=v.makeMessageType("livekit.DataStream.ByteHeader",()=>[{no:1,name:"name",kind:"scalar",T:9}],{localName:"DataStream_ByteHeader"}),ps=v.makeMessageType("livekit.DataStream.Header",()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"timestamp",kind:"scalar",T:3},{no:3,name:"topic",kind:"scalar",T:9},{no:4,name:"mime_type",kind:"scalar",T:9},{no:5,name:"total_length",kind:"scalar",T:4,opt:!0},{no:7,name:"encryption_type",kind:"enum",T:v.getEnumType(xe)},{no:8,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:9,name:"text_header",kind:"message",T:Aa,oneof:"content_header"},{no:10,name:"byte_header",kind:"message",T:xa,oneof:"content_header"}],{localName:"DataStream_Header"}),fs=v.makeMessageType("livekit.DataStream.Chunk",()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"chunk_index",kind:"scalar",T:4},{no:3,name:"content",kind:"scalar",T:12},{no:4,name:"version",kind:"scalar",T:5},{no:5,name:"iv",kind:"scalar",T:12,opt:!0}],{localName:"DataStream_Chunk"}),gs=v.makeMessageType("livekit.DataStream.Trailer",()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"reason",kind:"scalar",T:9},{no:3,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}}],{localName:"DataStream_Trailer"}),qe=v.makeEnum("livekit.SignalTarget",[{no:0,name:"PUBLISHER"},{no:1,name:"SUBSCRIBER"}]),vs=v.makeEnum("livekit.StreamState",[{no:0,name:"ACTIVE"},{no:1,name:"PAUSED"}]),ph=v.makeEnum("livekit.CandidateProtocol",[{no:0,name:"UDP"},{no:1,name:"TCP"},{no:2,name:"TLS"}]),fh=v.makeMessageType("livekit.SignalRequest",()=>[{no:1,name:"offer",kind:"message",T:Mt,oneof:"message"},{no:2,name:"answer",kind:"message",T:Mt,oneof:"message"},{no:3,name:"trickle",kind:"message",T:Js,oneof:"message"},{no:4,name:"add_track",kind:"message",T:ys,oneof:"message"},{no:5,name:"mute",kind:"message",T:Qs,oneof:"message"},{no:6,name:"subscription",kind:"message",T:fn,oneof:"message"},{no:7,name:"track_setting",kind:"message",T:La,oneof:"message"},{no:8,name:"leave",kind:"message",T:gn,oneof:"message"},{no:10,name:"update_layers",kind:"message",T:Ua,oneof:"message"},{no:11,name:"subscription_permission",kind:"message",T:ja,oneof:"message"},{no:12,name:"sync_state",kind:"message",T:za,oneof:"message"},{no:13,name:"simulate",kind:"message",T:He,oneof:"message"},{no:14,name:"ping",kind:"scalar",T:3,oneof:"message"},{no:15,name:"update_metadata",kind:"message",T:Fa,oneof:"message"},{no:16,name:"ping_req",kind:"message",T:Ga,oneof:"message"},{no:17,name:"update_audio_track",kind:"message",T:Na,oneof:"message"},{no:18,name:"update_video_track",kind:"message",T:Sh,oneof:"message"}]),Qr=v.makeMessageType("livekit.SignalResponse",()=>[{no:1,name:"join",kind:"message",T:gh,oneof:"message"},{no:2,name:"answer",kind:"message",T:Mt,oneof:"message"},{no:3,name:"offer",kind:"message",T:Mt,oneof:"message"},{no:4,name:"trickle",kind:"message",T:Js,oneof:"message"},{no:5,name:"update",kind:"message",T:yh,oneof:"message"},{no:6,name:"track_published",kind:"message",T:Ys,oneof:"message"},{no:8,name:"leave",kind:"message",T:gn,oneof:"message"},{no:9,name:"mute",kind:"message",T:Qs,oneof:"message"},{no:10,name:"speakers_changed",kind:"message",T:kh,oneof:"message"},{no:11,name:"room_update",kind:"message",T:Ch,oneof:"message"},{no:12,name:"connection_quality",kind:"message",T:Ph,oneof:"message"},{no:13,name:"stream_state_update",kind:"message",T:Ih,oneof:"message"},{no:14,name:"subscribed_quality_update",kind:"message",T:wh,oneof:"message"},{no:15,name:"subscription_permission_update",kind:"message",T:_h,oneof:"message"},{no:16,name:"refresh_token",kind:"scalar",T:9,oneof:"message"},{no:17,name:"track_unpublished",kind:"message",T:bh,oneof:"message"},{no:18,name:"pong",kind:"scalar",T:3,oneof:"message"},{no:19,name:"reconnect",kind:"message",T:vh,oneof:"message"},{no:20,name:"pong_resp",kind:"message",T:Dh,oneof:"message"},{no:21,name:"subscription_response",kind:"message",T:xh,oneof:"message"},{no:22,name:"request_response",kind:"message",T:Lh,oneof:"message"},{no:23,name:"track_subscribed",kind:"message",T:Nh,oneof:"message"},{no:24,name:"room_moved",kind:"message",T:Mh,oneof:"message"}]),bs=v.makeMessageType("livekit.SimulcastCodec",()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"cid",kind:"scalar",T:9}]),ys=v.makeMessageType("livekit.AddTrackRequest",()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:v.getEnumType(ze)},{no:4,name:"width",kind:"scalar",T:13},{no:5,name:"height",kind:"scalar",T:13},{no:6,name:"muted",kind:"scalar",T:8},{no:7,name:"disable_dtx",kind:"scalar",T:8},{no:8,name:"source",kind:"enum",T:v.getEnumType(pe)},{no:9,name:"layers",kind:"message",T:wt,repeated:!0},{no:10,name:"simulcast_codecs",kind:"message",T:bs,repeated:!0},{no:11,name:"sid",kind:"scalar",T:9},{no:12,name:"stereo",kind:"scalar",T:8},{no:13,name:"disable_red",kind:"scalar",T:8},{no:14,name:"encryption",kind:"enum",T:v.getEnumType(xe)},{no:15,name:"stream",kind:"scalar",T:9},{no:16,name:"backup_codec_policy",kind:"enum",T:v.getEnumType(Sa)},{no:17,name:"audio_features",kind:"enum",T:v.getEnumType(Se),repeated:!0}]),Js=v.makeMessageType("livekit.TrickleRequest",()=>[{no:1,name:"candidateInit",kind:"scalar",T:9},{no:2,name:"target",kind:"enum",T:v.getEnumType(qe)},{no:3,name:"final",kind:"scalar",T:8}]),Qs=v.makeMessageType("livekit.MuteTrackRequest",()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"muted",kind:"scalar",T:8}]),gh=v.makeMessageType("livekit.JoinResponse",()=>[{no:1,name:"room",kind:"message",T:pn},{no:2,name:"participant",kind:"message",T:_t},{no:3,name:"other_participants",kind:"message",T:_t,repeated:!0},{no:4,name:"server_version",kind:"scalar",T:9},{no:5,name:"ice_servers",kind:"message",T:Va,repeated:!0},{no:6,name:"subscriber_primary",kind:"scalar",T:8},{no:7,name:"alternative_url",kind:"scalar",T:9},{no:8,name:"client_configuration",kind:"message",T:Da},{no:9,name:"server_region",kind:"scalar",T:9},{no:10,name:"ping_timeout",kind:"scalar",T:5},{no:11,name:"ping_interval",kind:"scalar",T:5},{no:12,name:"server_info",kind:"message",T:uh},{no:13,name:"sif_trailer",kind:"scalar",T:12},{no:14,name:"enabled_publish_codecs",kind:"message",T:an,repeated:!0},{no:15,name:"fast_publish",kind:"scalar",T:8}]),vh=v.makeMessageType("livekit.ReconnectResponse",()=>[{no:1,name:"ice_servers",kind:"message",T:Va,repeated:!0},{no:2,name:"client_configuration",kind:"message",T:Da}]),Ys=v.makeMessageType("livekit.TrackPublishedResponse",()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"track",kind:"message",T:jt}]),bh=v.makeMessageType("livekit.TrackUnpublishedResponse",()=>[{no:1,name:"track_sid",kind:"scalar",T:9}]),Mt=v.makeMessageType("livekit.SessionDescription",()=>[{no:1,name:"type",kind:"scalar",T:9},{no:2,name:"sdp",kind:"scalar",T:9}]),yh=v.makeMessageType("livekit.ParticipantUpdate",()=>[{no:1,name:"participants",kind:"message",T:_t,repeated:!0}]),fn=v.makeMessageType("livekit.UpdateSubscription",()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:2,name:"subscribe",kind:"scalar",T:8},{no:3,name:"participant_tracks",kind:"message",T:wa,repeated:!0}]),La=v.makeMessageType("livekit.UpdateTrackSettings",()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:3,name:"disabled",kind:"scalar",T:8},{no:4,name:"quality",kind:"enum",T:v.getEnumType(Wt)},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"fps",kind:"scalar",T:13},{no:8,name:"priority",kind:"scalar",T:13}]),Na=v.makeMessageType("livekit.UpdateLocalAudioTrack",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"features",kind:"enum",T:v.getEnumType(Se),repeated:!0}]),Sh=v.makeMessageType("livekit.UpdateLocalVideoTrack",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13}]),gn=v.makeMessageType("livekit.LeaveRequest",()=>[{no:1,name:"can_reconnect",kind:"scalar",T:8},{no:2,name:"reason",kind:"enum",T:v.getEnumType(Ge)},{no:3,name:"action",kind:"enum",T:v.getEnumType(Kt)},{no:4,name:"regions",kind:"message",T:Oh}]),Kt=v.makeEnum("livekit.LeaveRequest.Action",[{no:0,name:"DISCONNECT"},{no:1,name:"RESUME"},{no:2,name:"RECONNECT"}]),Ua=v.makeMessageType("livekit.UpdateVideoLayers",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"layers",kind:"message",T:wt,repeated:!0}]),Fa=v.makeMessageType("livekit.UpdateParticipantMetadata",()=>[{no:1,name:"metadata",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:4,name:"request_id",kind:"scalar",T:13}]),Va=v.makeMessageType("livekit.ICEServer",()=>[{no:1,name:"urls",kind:"scalar",T:9,repeated:!0},{no:2,name:"username",kind:"scalar",T:9},{no:3,name:"credential",kind:"scalar",T:9}]),kh=v.makeMessageType("livekit.SpeakersChanged",()=>[{no:1,name:"speakers",kind:"message",T:ka,repeated:!0}]),Ch=v.makeMessageType("livekit.RoomUpdate",()=>[{no:1,name:"room",kind:"message",T:pn}]),Th=v.makeMessageType("livekit.ConnectionQualityInfo",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"quality",kind:"enum",T:v.getEnumType(gi)},{no:3,name:"score",kind:"scalar",T:2}]),Ph=v.makeMessageType("livekit.ConnectionQualityUpdate",()=>[{no:1,name:"updates",kind:"message",T:Th,repeated:!0}]),Eh=v.makeMessageType("livekit.StreamStateInfo",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:v.getEnumType(vs)}]),Ih=v.makeMessageType("livekit.StreamStateUpdate",()=>[{no:1,name:"stream_states",kind:"message",T:Eh,repeated:!0}]),Xs=v.makeMessageType("livekit.SubscribedQuality",()=>[{no:1,name:"quality",kind:"enum",T:v.getEnumType(Wt)},{no:2,name:"enabled",kind:"scalar",T:8}]),Rh=v.makeMessageType("livekit.SubscribedCodec",()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"qualities",kind:"message",T:Xs,repeated:!0}]),wh=v.makeMessageType("livekit.SubscribedQualityUpdate",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"subscribed_qualities",kind:"message",T:Xs,repeated:!0},{no:3,name:"subscribed_codecs",kind:"message",T:Rh,repeated:!0}]),Ba=v.makeMessageType("livekit.TrackPermission",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"all_tracks",kind:"scalar",T:8},{no:3,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:4,name:"participant_identity",kind:"scalar",T:9}]),ja=v.makeMessageType("livekit.SubscriptionPermission",()=>[{no:1,name:"all_participants",kind:"scalar",T:8},{no:2,name:"track_permissions",kind:"message",T:Ba,repeated:!0}]),_h=v.makeMessageType("livekit.SubscriptionPermissionUpdate",()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"allowed",kind:"scalar",T:8}]),Mh=v.makeMessageType("livekit.RoomMovedResponse",()=>[{no:1,name:"room",kind:"message",T:pn},{no:2,name:"token",kind:"scalar",T:9},{no:3,name:"participant",kind:"message",T:_t},{no:4,name:"other_participants",kind:"message",T:_t,repeated:!0}]),za=v.makeMessageType("livekit.SyncState",()=>[{no:1,name:"answer",kind:"message",T:Mt},{no:2,name:"subscription",kind:"message",T:fn},{no:3,name:"publish_tracks",kind:"message",T:Ys,repeated:!0},{no:4,name:"data_channels",kind:"message",T:qa,repeated:!0},{no:5,name:"offer",kind:"message",T:Mt},{no:6,name:"track_sids_disabled",kind:"scalar",T:9,repeated:!0}]),qa=v.makeMessageType("livekit.DataChannelInfo",()=>[{no:1,name:"label",kind:"scalar",T:9},{no:2,name:"id",kind:"scalar",T:13},{no:3,name:"target",kind:"enum",T:v.getEnumType(qe)}]),He=v.makeMessageType("livekit.SimulateScenario",()=>[{no:1,name:"speaker_update",kind:"scalar",T:5,oneof:"scenario"},{no:2,name:"node_failure",kind:"scalar",T:8,oneof:"scenario"},{no:3,name:"migration",kind:"scalar",T:8,oneof:"scenario"},{no:4,name:"server_leave",kind:"scalar",T:8,oneof:"scenario"},{no:5,name:"switch_candidate_protocol",kind:"enum",T:v.getEnumType(ph),oneof:"scenario"},{no:6,name:"subscriber_bandwidth",kind:"scalar",T:3,oneof:"scenario"},{no:7,name:"disconnect_signal_on_resume",kind:"scalar",T:8,oneof:"scenario"},{no:8,name:"disconnect_signal_on_resume_no_messages",kind:"scalar",T:8,oneof:"scenario"},{no:9,name:"leave_request_full_reconnect",kind:"scalar",T:8,oneof:"scenario"}]),Ga=v.makeMessageType("livekit.Ping",()=>[{no:1,name:"timestamp",kind:"scalar",T:3},{no:2,name:"rtt",kind:"scalar",T:3}]),Dh=v.makeMessageType("livekit.Pong",()=>[{no:1,name:"last_ping_timestamp",kind:"scalar",T:3},{no:2,name:"timestamp",kind:"scalar",T:3}]),Oh=v.makeMessageType("livekit.RegionSettings",()=>[{no:1,name:"regions",kind:"message",T:Ah,repeated:!0}]),Ah=v.makeMessageType("livekit.RegionInfo",()=>[{no:1,name:"region",kind:"scalar",T:9},{no:2,name:"url",kind:"scalar",T:9},{no:3,name:"distance",kind:"scalar",T:3}]),xh=v.makeMessageType("livekit.SubscriptionResponse",()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"err",kind:"enum",T:v.getEnumType(sh)}]),Lh=v.makeMessageType("livekit.RequestResponse",()=>[{no:1,name:"request_id",kind:"scalar",T:13},{no:2,name:"reason",kind:"enum",T:v.getEnumType(Zs)},{no:3,name:"message",kind:"scalar",T:9}]),Zs=v.makeEnum("livekit.RequestResponse.Reason",[{no:0,name:"OK"},{no:1,name:"NOT_FOUND"},{no:2,name:"NOT_ALLOWED"},{no:3,name:"LIMIT_EXCEEDED"}]),Nh=v.makeMessageType("livekit.TrackSubscribed",()=>[{no:1,name:"track_sid",kind:"scalar",T:9}]);function Uh(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Wi={exports:{}},Fh=Wi.exports,Yr;function Vh(){return Yr||(Yr=1,function(t){(function(e,i){t.exports?t.exports=i():e.log=i()})(Fh,function(){var e=function(){},i="undefined",n=typeof window!==i&&typeof window.navigator!==i&&/Trident\/|MSIE /.test(window.navigator.userAgent),s=["trace","debug","info","warn","error"],r={},o=null;function a(f,y){var S=f[y];if(typeof S.bind=="function")return S.bind(f);try{return Function.prototype.bind.call(S,f)}catch{return function(){return Function.prototype.apply.apply(S,[f,arguments])}}}function c(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function d(f){return f==="debug"&&(f="log"),typeof console===i?!1:f==="trace"&&n?c:console[f]!==void 0?a(console,f):console.log!==void 0?a(console,"log"):e}function l(){for(var f=this.getLevel(),y=0;y<s.length;y++){var S=s[y];this[S]=y<f?e:this.methodFactory(S,f,this.name)}if(this.log=this.debug,typeof console===i&&f<this.levels.SILENT)return"No console available for logging"}function u(f){return function(){typeof console!==i&&(l.call(this),this[f].apply(this,arguments))}}function h(f,y,S){return d(f)||u.apply(this,arguments)}function p(f,y){var S=this,x,M,b,k="loglevel";typeof f=="string"?k+=":"+f:typeof f=="symbol"&&(k=void 0);function _(q){var X=(s[q]||"silent").toUpperCase();if(!(typeof window===i||!k)){try{window.localStorage[k]=X;return}catch{}try{window.document.cookie=encodeURIComponent(k)+"="+X+";"}catch{}}}function j(){var q;if(!(typeof window===i||!k)){try{q=window.localStorage[k]}catch{}if(typeof q===i)try{var X=window.document.cookie,ye=encodeURIComponent(k),Ne=X.indexOf(ye+"=");Ne!==-1&&(q=/^([^;]+)/.exec(X.slice(Ne+ye.length+1))[1])}catch{}return S.levels[q]===void 0&&(q=void 0),q}}function z(){if(!(typeof window===i||!k)){try{window.localStorage.removeItem(k)}catch{}try{window.document.cookie=encodeURIComponent(k)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch{}}}function V(q){var X=q;if(typeof X=="string"&&S.levels[X.toUpperCase()]!==void 0&&(X=S.levels[X.toUpperCase()]),typeof X=="number"&&X>=0&&X<=S.levels.SILENT)return X;throw new TypeError("log.setLevel() called with invalid level: "+q)}S.name=f,S.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},S.methodFactory=y||h,S.getLevel=function(){return b??M??x},S.setLevel=function(q,X){return b=V(q),X!==!1&&_(b),l.call(S)},S.setDefaultLevel=function(q){M=V(q),j()||S.setLevel(q,!1)},S.resetLevel=function(){b=null,z(),l.call(S)},S.enableAll=function(q){S.setLevel(S.levels.TRACE,q)},S.disableAll=function(q){S.setLevel(S.levels.SILENT,q)},S.rebuild=function(){if(o!==S&&(x=V(o.getLevel())),l.call(S),o===S)for(var q in r)r[q].rebuild()},x=V(o?o.getLevel():"WARN");var B=j();B!=null&&(b=V(B)),l.call(S)}o=new p,o.getLogger=function(y){if(typeof y!="symbol"&&typeof y!="string"||y==="")throw new TypeError("You must supply a name when creating a logger.");var S=r[y];return S||(S=r[y]=new p(y,o.methodFactory)),S};var g=typeof window!==i?window.log:void 0;return o.noConflict=function(){return typeof window!==i&&window.log===o&&(window.log=g),o},o.getLoggers=function(){return r},o.default=o,o})}(Wi)),Wi.exports}var vn=Vh(),Ss;(function(t){t[t.trace=0]="trace",t[t.debug=1]="debug",t[t.info=2]="info",t[t.warn=3]="warn",t[t.error=4]="error",t[t.silent=5]="silent"})(Ss||(Ss={}));var We;(function(t){t.Default="livekit",t.Room="livekit-room",t.Participant="livekit-participant",t.Track="livekit-track",t.Publication="livekit-track-publication",t.Engine="livekit-engine",t.Signal="livekit-signal",t.PCManager="livekit-pc-manager",t.PCTransport="livekit-pc-transport",t.E2EE="lk-e2ee"})(We||(We={}));let Q=vn.getLogger("livekit");Object.values(We).map(t=>vn.getLogger(t));Q.setDefaultLevel(Ss.info);function nt(t){const e=vn.getLogger(t);return e.setDefaultLevel(Q.getLevel()),e}const Bh=vn.getLogger("lk-e2ee"),li=7e3,jh=[0,300,4*300,9*300,16*300,li,li,li,li,li];class zh{constructor(e){this._retryDelays=e!==void 0?[...e]:jh}nextRetryDelayInMs(e){if(e.retryCount>=this._retryDelays.length)return null;const i=this._retryDelays[e.retryCount];return e.retryCount<=1?i:i+Math.random()*1e3}}function qh(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(t);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(t,n[s])&&(i[n[s]]=t[n[s]]);return i}function m(t,e,i,n){function s(r){return r instanceof i?r:new i(function(o){o(r)})}return new(i||(i=Promise))(function(r,o){function a(l){try{d(n.next(l))}catch(u){o(u)}}function c(l){try{d(n.throw(l))}catch(u){o(u)}}function d(l){l.done?r(l.value):s(l.value).then(a,c)}d((n=n.apply(t,e||[])).next())})}function Xr(t){var e=typeof Symbol=="function"&&Symbol.iterator,i=e&&t[e],n=0;if(i)return i.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function tt(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],i;return e?e.call(t):(t=typeof Xr=="function"?Xr(t):t[Symbol.iterator](),i={},n("next"),n("throw"),n("return"),i[Symbol.asyncIterator]=function(){return this},i);function n(r){i[r]=t[r]&&function(o){return new Promise(function(a,c){o=t[r](o),s(a,c,o.done,o.value)})}}function s(r,o,a,c){Promise.resolve(c).then(function(d){r({value:d,done:a})},o)}}var Ni={exports:{}},Zr;function Gh(){if(Zr)return Ni.exports;Zr=1;var t=typeof Reflect=="object"?Reflect:null,e=t&&typeof t.apply=="function"?t.apply:function(k,_,j){return Function.prototype.apply.call(k,_,j)},i;t&&typeof t.ownKeys=="function"?i=t.ownKeys:Object.getOwnPropertySymbols?i=function(k){return Object.getOwnPropertyNames(k).concat(Object.getOwnPropertySymbols(k))}:i=function(k){return Object.getOwnPropertyNames(k)};function n(b){console&&console.warn&&console.warn(b)}var s=Number.isNaN||function(k){return k!==k};function r(){r.init.call(this)}Ni.exports=r,Ni.exports.once=S,r.EventEmitter=r,r.prototype._events=void 0,r.prototype._eventsCount=0,r.prototype._maxListeners=void 0;var o=10;function a(b){if(typeof b!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof b)}Object.defineProperty(r,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(b){if(typeof b!="number"||b<0||s(b))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+b+".");o=b}}),r.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},r.prototype.setMaxListeners=function(k){if(typeof k!="number"||k<0||s(k))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+k+".");return this._maxListeners=k,this};function c(b){return b._maxListeners===void 0?r.defaultMaxListeners:b._maxListeners}r.prototype.getMaxListeners=function(){return c(this)},r.prototype.emit=function(k){for(var _=[],j=1;j<arguments.length;j++)_.push(arguments[j]);var z=k==="error",V=this._events;if(V!==void 0)z=z&&V.error===void 0;else if(!z)return!1;if(z){var B;if(_.length>0&&(B=_[0]),B instanceof Error)throw B;var q=new Error("Unhandled error."+(B?" ("+B.message+")":""));throw q.context=B,q}var X=V[k];if(X===void 0)return!1;if(typeof X=="function")e(X,this,_);else for(var ye=X.length,Ne=g(X,ye),j=0;j<ye;++j)e(Ne[j],this,_);return!0};function d(b,k,_,j){var z,V,B;if(a(_),V=b._events,V===void 0?(V=b._events=Object.create(null),b._eventsCount=0):(V.newListener!==void 0&&(b.emit("newListener",k,_.listener?_.listener:_),V=b._events),B=V[k]),B===void 0)B=V[k]=_,++b._eventsCount;else if(typeof B=="function"?B=V[k]=j?[_,B]:[B,_]:j?B.unshift(_):B.push(_),z=c(b),z>0&&B.length>z&&!B.warned){B.warned=!0;var q=new Error("Possible EventEmitter memory leak detected. "+B.length+" "+String(k)+" listeners added. Use emitter.setMaxListeners() to increase limit");q.name="MaxListenersExceededWarning",q.emitter=b,q.type=k,q.count=B.length,n(q)}return b}r.prototype.addListener=function(k,_){return d(this,k,_,!1)},r.prototype.on=r.prototype.addListener,r.prototype.prependListener=function(k,_){return d(this,k,_,!0)};function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function u(b,k,_){var j={fired:!1,wrapFn:void 0,target:b,type:k,listener:_},z=l.bind(j);return z.listener=_,j.wrapFn=z,z}r.prototype.once=function(k,_){return a(_),this.on(k,u(this,k,_)),this},r.prototype.prependOnceListener=function(k,_){return a(_),this.prependListener(k,u(this,k,_)),this},r.prototype.removeListener=function(k,_){var j,z,V,B,q;if(a(_),z=this._events,z===void 0)return this;if(j=z[k],j===void 0)return this;if(j===_||j.listener===_)--this._eventsCount===0?this._events=Object.create(null):(delete z[k],z.removeListener&&this.emit("removeListener",k,j.listener||_));else if(typeof j!="function"){for(V=-1,B=j.length-1;B>=0;B--)if(j[B]===_||j[B].listener===_){q=j[B].listener,V=B;break}if(V<0)return this;V===0?j.shift():f(j,V),j.length===1&&(z[k]=j[0]),z.removeListener!==void 0&&this.emit("removeListener",k,q||_)}return this},r.prototype.off=r.prototype.removeListener,r.prototype.removeAllListeners=function(k){var _,j,z;if(j=this._events,j===void 0)return this;if(j.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):j[k]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete j[k]),this;if(arguments.length===0){var V=Object.keys(j),B;for(z=0;z<V.length;++z)B=V[z],B!=="removeListener"&&this.removeAllListeners(B);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(_=j[k],typeof _=="function")this.removeListener(k,_);else if(_!==void 0)for(z=_.length-1;z>=0;z--)this.removeListener(k,_[z]);return this};function h(b,k,_){var j=b._events;if(j===void 0)return[];var z=j[k];return z===void 0?[]:typeof z=="function"?_?[z.listener||z]:[z]:_?y(z):g(z,z.length)}r.prototype.listeners=function(k){return h(this,k,!0)},r.prototype.rawListeners=function(k){return h(this,k,!1)},r.listenerCount=function(b,k){return typeof b.listenerCount=="function"?b.listenerCount(k):p.call(b,k)},r.prototype.listenerCount=p;function p(b){var k=this._events;if(k!==void 0){var _=k[b];if(typeof _=="function")return 1;if(_!==void 0)return _.length}return 0}r.prototype.eventNames=function(){return this._eventsCount>0?i(this._events):[]};function g(b,k){for(var _=new Array(k),j=0;j<k;++j)_[j]=b[j];return _}function f(b,k){for(;k+1<b.length;k++)b[k]=b[k+1];b.pop()}function y(b){for(var k=new Array(b.length),_=0;_<k.length;++_)k[_]=b[_].listener||b[_];return k}function S(b,k){return new Promise(function(_,j){function z(B){b.removeListener(k,V),j(B)}function V(){typeof b.removeListener=="function"&&b.removeListener("error",z),_([].slice.call(arguments))}M(b,k,V,{once:!0}),k!=="error"&&x(b,z,{once:!0})})}function x(b,k,_){typeof b.on=="function"&&M(b,"error",k,_)}function M(b,k,_,j){if(typeof b.on=="function")j.once?b.once(k,_):b.on(k,_);else if(typeof b.addEventListener=="function")b.addEventListener(k,function z(V){j.once&&b.removeEventListener(k,z),_(V)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof b)}return Ni.exports}var Ye=Gh();let Wa=!0,Ha=!0;function vi(t,e,i){const n=t.match(e);return n&&n.length>=i&&parseFloat(n[i],10)}function At(t,e,i){if(!t.RTCPeerConnection)return;const n=t.RTCPeerConnection.prototype,s=n.addEventListener;n.addEventListener=function(o,a){if(o!==e)return s.apply(this,arguments);const c=d=>{const l=i(d);l&&(a.handleEvent?a.handleEvent(l):a(l))};return this._eventMap=this._eventMap||{},this._eventMap[e]||(this._eventMap[e]=new Map),this._eventMap[e].set(a,c),s.apply(this,[o,c])};const r=n.removeEventListener;n.removeEventListener=function(o,a){if(o!==e||!this._eventMap||!this._eventMap[e])return r.apply(this,arguments);if(!this._eventMap[e].has(a))return r.apply(this,arguments);const c=this._eventMap[e].get(a);return this._eventMap[e].delete(a),this._eventMap[e].size===0&&delete this._eventMap[e],Object.keys(this._eventMap).length===0&&delete this._eventMap,r.apply(this,[o,c])},Object.defineProperty(n,"on"+e,{get(){return this["_on"+e]},set(o){this["_on"+e]&&(this.removeEventListener(e,this["_on"+e]),delete this["_on"+e]),o&&this.addEventListener(e,this["_on"+e]=o)},enumerable:!0,configurable:!0})}function Wh(t){return typeof t!="boolean"?new Error("Argument type: "+typeof t+". Please use a boolean."):(Wa=t,t?"adapter.js logging disabled":"adapter.js logging enabled")}function Hh(t){return typeof t!="boolean"?new Error("Argument type: "+typeof t+". Please use a boolean."):(Ha=!t,"adapter.js deprecation warnings "+(t?"disabled":"enabled"))}function Ka(){if(typeof window=="object"){if(Wa)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function er(t,e){Ha&&console.warn(t+" is deprecated, please use "+e+" instead.")}function Kh(t){const e={browser:null,version:null};if(typeof t>"u"||!t.navigator||!t.navigator.userAgent)return e.browser="Not a browser.",e;const{navigator:i}=t;if(i.userAgentData&&i.userAgentData.brands){const n=i.userAgentData.brands.find(s=>s.brand==="Chromium");if(n)return{browser:"chrome",version:parseInt(n.version,10)}}if(i.mozGetUserMedia)e.browser="firefox",e.version=parseInt(vi(i.userAgent,/Firefox\/(\d+)\./,1));else if(i.webkitGetUserMedia||t.isSecureContext===!1&&t.webkitRTCPeerConnection)e.browser="chrome",e.version=parseInt(vi(i.userAgent,/Chrom(e|ium)\/(\d+)\./,2));else if(t.RTCPeerConnection&&i.userAgent.match(/AppleWebKit\/(\d+)\./))e.browser="safari",e.version=parseInt(vi(i.userAgent,/AppleWebKit\/(\d+)\./,1)),e.supportsUnifiedPlan=t.RTCRtpTransceiver&&"currentDirection"in t.RTCRtpTransceiver.prototype,e._safariVersion=vi(i.userAgent,/Version\/(\d+(\.?\d+))/,1);else return e.browser="Not a supported browser.",e;return e}function eo(t){return Object.prototype.toString.call(t)==="[object Object]"}function $a(t){return eo(t)?Object.keys(t).reduce(function(e,i){const n=eo(t[i]),s=n?$a(t[i]):t[i],r=n&&!Object.keys(s).length;return s===void 0||r?e:Object.assign(e,{[i]:s})},{}):t}function ks(t,e,i){!e||i.has(e.id)||(i.set(e.id,e),Object.keys(e).forEach(n=>{n.endsWith("Id")?ks(t,t.get(e[n]),i):n.endsWith("Ids")&&e[n].forEach(s=>{ks(t,t.get(s),i)})}))}function to(t,e,i){const n=i?"outbound-rtp":"inbound-rtp",s=new Map;if(e===null)return s;const r=[];return t.forEach(o=>{o.type==="track"&&o.trackIdentifier===e.id&&r.push(o)}),r.forEach(o=>{t.forEach(a=>{a.type===n&&a.trackId===o.id&&ks(t,a,s)})}),s}const io=Ka;function Ja(t,e){const i=t&&t.navigator;if(!i.mediaDevices)return;const n=function(a){if(typeof a!="object"||a.mandatory||a.optional)return a;const c={};return Object.keys(a).forEach(d=>{if(d==="require"||d==="advanced"||d==="mediaSource")return;const l=typeof a[d]=="object"?a[d]:{ideal:a[d]};l.exact!==void 0&&typeof l.exact=="number"&&(l.min=l.max=l.exact);const u=function(h,p){return h?h+p.charAt(0).toUpperCase()+p.slice(1):p==="deviceId"?"sourceId":p};if(l.ideal!==void 0){c.optional=c.optional||[];let h={};typeof l.ideal=="number"?(h[u("min",d)]=l.ideal,c.optional.push(h),h={},h[u("max",d)]=l.ideal,c.optional.push(h)):(h[u("",d)]=l.ideal,c.optional.push(h))}l.exact!==void 0&&typeof l.exact!="number"?(c.mandatory=c.mandatory||{},c.mandatory[u("",d)]=l.exact):["min","max"].forEach(h=>{l[h]!==void 0&&(c.mandatory=c.mandatory||{},c.mandatory[u(h,d)]=l[h])})}),a.advanced&&(c.optional=(c.optional||[]).concat(a.advanced)),c},s=function(a,c){if(e.version>=61)return c(a);if(a=JSON.parse(JSON.stringify(a)),a&&typeof a.audio=="object"){const d=function(l,u,h){u in l&&!(h in l)&&(l[h]=l[u],delete l[u])};a=JSON.parse(JSON.stringify(a)),d(a.audio,"autoGainControl","googAutoGainControl"),d(a.audio,"noiseSuppression","googNoiseSuppression"),a.audio=n(a.audio)}if(a&&typeof a.video=="object"){let d=a.video.facingMode;d=d&&(typeof d=="object"?d:{ideal:d});const l=e.version<66;if(d&&(d.exact==="user"||d.exact==="environment"||d.ideal==="user"||d.ideal==="environment")&&!(i.mediaDevices.getSupportedConstraints&&i.mediaDevices.getSupportedConstraints().facingMode&&!l)){delete a.video.facingMode;let u;if(d.exact==="environment"||d.ideal==="environment"?u=["back","rear"]:(d.exact==="user"||d.ideal==="user")&&(u=["front"]),u)return i.mediaDevices.enumerateDevices().then(h=>{h=h.filter(g=>g.kind==="videoinput");let p=h.find(g=>u.some(f=>g.label.toLowerCase().includes(f)));return!p&&h.length&&u.includes("back")&&(p=h[h.length-1]),p&&(a.video.deviceId=d.exact?{exact:p.deviceId}:{ideal:p.deviceId}),a.video=n(a.video),io("chrome: "+JSON.stringify(a)),c(a)})}a.video=n(a.video)}return io("chrome: "+JSON.stringify(a)),c(a)},r=function(a){return e.version>=64?a:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[a.name]||a.name,message:a.message,constraint:a.constraint||a.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},o=function(a,c,d){s(a,l=>{i.webkitGetUserMedia(l,c,u=>{d&&d(r(u))})})};if(i.getUserMedia=o.bind(i),i.mediaDevices.getUserMedia){const a=i.mediaDevices.getUserMedia.bind(i.mediaDevices);i.mediaDevices.getUserMedia=function(c){return s(c,d=>a(d).then(l=>{if(d.audio&&!l.getAudioTracks().length||d.video&&!l.getVideoTracks().length)throw l.getTracks().forEach(u=>{u.stop()}),new DOMException("","NotFoundError");return l},l=>Promise.reject(r(l))))}}}function Qa(t){t.MediaStream=t.MediaStream||t.webkitMediaStream}function Ya(t){if(typeof t=="object"&&t.RTCPeerConnection&&!("ontrack"in t.RTCPeerConnection.prototype)){Object.defineProperty(t.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(i){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=i)},enumerable:!0,configurable:!0});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=n=>{n.stream.addEventListener("addtrack",s=>{let r;t.RTCPeerConnection.prototype.getReceivers?r=this.getReceivers().find(a=>a.track&&a.track.id===s.track.id):r={track:s.track};const o=new Event("track");o.track=s.track,o.receiver=r,o.transceiver={receiver:r},o.streams=[n.stream],this.dispatchEvent(o)}),n.stream.getTracks().forEach(s=>{let r;t.RTCPeerConnection.prototype.getReceivers?r=this.getReceivers().find(a=>a.track&&a.track.id===s.id):r={track:s};const o=new Event("track");o.track=s,o.receiver=r,o.transceiver={receiver:r},o.streams=[n.stream],this.dispatchEvent(o)})},this.addEventListener("addstream",this._ontrackpoly)),e.apply(this,arguments)}}else At(t,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e))}function Xa(t){if(typeof t=="object"&&t.RTCPeerConnection&&!("getSenders"in t.RTCPeerConnection.prototype)&&"createDTMFSender"in t.RTCPeerConnection.prototype){const e=function(s,r){return{track:r,get dtmf(){return this._dtmf===void 0&&(r.kind==="audio"?this._dtmf=s.createDTMFSender(r):this._dtmf=null),this._dtmf},_pc:s}};if(!t.RTCPeerConnection.prototype.getSenders){t.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const s=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(a,c){let d=s.apply(this,arguments);return d||(d=e(this,a),this._senders.push(d)),d};const r=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(a){r.apply(this,arguments);const c=this._senders.indexOf(a);c!==-1&&this._senders.splice(c,1)}}const i=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(r){this._senders=this._senders||[],i.apply(this,[r]),r.getTracks().forEach(o=>{this._senders.push(e(this,o))})};const n=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(r){this._senders=this._senders||[],n.apply(this,[r]),r.getTracks().forEach(o=>{const a=this._senders.find(c=>c.track===o);a&&this._senders.splice(this._senders.indexOf(a),1)})}}else if(typeof t=="object"&&t.RTCPeerConnection&&"getSenders"in t.RTCPeerConnection.prototype&&"createDTMFSender"in t.RTCPeerConnection.prototype&&t.RTCRtpSender&&!("dtmf"in t.RTCRtpSender.prototype)){const e=t.RTCPeerConnection.prototype.getSenders;t.RTCPeerConnection.prototype.getSenders=function(){const n=e.apply(this,[]);return n.forEach(s=>s._pc=this),n},Object.defineProperty(t.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Za(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender&&t.RTCRtpReceiver))return;if(!("getStats"in t.RTCRtpSender.prototype)){const i=t.RTCPeerConnection.prototype.getSenders;i&&(t.RTCPeerConnection.prototype.getSenders=function(){const r=i.apply(this,[]);return r.forEach(o=>o._pc=this),r});const n=t.RTCPeerConnection.prototype.addTrack;n&&(t.RTCPeerConnection.prototype.addTrack=function(){const r=n.apply(this,arguments);return r._pc=this,r}),t.RTCRtpSender.prototype.getStats=function(){const r=this;return this._pc.getStats().then(o=>to(o,r.track,!0))}}if(!("getStats"in t.RTCRtpReceiver.prototype)){const i=t.RTCPeerConnection.prototype.getReceivers;i&&(t.RTCPeerConnection.prototype.getReceivers=function(){const s=i.apply(this,[]);return s.forEach(r=>r._pc=this),s}),At(t,"track",n=>(n.receiver._pc=n.srcElement,n)),t.RTCRtpReceiver.prototype.getStats=function(){const s=this;return this._pc.getStats().then(r=>to(r,s.track,!1))}}if(!("getStats"in t.RTCRtpSender.prototype&&"getStats"in t.RTCRtpReceiver.prototype))return;const e=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof t.MediaStreamTrack){const n=arguments[0];let s,r,o;return this.getSenders().forEach(a=>{a.track===n&&(s?o=!0:s=a)}),this.getReceivers().forEach(a=>(a.track===n&&(r?o=!0:r=a),a.track===n)),o||s&&r?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):s?s.getStats():r?r.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return e.apply(this,arguments)}}function ec(t){t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(o=>this._shimmedLocalStreams[o][0])};const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addTrack=function(o,a){if(!a)return e.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const c=e.apply(this,arguments);return this._shimmedLocalStreams[a.id]?this._shimmedLocalStreams[a.id].indexOf(c)===-1&&this._shimmedLocalStreams[a.id].push(c):this._shimmedLocalStreams[a.id]=[a,c],c};const i=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(o){this._shimmedLocalStreams=this._shimmedLocalStreams||{},o.getTracks().forEach(d=>{if(this.getSenders().find(u=>u.track===d))throw new DOMException("Track already exists.","InvalidAccessError")});const a=this.getSenders();i.apply(this,arguments);const c=this.getSenders().filter(d=>a.indexOf(d)===-1);this._shimmedLocalStreams[o.id]=[o].concat(c)};const n=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(o){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[o.id],n.apply(this,arguments)};const s=t.RTCPeerConnection.prototype.removeTrack;t.RTCPeerConnection.prototype.removeTrack=function(o){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},o&&Object.keys(this._shimmedLocalStreams).forEach(a=>{const c=this._shimmedLocalStreams[a].indexOf(o);c!==-1&&this._shimmedLocalStreams[a].splice(c,1),this._shimmedLocalStreams[a].length===1&&delete this._shimmedLocalStreams[a]}),s.apply(this,arguments)}}function tc(t,e){if(!t.RTCPeerConnection)return;if(t.RTCPeerConnection.prototype.addTrack&&e.version>=65)return ec(t);const i=t.RTCPeerConnection.prototype.getLocalStreams;t.RTCPeerConnection.prototype.getLocalStreams=function(){const l=i.apply(this);return this._reverseStreams=this._reverseStreams||{},l.map(u=>this._reverseStreams[u.id])};const n=t.RTCPeerConnection.prototype.addStream;t.RTCPeerConnection.prototype.addStream=function(l){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},l.getTracks().forEach(u=>{if(this.getSenders().find(p=>p.track===u))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[l.id]){const u=new t.MediaStream(l.getTracks());this._streams[l.id]=u,this._reverseStreams[u.id]=l,l=u}n.apply(this,[l])};const s=t.RTCPeerConnection.prototype.removeStream;t.RTCPeerConnection.prototype.removeStream=function(l){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},s.apply(this,[this._streams[l.id]||l]),delete this._reverseStreams[this._streams[l.id]?this._streams[l.id].id:l.id],delete this._streams[l.id]},t.RTCPeerConnection.prototype.addTrack=function(l,u){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const h=[].slice.call(arguments,1);if(h.length!==1||!h[0].getTracks().find(f=>f===l))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(f=>f.track===l))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const g=this._streams[u.id];if(g)g.addTrack(l),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const f=new t.MediaStream([l]);this._streams[u.id]=f,this._reverseStreams[f.id]=u,this.addStream(f)}return this.getSenders().find(f=>f.track===l)};function r(d,l){let u=l.sdp;return Object.keys(d._reverseStreams||[]).forEach(h=>{const p=d._reverseStreams[h],g=d._streams[p.id];u=u.replace(new RegExp(g.id,"g"),p.id)}),new RTCSessionDescription({type:l.type,sdp:u})}function o(d,l){let u=l.sdp;return Object.keys(d._reverseStreams||[]).forEach(h=>{const p=d._reverseStreams[h],g=d._streams[p.id];u=u.replace(new RegExp(p.id,"g"),g.id)}),new RTCSessionDescription({type:l.type,sdp:u})}["createOffer","createAnswer"].forEach(function(d){const l=t.RTCPeerConnection.prototype[d],u={[d](){const h=arguments;return arguments.length&&typeof arguments[0]=="function"?l.apply(this,[g=>{const f=r(this,g);h[0].apply(null,[f])},g=>{h[1]&&h[1].apply(null,g)},arguments[2]]):l.apply(this,arguments).then(g=>r(this,g))}};t.RTCPeerConnection.prototype[d]=u[d]});const a=t.RTCPeerConnection.prototype.setLocalDescription;t.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?a.apply(this,arguments):(arguments[0]=o(this,arguments[0]),a.apply(this,arguments))};const c=Object.getOwnPropertyDescriptor(t.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(t.RTCPeerConnection.prototype,"localDescription",{get(){const d=c.get.apply(this);return d.type===""?d:r(this,d)}}),t.RTCPeerConnection.prototype.removeTrack=function(l){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!l._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(l._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let h;Object.keys(this._streams).forEach(p=>{this._streams[p].getTracks().find(f=>l.track===f)&&(h=this._streams[p])}),h&&(h.getTracks().length===1?this.removeStream(this._reverseStreams[h.id]):h.removeTrack(l.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Cs(t,e){!t.RTCPeerConnection&&t.webkitRTCPeerConnection&&(t.RTCPeerConnection=t.webkitRTCPeerConnection),t.RTCPeerConnection&&e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(i){const n=t.RTCPeerConnection.prototype[i],s={[i](){return arguments[0]=new(i==="addIceCandidate"?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};t.RTCPeerConnection.prototype[i]=s[i]})}function ic(t,e){At(t,"negotiationneeded",i=>{const n=i.target;if(!((e.version<72||n.getConfiguration&&n.getConfiguration().sdpSemantics==="plan-b")&&n.signalingState!=="stable"))return i})}var no=Object.freeze({__proto__:null,fixNegotiationNeeded:ic,shimAddTrackRemoveTrack:tc,shimAddTrackRemoveTrackWithNative:ec,shimGetSendersWithDtmf:Xa,shimGetUserMedia:Ja,shimMediaStream:Qa,shimOnTrack:Ya,shimPeerConnection:Cs,shimSenderReceiverGetStats:Za});function nc(t,e){const i=t&&t.navigator,n=t&&t.MediaStreamTrack;if(i.getUserMedia=function(s,r,o){er("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),i.mediaDevices.getUserMedia(s).then(r,o)},!(e.version>55&&"autoGainControl"in i.mediaDevices.getSupportedConstraints())){const s=function(o,a,c){a in o&&!(c in o)&&(o[c]=o[a],delete o[a])},r=i.mediaDevices.getUserMedia.bind(i.mediaDevices);if(i.mediaDevices.getUserMedia=function(o){return typeof o=="object"&&typeof o.audio=="object"&&(o=JSON.parse(JSON.stringify(o)),s(o.audio,"autoGainControl","mozAutoGainControl"),s(o.audio,"noiseSuppression","mozNoiseSuppression")),r(o)},n&&n.prototype.getSettings){const o=n.prototype.getSettings;n.prototype.getSettings=function(){const a=o.apply(this,arguments);return s(a,"mozAutoGainControl","autoGainControl"),s(a,"mozNoiseSuppression","noiseSuppression"),a}}if(n&&n.prototype.applyConstraints){const o=n.prototype.applyConstraints;n.prototype.applyConstraints=function(a){return this.kind==="audio"&&typeof a=="object"&&(a=JSON.parse(JSON.stringify(a)),s(a,"autoGainControl","mozAutoGainControl"),s(a,"noiseSuppression","mozNoiseSuppression")),o.apply(this,[a])}}}}function $h(t,e){t.navigator.mediaDevices&&"getDisplayMedia"in t.navigator.mediaDevices||t.navigator.mediaDevices&&(t.navigator.mediaDevices.getDisplayMedia=function(n){if(!(n&&n.video)){const s=new DOMException("getDisplayMedia without video constraints is undefined");return s.name="NotFoundError",s.code=8,Promise.reject(s)}return n.video===!0?n.video={mediaSource:e}:n.video.mediaSource=e,t.navigator.mediaDevices.getUserMedia(n)})}function sc(t){typeof t=="object"&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Ts(t,e){if(typeof t!="object"||!(t.RTCPeerConnection||t.mozRTCPeerConnection))return;!t.RTCPeerConnection&&t.mozRTCPeerConnection&&(t.RTCPeerConnection=t.mozRTCPeerConnection),e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(s){const r=t.RTCPeerConnection.prototype[s],o={[s](){return arguments[0]=new(s==="addIceCandidate"?t.RTCIceCandidate:t.RTCSessionDescription)(arguments[0]),r.apply(this,arguments)}};t.RTCPeerConnection.prototype[s]=o[s]});const i={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},n=t.RTCPeerConnection.prototype.getStats;t.RTCPeerConnection.prototype.getStats=function(){const[r,o,a]=arguments;return n.apply(this,[r||null]).then(c=>{if(e.version<53&&!o)try{c.forEach(d=>{d.type=i[d.type]||d.type})}catch(d){if(d.name!=="TypeError")throw d;c.forEach((l,u)=>{c.set(u,Object.assign({},l,{type:i[l.type]||l.type}))})}return c}).then(o,a)}}function rc(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender)||t.RTCRtpSender&&"getStats"in t.RTCRtpSender.prototype)return;const e=t.RTCPeerConnection.prototype.getSenders;e&&(t.RTCPeerConnection.prototype.getSenders=function(){const s=e.apply(this,[]);return s.forEach(r=>r._pc=this),s});const i=t.RTCPeerConnection.prototype.addTrack;i&&(t.RTCPeerConnection.prototype.addTrack=function(){const s=i.apply(this,arguments);return s._pc=this,s}),t.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function oc(t){if(!(typeof t=="object"&&t.RTCPeerConnection&&t.RTCRtpSender)||t.RTCRtpSender&&"getStats"in t.RTCRtpReceiver.prototype)return;const e=t.RTCPeerConnection.prototype.getReceivers;e&&(t.RTCPeerConnection.prototype.getReceivers=function(){const n=e.apply(this,[]);return n.forEach(s=>s._pc=this),n}),At(t,"track",i=>(i.receiver._pc=i.srcElement,i)),t.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function ac(t){!t.RTCPeerConnection||"removeStream"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.removeStream=function(i){er("removeStream","removeTrack"),this.getSenders().forEach(n=>{n.track&&i.getTracks().includes(n.track)&&this.removeTrack(n)})})}function cc(t){t.DataChannel&&!t.RTCDataChannel&&(t.RTCDataChannel=t.DataChannel)}function dc(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.addTransceiver;e&&(t.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let n=arguments[1]&&arguments[1].sendEncodings;n===void 0&&(n=[]),n=[...n];const s=n.length>0;s&&n.forEach(o=>{if("rid"in o&&!/^[a-z0-9]{0,16}$/i.test(o.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in o&&!(parseFloat(o.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in o&&!(parseFloat(o.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const r=e.apply(this,arguments);if(s){const{sender:o}=r,a=o.getParameters();(!("encodings"in a)||a.encodings.length===1&&Object.keys(a.encodings[0]).length===0)&&(a.encodings=n,o.sendEncodings=n,this.setParametersPromises.push(o.setParameters(a).then(()=>{delete o.sendEncodings}).catch(()=>{delete o.sendEncodings})))}return r})}function lc(t){if(!(typeof t=="object"&&t.RTCRtpSender))return;const e=t.RTCRtpSender.prototype.getParameters;e&&(t.RTCRtpSender.prototype.getParameters=function(){const n=e.apply(this,arguments);return"encodings"in n||(n.encodings=[].concat(this.sendEncodings||[{}])),n})}function uc(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}function hc(t){if(!(typeof t=="object"&&t.RTCPeerConnection))return;const e=t.RTCPeerConnection.prototype.createAnswer;t.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}var so=Object.freeze({__proto__:null,shimAddTransceiver:dc,shimCreateAnswer:hc,shimCreateOffer:uc,shimGetDisplayMedia:$h,shimGetParameters:lc,shimGetUserMedia:nc,shimOnTrack:sc,shimPeerConnection:Ts,shimRTCDataChannel:cc,shimReceiverGetStats:oc,shimRemoveStream:ac,shimSenderGetStats:rc});function mc(t){if(!(typeof t!="object"||!t.RTCPeerConnection)){if("getLocalStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in t.RTCPeerConnection.prototype)){const e=t.RTCPeerConnection.prototype.addTrack;t.RTCPeerConnection.prototype.addStream=function(n){this._localStreams||(this._localStreams=[]),this._localStreams.includes(n)||this._localStreams.push(n),n.getAudioTracks().forEach(s=>e.call(this,s,n)),n.getVideoTracks().forEach(s=>e.call(this,s,n))},t.RTCPeerConnection.prototype.addTrack=function(n){for(var s=arguments.length,r=new Array(s>1?s-1:0),o=1;o<s;o++)r[o-1]=arguments[o];return r&&r.forEach(a=>{this._localStreams?this._localStreams.includes(a)||this._localStreams.push(a):this._localStreams=[a]}),e.apply(this,arguments)}}"removeStream"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.removeStream=function(i){this._localStreams||(this._localStreams=[]);const n=this._localStreams.indexOf(i);if(n===-1)return;this._localStreams.splice(n,1);const s=i.getTracks();this.getSenders().forEach(r=>{s.includes(r.track)&&this.removeTrack(r)})})}}function pc(t){if(!(typeof t!="object"||!t.RTCPeerConnection)&&("getRemoteStreams"in t.RTCPeerConnection.prototype||(t.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in t.RTCPeerConnection.prototype))){Object.defineProperty(t.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(i){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=i),this.addEventListener("track",this._onaddstreampoly=n=>{n.streams.forEach(s=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(s))return;this._remoteStreams.push(s);const r=new Event("addstream");r.stream=s,this.dispatchEvent(r)})})}});const e=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){const n=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(s){s.streams.forEach(r=>{if(n._remoteStreams||(n._remoteStreams=[]),n._remoteStreams.indexOf(r)>=0)return;n._remoteStreams.push(r);const o=new Event("addstream");o.stream=r,n.dispatchEvent(o)})}),e.apply(n,arguments)}}}function fc(t){if(typeof t!="object"||!t.RTCPeerConnection)return;const e=t.RTCPeerConnection.prototype,i=e.createOffer,n=e.createAnswer,s=e.setLocalDescription,r=e.setRemoteDescription,o=e.addIceCandidate;e.createOffer=function(d,l){const u=arguments.length>=2?arguments[2]:arguments[0],h=i.apply(this,[u]);return l?(h.then(d,l),Promise.resolve()):h},e.createAnswer=function(d,l){const u=arguments.length>=2?arguments[2]:arguments[0],h=n.apply(this,[u]);return l?(h.then(d,l),Promise.resolve()):h};let a=function(c,d,l){const u=s.apply(this,[c]);return l?(u.then(d,l),Promise.resolve()):u};e.setLocalDescription=a,a=function(c,d,l){const u=r.apply(this,[c]);return l?(u.then(d,l),Promise.resolve()):u},e.setRemoteDescription=a,a=function(c,d,l){const u=o.apply(this,[c]);return l?(u.then(d,l),Promise.resolve()):u},e.addIceCandidate=a}function gc(t){const e=t&&t.navigator;if(e.mediaDevices&&e.mediaDevices.getUserMedia){const i=e.mediaDevices,n=i.getUserMedia.bind(i);e.mediaDevices.getUserMedia=s=>n(vc(s))}!e.getUserMedia&&e.mediaDevices&&e.mediaDevices.getUserMedia&&(e.getUserMedia=(function(n,s,r){e.mediaDevices.getUserMedia(n).then(s,r)}).bind(e))}function vc(t){return t&&t.video!==void 0?Object.assign({},t,{video:$a(t.video)}):t}function bc(t){if(!t.RTCPeerConnection)return;const e=t.RTCPeerConnection;t.RTCPeerConnection=function(n,s){if(n&&n.iceServers){const r=[];for(let o=0;o<n.iceServers.length;o++){let a=n.iceServers[o];a.urls===void 0&&a.url?(er("RTCIceServer.url","RTCIceServer.urls"),a=JSON.parse(JSON.stringify(a)),a.urls=a.url,delete a.url,r.push(a)):r.push(n.iceServers[o])}n.iceServers=r}return new e(n,s)},t.RTCPeerConnection.prototype=e.prototype,"generateCertificate"in e&&Object.defineProperty(t.RTCPeerConnection,"generateCertificate",{get(){return e.generateCertificate}})}function yc(t){typeof t=="object"&&t.RTCTrackEvent&&"receiver"in t.RTCTrackEvent.prototype&&!("transceiver"in t.RTCTrackEvent.prototype)&&Object.defineProperty(t.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Sc(t){const e=t.RTCPeerConnection.prototype.createOffer;t.RTCPeerConnection.prototype.createOffer=function(n){if(n){typeof n.offerToReceiveAudio<"u"&&(n.offerToReceiveAudio=!!n.offerToReceiveAudio);const s=this.getTransceivers().find(o=>o.receiver.track.kind==="audio");n.offerToReceiveAudio===!1&&s?s.direction==="sendrecv"?s.setDirection?s.setDirection("sendonly"):s.direction="sendonly":s.direction==="recvonly"&&(s.setDirection?s.setDirection("inactive"):s.direction="inactive"):n.offerToReceiveAudio===!0&&!s&&this.addTransceiver("audio",{direction:"recvonly"}),typeof n.offerToReceiveVideo<"u"&&(n.offerToReceiveVideo=!!n.offerToReceiveVideo);const r=this.getTransceivers().find(o=>o.receiver.track.kind==="video");n.offerToReceiveVideo===!1&&r?r.direction==="sendrecv"?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":r.direction==="recvonly"&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):n.offerToReceiveVideo===!0&&!r&&this.addTransceiver("video",{direction:"recvonly"})}return e.apply(this,arguments)}}function kc(t){typeof t!="object"||t.AudioContext||(t.AudioContext=t.webkitAudioContext)}var ro=Object.freeze({__proto__:null,shimAudioContext:kc,shimCallbacksAPI:fc,shimConstraints:vc,shimCreateOfferLegacy:Sc,shimGetUserMedia:gc,shimLocalStreamsAPI:mc,shimRTCIceServerUrls:bc,shimRemoteStreamsAPI:pc,shimTrackEventTransceiver:yc}),Ln={exports:{}},oo;function Jh(){return oo||(oo=1,function(t){const e={};e.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},e.localCName=e.generateIdentifier(),e.splitLines=function(i){return i.trim().split(`
`).map(n=>n.trim())},e.splitSections=function(i){return i.split(`
m=`).map((s,r)=>(r>0?"m="+s:s).trim()+`\r
`)},e.getDescription=function(i){const n=e.splitSections(i);return n&&n[0]},e.getMediaSections=function(i){const n=e.splitSections(i);return n.shift(),n},e.matchPrefix=function(i,n){return e.splitLines(i).filter(s=>s.indexOf(n)===0)},e.parseCandidate=function(i){let n;i.indexOf("a=candidate:")===0?n=i.substring(12).split(" "):n=i.substring(10).split(" ");const s={foundation:n[0],component:{1:"rtp",2:"rtcp"}[n[1]]||n[1],protocol:n[2].toLowerCase(),priority:parseInt(n[3],10),ip:n[4],address:n[4],port:parseInt(n[5],10),type:n[7]};for(let r=8;r<n.length;r+=2)switch(n[r]){case"raddr":s.relatedAddress=n[r+1];break;case"rport":s.relatedPort=parseInt(n[r+1],10);break;case"tcptype":s.tcpType=n[r+1];break;case"ufrag":s.ufrag=n[r+1],s.usernameFragment=n[r+1];break;default:s[n[r]]===void 0&&(s[n[r]]=n[r+1]);break}return s},e.writeCandidate=function(i){const n=[];n.push(i.foundation);const s=i.component;s==="rtp"?n.push(1):s==="rtcp"?n.push(2):n.push(s),n.push(i.protocol.toUpperCase()),n.push(i.priority),n.push(i.address||i.ip),n.push(i.port);const r=i.type;return n.push("typ"),n.push(r),r!=="host"&&i.relatedAddress&&i.relatedPort&&(n.push("raddr"),n.push(i.relatedAddress),n.push("rport"),n.push(i.relatedPort)),i.tcpType&&i.protocol.toLowerCase()==="tcp"&&(n.push("tcptype"),n.push(i.tcpType)),(i.usernameFragment||i.ufrag)&&(n.push("ufrag"),n.push(i.usernameFragment||i.ufrag)),"candidate:"+n.join(" ")},e.parseIceOptions=function(i){return i.substring(14).split(" ")},e.parseRtpMap=function(i){let n=i.substring(9).split(" ");const s={payloadType:parseInt(n.shift(),10)};return n=n[0].split("/"),s.name=n[0],s.clockRate=parseInt(n[1],10),s.channels=n.length===3?parseInt(n[2],10):1,s.numChannels=s.channels,s},e.writeRtpMap=function(i){let n=i.payloadType;i.preferredPayloadType!==void 0&&(n=i.preferredPayloadType);const s=i.channels||i.numChannels||1;return"a=rtpmap:"+n+" "+i.name+"/"+i.clockRate+(s!==1?"/"+s:"")+`\r
`},e.parseExtmap=function(i){const n=i.substring(9).split(" ");return{id:parseInt(n[0],10),direction:n[0].indexOf("/")>0?n[0].split("/")[1]:"sendrecv",uri:n[1],attributes:n.slice(2).join(" ")}},e.writeExtmap=function(i){return"a=extmap:"+(i.id||i.preferredId)+(i.direction&&i.direction!=="sendrecv"?"/"+i.direction:"")+" "+i.uri+(i.attributes?" "+i.attributes:"")+`\r
`},e.parseFmtp=function(i){const n={};let s;const r=i.substring(i.indexOf(" ")+1).split(";");for(let o=0;o<r.length;o++)s=r[o].trim().split("="),n[s[0].trim()]=s[1];return n},e.writeFmtp=function(i){let n="",s=i.payloadType;if(i.preferredPayloadType!==void 0&&(s=i.preferredPayloadType),i.parameters&&Object.keys(i.parameters).length){const r=[];Object.keys(i.parameters).forEach(o=>{i.parameters[o]!==void 0?r.push(o+"="+i.parameters[o]):r.push(o)}),n+="a=fmtp:"+s+" "+r.join(";")+`\r
`}return n},e.parseRtcpFb=function(i){const n=i.substring(i.indexOf(" ")+1).split(" ");return{type:n.shift(),parameter:n.join(" ")}},e.writeRtcpFb=function(i){let n="",s=i.payloadType;return i.preferredPayloadType!==void 0&&(s=i.preferredPayloadType),i.rtcpFeedback&&i.rtcpFeedback.length&&i.rtcpFeedback.forEach(r=>{n+="a=rtcp-fb:"+s+" "+r.type+(r.parameter&&r.parameter.length?" "+r.parameter:"")+`\r
`}),n},e.parseSsrcMedia=function(i){const n=i.indexOf(" "),s={ssrc:parseInt(i.substring(7,n),10)},r=i.indexOf(":",n);return r>-1?(s.attribute=i.substring(n+1,r),s.value=i.substring(r+1)):s.attribute=i.substring(n+1),s},e.parseSsrcGroup=function(i){const n=i.substring(13).split(" ");return{semantics:n.shift(),ssrcs:n.map(s=>parseInt(s,10))}},e.getMid=function(i){const n=e.matchPrefix(i,"a=mid:")[0];if(n)return n.substring(6)},e.parseFingerprint=function(i){const n=i.substring(14).split(" ");return{algorithm:n[0].toLowerCase(),value:n[1].toUpperCase()}},e.getDtlsParameters=function(i,n){return{role:"auto",fingerprints:e.matchPrefix(i+n,"a=fingerprint:").map(e.parseFingerprint)}},e.writeDtlsParameters=function(i,n){let s="a=setup:"+n+`\r
`;return i.fingerprints.forEach(r=>{s+="a=fingerprint:"+r.algorithm+" "+r.value+`\r
`}),s},e.parseCryptoLine=function(i){const n=i.substring(9).split(" ");return{tag:parseInt(n[0],10),cryptoSuite:n[1],keyParams:n[2],sessionParams:n.slice(3)}},e.writeCryptoLine=function(i){return"a=crypto:"+i.tag+" "+i.cryptoSuite+" "+(typeof i.keyParams=="object"?e.writeCryptoKeyParams(i.keyParams):i.keyParams)+(i.sessionParams?" "+i.sessionParams.join(" "):"")+`\r
`},e.parseCryptoKeyParams=function(i){if(i.indexOf("inline:")!==0)return null;const n=i.substring(7).split("|");return{keyMethod:"inline",keySalt:n[0],lifeTime:n[1],mkiValue:n[2]?n[2].split(":")[0]:void 0,mkiLength:n[2]?n[2].split(":")[1]:void 0}},e.writeCryptoKeyParams=function(i){return i.keyMethod+":"+i.keySalt+(i.lifeTime?"|"+i.lifeTime:"")+(i.mkiValue&&i.mkiLength?"|"+i.mkiValue+":"+i.mkiLength:"")},e.getCryptoParameters=function(i,n){return e.matchPrefix(i+n,"a=crypto:").map(e.parseCryptoLine)},e.getIceParameters=function(i,n){const s=e.matchPrefix(i+n,"a=ice-ufrag:")[0],r=e.matchPrefix(i+n,"a=ice-pwd:")[0];return s&&r?{usernameFragment:s.substring(12),password:r.substring(10)}:null},e.writeIceParameters=function(i){let n="a=ice-ufrag:"+i.usernameFragment+`\r
a=ice-pwd:`+i.password+`\r
`;return i.iceLite&&(n+=`a=ice-lite\r
`),n},e.parseRtpParameters=function(i){const n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=e.splitLines(i)[0].split(" ");n.profile=r[2];for(let a=3;a<r.length;a++){const c=r[a],d=e.matchPrefix(i,"a=rtpmap:"+c+" ")[0];if(d){const l=e.parseRtpMap(d),u=e.matchPrefix(i,"a=fmtp:"+c+" ");switch(l.parameters=u.length?e.parseFmtp(u[0]):{},l.rtcpFeedback=e.matchPrefix(i,"a=rtcp-fb:"+c+" ").map(e.parseRtcpFb),n.codecs.push(l),l.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(l.name.toUpperCase());break}}}e.matchPrefix(i,"a=extmap:").forEach(a=>{n.headerExtensions.push(e.parseExtmap(a))});const o=e.matchPrefix(i,"a=rtcp-fb:* ").map(e.parseRtcpFb);return n.codecs.forEach(a=>{o.forEach(c=>{a.rtcpFeedback.find(l=>l.type===c.type&&l.parameter===c.parameter)||a.rtcpFeedback.push(c)})}),n},e.writeRtpDescription=function(i,n){let s="";s+="m="+i+" ",s+=n.codecs.length>0?"9":"0",s+=" "+(n.profile||"UDP/TLS/RTP/SAVPF")+" ",s+=n.codecs.map(o=>o.preferredPayloadType!==void 0?o.preferredPayloadType:o.payloadType).join(" ")+`\r
`,s+=`c=IN IP4 0.0.0.0\r
`,s+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,n.codecs.forEach(o=>{s+=e.writeRtpMap(o),s+=e.writeFmtp(o),s+=e.writeRtcpFb(o)});let r=0;return n.codecs.forEach(o=>{o.maxptime>r&&(r=o.maxptime)}),r>0&&(s+="a=maxptime:"+r+`\r
`),n.headerExtensions&&n.headerExtensions.forEach(o=>{s+=e.writeExtmap(o)}),s},e.parseRtpEncodingParameters=function(i){const n=[],s=e.parseRtpParameters(i),r=s.fecMechanisms.indexOf("RED")!==-1,o=s.fecMechanisms.indexOf("ULPFEC")!==-1,a=e.matchPrefix(i,"a=ssrc:").map(h=>e.parseSsrcMedia(h)).filter(h=>h.attribute==="cname"),c=a.length>0&&a[0].ssrc;let d;const l=e.matchPrefix(i,"a=ssrc-group:FID").map(h=>h.substring(17).split(" ").map(g=>parseInt(g,10)));l.length>0&&l[0].length>1&&l[0][0]===c&&(d=l[0][1]),s.codecs.forEach(h=>{if(h.name.toUpperCase()==="RTX"&&h.parameters.apt){let p={ssrc:c,codecPayloadType:parseInt(h.parameters.apt,10)};c&&d&&(p.rtx={ssrc:d}),n.push(p),r&&(p=JSON.parse(JSON.stringify(p)),p.fec={ssrc:c,mechanism:o?"red+ulpfec":"red"},n.push(p))}}),n.length===0&&c&&n.push({ssrc:c});let u=e.matchPrefix(i,"b=");return u.length&&(u[0].indexOf("b=TIAS:")===0?u=parseInt(u[0].substring(7),10):u[0].indexOf("b=AS:")===0?u=parseInt(u[0].substring(5),10)*1e3*.95-2e3*8:u=void 0,n.forEach(h=>{h.maxBitrate=u})),n},e.parseRtcpParameters=function(i){const n={},s=e.matchPrefix(i,"a=ssrc:").map(a=>e.parseSsrcMedia(a)).filter(a=>a.attribute==="cname")[0];s&&(n.cname=s.value,n.ssrc=s.ssrc);const r=e.matchPrefix(i,"a=rtcp-rsize");n.reducedSize=r.length>0,n.compound=r.length===0;const o=e.matchPrefix(i,"a=rtcp-mux");return n.mux=o.length>0,n},e.writeRtcpParameters=function(i){let n="";return i.reducedSize&&(n+=`a=rtcp-rsize\r
`),i.mux&&(n+=`a=rtcp-mux\r
`),i.ssrc!==void 0&&i.cname&&(n+="a=ssrc:"+i.ssrc+" cname:"+i.cname+`\r
`),n},e.parseMsid=function(i){let n;const s=e.matchPrefix(i,"a=msid:");if(s.length===1)return n=s[0].substring(7).split(" "),{stream:n[0],track:n[1]};const r=e.matchPrefix(i,"a=ssrc:").map(o=>e.parseSsrcMedia(o)).filter(o=>o.attribute==="msid");if(r.length>0)return n=r[0].value.split(" "),{stream:n[0],track:n[1]}},e.parseSctpDescription=function(i){const n=e.parseMLine(i),s=e.matchPrefix(i,"a=max-message-size:");let r;s.length>0&&(r=parseInt(s[0].substring(19),10)),isNaN(r)&&(r=65536);const o=e.matchPrefix(i,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substring(12),10),protocol:n.fmt,maxMessageSize:r};const a=e.matchPrefix(i,"a=sctpmap:");if(a.length>0){const c=a[0].substring(10).split(" ");return{port:parseInt(c[0],10),protocol:c[1],maxMessageSize:r}}},e.writeSctpDescription=function(i,n){let s=[];return i.protocol!=="DTLS/SCTP"?s=["m="+i.kind+" 9 "+i.protocol+" "+n.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+n.port+`\r
`]:s=["m="+i.kind+" 9 "+i.protocol+" "+n.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+n.port+" "+n.protocol+` 65535\r
`],n.maxMessageSize!==void 0&&s.push("a=max-message-size:"+n.maxMessageSize+`\r
`),s.join("")},e.generateSessionId=function(){return Math.random().toString().substr(2,22)},e.writeSessionBoilerplate=function(i,n,s){let r;const o=n!==void 0?n:2;return i?r=i:r=e.generateSessionId(),`v=0\r
o=`+(s||"thisisadapterortc")+" "+r+" "+o+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},e.getDirection=function(i,n){const s=e.splitLines(i);for(let r=0;r<s.length;r++)switch(s[r]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return s[r].substring(2)}return n?e.getDirection(n):"sendrecv"},e.getKind=function(i){return e.splitLines(i)[0].split(" ")[0].substring(2)},e.isRejected=function(i){return i.split(" ",2)[1]==="0"},e.parseMLine=function(i){const s=e.splitLines(i)[0].substring(2).split(" ");return{kind:s[0],port:parseInt(s[1],10),protocol:s[2],fmt:s.slice(3).join(" ")}},e.parseOLine=function(i){const s=e.matchPrefix(i,"o=")[0].substring(2).split(" ");return{username:s[0],sessionId:s[1],sessionVersion:parseInt(s[2],10),netType:s[3],addressType:s[4],address:s[5]}},e.isValidSDP=function(i){if(typeof i!="string"||i.length===0)return!1;const n=e.splitLines(i);for(let s=0;s<n.length;s++)if(n[s].length<2||n[s].charAt(1)!=="=")return!1;return!0},t.exports=e}(Ln)),Ln.exports}var Cc=Jh(),$t=Uh(Cc),Qh=cu({__proto__:null,default:$t},[Cc]);function Hi(t){if(!t.RTCIceCandidate||t.RTCIceCandidate&&"foundation"in t.RTCIceCandidate.prototype)return;const e=t.RTCIceCandidate;t.RTCIceCandidate=function(n){if(typeof n=="object"&&n.candidate&&n.candidate.indexOf("a=")===0&&(n=JSON.parse(JSON.stringify(n)),n.candidate=n.candidate.substring(2)),n.candidate&&n.candidate.length){const s=new e(n),r=$t.parseCandidate(n.candidate);for(const o in r)o in s||Object.defineProperty(s,o,{value:r[o]});return s.toJSON=function(){return{candidate:s.candidate,sdpMid:s.sdpMid,sdpMLineIndex:s.sdpMLineIndex,usernameFragment:s.usernameFragment}},s}return new e(n)},t.RTCIceCandidate.prototype=e.prototype,At(t,"icecandidate",i=>(i.candidate&&Object.defineProperty(i,"candidate",{value:new t.RTCIceCandidate(i.candidate),writable:"false"}),i))}function Ps(t){!t.RTCIceCandidate||t.RTCIceCandidate&&"relayProtocol"in t.RTCIceCandidate.prototype||At(t,"icecandidate",e=>{if(e.candidate){const i=$t.parseCandidate(e.candidate.candidate);i.type==="relay"&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[i.priority>>24])}return e})}function Ki(t,e){if(!t.RTCPeerConnection)return;"sctp"in t.RTCPeerConnection.prototype||Object.defineProperty(t.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});const i=function(a){if(!a||!a.sdp)return!1;const c=$t.splitSections(a.sdp);return c.shift(),c.some(d=>{const l=$t.parseMLine(d);return l&&l.kind==="application"&&l.protocol.indexOf("SCTP")!==-1})},n=function(a){const c=a.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(c===null||c.length<2)return-1;const d=parseInt(c[1],10);return d!==d?-1:d},s=function(a){let c=65536;return e.browser==="firefox"&&(e.version<57?a===-1?c=16384:c=2147483637:e.version<60?c=e.version===57?65535:65536:c=2147483637),c},r=function(a,c){let d=65536;e.browser==="firefox"&&e.version===57&&(d=65535);const l=$t.matchPrefix(a.sdp,"a=max-message-size:");return l.length>0?d=parseInt(l[0].substring(19),10):e.browser==="firefox"&&c!==-1&&(d=2147483637),d},o=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,e.browser==="chrome"&&e.version>=76){const{sdpSemantics:c}=this.getConfiguration();c==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(i(arguments[0])){const c=n(arguments[0]),d=s(c),l=r(arguments[0],c);let u;d===0&&l===0?u=Number.POSITIVE_INFINITY:d===0||l===0?u=Math.max(d,l):u=Math.min(d,l);const h={};Object.defineProperty(h,"maxMessageSize",{get(){return u}}),this._sctp=h}return o.apply(this,arguments)}}function $i(t){if(!(t.RTCPeerConnection&&"createDataChannel"in t.RTCPeerConnection.prototype))return;function e(n,s){const r=n.send;n.send=function(){const a=arguments[0],c=a.length||a.size||a.byteLength;if(n.readyState==="open"&&s.sctp&&c>s.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+s.sctp.maxMessageSize+" bytes)");return r.apply(n,arguments)}}const i=t.RTCPeerConnection.prototype.createDataChannel;t.RTCPeerConnection.prototype.createDataChannel=function(){const s=i.apply(this,arguments);return e(s,this),s},At(t,"datachannel",n=>(e(n.channel,n.target),n))}function Es(t){if(!t.RTCPeerConnection||"connectionState"in t.RTCPeerConnection.prototype)return;const e=t.RTCPeerConnection.prototype;Object.defineProperty(e,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(e,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(i){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),i&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=i)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(i=>{const n=e[i];e[i]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=s=>{const r=s.target;if(r._lastConnectionState!==r.connectionState){r._lastConnectionState=r.connectionState;const o=new Event("connectionstatechange",s);r.dispatchEvent(o)}return s},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}function Is(t,e){if(!t.RTCPeerConnection||e.browser==="chrome"&&e.version>=71||e.browser==="safari"&&e._safariVersion>=13.1)return;const i=t.RTCPeerConnection.prototype.setRemoteDescription;t.RTCPeerConnection.prototype.setRemoteDescription=function(s){if(s&&s.sdp&&s.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){const r=s.sdp.split(`
`).filter(o=>o.trim()!=="a=extmap-allow-mixed").join(`
`);t.RTCSessionDescription&&s instanceof t.RTCSessionDescription?arguments[0]=new t.RTCSessionDescription({type:s.type,sdp:r}):s.sdp=r}return i.apply(this,arguments)}}function Ji(t,e){if(!(t.RTCPeerConnection&&t.RTCPeerConnection.prototype))return;const i=t.RTCPeerConnection.prototype.addIceCandidate;!i||i.length===0||(t.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(e.browser==="chrome"&&e.version<78||e.browser==="firefox"&&e.version<68||e.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():i.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function Qi(t,e){if(!(t.RTCPeerConnection&&t.RTCPeerConnection.prototype))return;const i=t.RTCPeerConnection.prototype.setLocalDescription;!i||i.length===0||(t.RTCPeerConnection.prototype.setLocalDescription=function(){let s=arguments[0]||{};if(typeof s!="object"||s.type&&s.sdp)return i.apply(this,arguments);if(s={type:s.type,sdp:s.sdp},!s.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":s.type="offer";break;default:s.type="answer";break}return s.sdp||s.type!=="offer"&&s.type!=="answer"?i.apply(this,[s]):(s.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(o=>i.apply(this,[o]))})}var Yh=Object.freeze({__proto__:null,removeExtmapAllowMixed:Is,shimAddIceCandidateNullOrEmpty:Ji,shimConnectionState:Es,shimMaxMessageSize:Ki,shimParameterlessSetLocalDescription:Qi,shimRTCIceCandidate:Hi,shimRTCIceCandidateRelayProtocol:Ps,shimSendThrowTypeError:$i});function Xh(){let{window:t}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimSafari:!0};const i=Ka,n=Kh(t),s={browserDetails:n,commonShim:Yh,extractVersion:vi,disableLog:Wh,disableWarnings:Hh,sdp:Qh};switch(n.browser){case"chrome":if(!no||!Cs||!e.shimChrome)return i("Chrome shim is not included in this adapter release."),s;if(n.version===null)return i("Chrome shim can not determine version, not shimming."),s;i("adapter.js shimming chrome."),s.browserShim=no,Ji(t,n),Qi(t),Ja(t,n),Qa(t),Cs(t,n),Ya(t),tc(t,n),Xa(t),Za(t),ic(t,n),Hi(t),Ps(t),Es(t),Ki(t,n),$i(t),Is(t,n);break;case"firefox":if(!so||!Ts||!e.shimFirefox)return i("Firefox shim is not included in this adapter release."),s;i("adapter.js shimming firefox."),s.browserShim=so,Ji(t,n),Qi(t),nc(t,n),Ts(t,n),sc(t),ac(t),rc(t),oc(t),cc(t),dc(t),lc(t),uc(t),hc(t),Hi(t),Es(t),Ki(t,n),$i(t);break;case"safari":if(!ro||!e.shimSafari)return i("Safari shim is not included in this adapter release."),s;i("adapter.js shimming safari."),s.browserShim=ro,Ji(t,n),Qi(t),bc(t),Sc(t),fc(t),mc(t),pc(t),yc(t),gc(t),kc(t),Hi(t),Ps(t),Ki(t,n),$i(t),Is(t,n);break;default:i("Unsupported browser!");break}return s}Xh({window:typeof window>"u"?void 0:window});const Zh=10,Ui="lk_e2ee",em="LKFrameEncryptionKey",tm={sharedKey:!1,ratchetSalt:em,ratchetWindowSize:8,failureTolerance:Zh,keyringSize:16};var dt;(function(t){t.SetKey="setKey",t.RatchetRequest="ratchetRequest",t.KeyRatcheted="keyRatcheted"})(dt||(dt={}));var ao;(function(t){t.KeyRatcheted="keyRatcheted"})(ao||(ao={}));var ct;(function(t){t.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",t.EncryptionError="encryptionError"})(ct||(ct={}));var co;(function(t){t.Error="cryptorError"})(co||(co={}));function im(){return nm()||Rs()}function Rs(){return typeof window.RTCRtpScriptTransform<"u"}function nm(){return typeof window.RTCRtpSender<"u"&&typeof window.RTCRtpSender.prototype.createEncodedStreams<"u"}class Mf extends Ye.EventEmitter{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(),this.onKeyRatcheted=(i,n,s)=>{Q.debug("key ratcheted event received",{ratchetResult:i,participantId:n,keyIndex:s})},this.keyInfoMap=new Map,this.options=Object.assign(Object.assign({},tm),e),this.on(dt.KeyRatcheted,this.onKeyRatcheted)}onSetEncryptionKey(e,i,n){const s={key:e,participantIdentity:i,keyIndex:n};if(!this.options.sharedKey&&!i)throw new Error("participant identity needs to be passed for encryption key if sharedKey option is false");this.keyInfoMap.set("".concat(i??"shared","-").concat(n??0),s),this.emit(dt.SetKey,s)}getKeys(){return Array.from(this.keyInfoMap.values())}getOptions(){return this.options}ratchetKey(e,i){this.emit(dt.RatchetRequest,e,i)}}class gt extends Error{constructor(e,i){super(i||"an error has occured"),this.name="LiveKitError",this.code=e}}var Y;(function(t){t[t.NotAllowed=0]="NotAllowed",t[t.ServerUnreachable=1]="ServerUnreachable",t[t.InternalError=2]="InternalError",t[t.Cancelled=3]="Cancelled",t[t.LeaveRequest=4]="LeaveRequest",t[t.Timeout=5]="Timeout"})(Y||(Y={}));class re extends gt{constructor(e,i,n,s){super(1,e),this.name="ConnectionError",this.status=n,this.reason=i,this.context=s,this.reasonName=Y[i]}}class bn extends gt{constructor(e){super(21,e??"device is unsupported"),this.name="DeviceUnsupportedError"}}class $e extends gt{constructor(e){super(20,e??"track is invalid"),this.name="TrackInvalidError"}}class sm extends gt{constructor(e){super(10,e??"unsupported server"),this.name="UnsupportedServer"}}class fe extends gt{constructor(e){super(12,e??"unexpected connection state"),this.name="UnexpectedConnectionState"}}class ws extends gt{constructor(e){super(13,e??"unable to negotiate"),this.name="NegotiationError"}}class lo extends gt{constructor(e,i){super(15,e),this.name="PublishTrackError",this.status=i}}class uo extends gt{constructor(e,i){super(15,e),this.reason=i,this.reasonName=typeof i=="string"?i:Zs[i]}}var cn;(function(t){t.PermissionDenied="PermissionDenied",t.NotFound="NotFound",t.DeviceInUse="DeviceInUse",t.Other="Other"})(cn||(cn={}));(function(t){function e(i){if(i&&"name"in i)return i.name==="NotFoundError"||i.name==="DevicesNotFoundError"?t.NotFound:i.name==="NotAllowedError"||i.name==="PermissionDeniedError"?t.PermissionDenied:i.name==="NotReadableError"||i.name==="TrackStartError"?t.DeviceInUse:t.Other}t.getFailure=e})(cn||(cn={}));var ho;(function(t){t[t.InvalidKey=0]="InvalidKey",t[t.MissingKey=1]="MissingKey",t[t.InternalError=2]="InternalError"})(ho||(ho={}));var I;(function(t){t.Connected="connected",t.Reconnecting="reconnecting",t.SignalReconnecting="signalReconnecting",t.Reconnected="reconnected",t.Disconnected="disconnected",t.ConnectionStateChanged="connectionStateChanged",t.Moved="moved",t.MediaDevicesChanged="mediaDevicesChanged",t.ParticipantConnected="participantConnected",t.ParticipantDisconnected="participantDisconnected",t.TrackPublished="trackPublished",t.TrackSubscribed="trackSubscribed",t.TrackSubscriptionFailed="trackSubscriptionFailed",t.TrackUnpublished="trackUnpublished",t.TrackUnsubscribed="trackUnsubscribed",t.TrackMuted="trackMuted",t.TrackUnmuted="trackUnmuted",t.LocalTrackPublished="localTrackPublished",t.LocalTrackUnpublished="localTrackUnpublished",t.LocalAudioSilenceDetected="localAudioSilenceDetected",t.ActiveSpeakersChanged="activeSpeakersChanged",t.ParticipantMetadataChanged="participantMetadataChanged",t.ParticipantNameChanged="participantNameChanged",t.ParticipantAttributesChanged="participantAttributesChanged",t.ParticipantActive="participantActive",t.RoomMetadataChanged="roomMetadataChanged",t.DataReceived="dataReceived",t.SipDTMFReceived="sipDTMFReceived",t.TranscriptionReceived="transcriptionReceived",t.ConnectionQualityChanged="connectionQualityChanged",t.TrackStreamStateChanged="trackStreamStateChanged",t.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",t.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",t.AudioPlaybackStatusChanged="audioPlaybackChanged",t.VideoPlaybackStatusChanged="videoPlaybackChanged",t.MediaDevicesError="mediaDevicesError",t.ParticipantPermissionsChanged="participantPermissionsChanged",t.SignalConnected="signalConnected",t.RecordingStatusChanged="recordingStatusChanged",t.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",t.EncryptionError="encryptionError",t.DCBufferStatusChanged="dcBufferStatusChanged",t.ActiveDeviceChanged="activeDeviceChanged",t.ChatMessage="chatMessage",t.LocalTrackSubscribed="localTrackSubscribed",t.MetricsReceived="metricsReceived"})(I||(I={}));var A;(function(t){t.TrackPublished="trackPublished",t.TrackSubscribed="trackSubscribed",t.TrackSubscriptionFailed="trackSubscriptionFailed",t.TrackUnpublished="trackUnpublished",t.TrackUnsubscribed="trackUnsubscribed",t.TrackMuted="trackMuted",t.TrackUnmuted="trackUnmuted",t.LocalTrackPublished="localTrackPublished",t.LocalTrackUnpublished="localTrackUnpublished",t.ParticipantMetadataChanged="participantMetadataChanged",t.ParticipantNameChanged="participantNameChanged",t.DataReceived="dataReceived",t.SipDTMFReceived="sipDTMFReceived",t.TranscriptionReceived="transcriptionReceived",t.IsSpeakingChanged="isSpeakingChanged",t.ConnectionQualityChanged="connectionQualityChanged",t.TrackStreamStateChanged="trackStreamStateChanged",t.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",t.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",t.MediaDevicesError="mediaDevicesError",t.AudioStreamAcquired="audioStreamAcquired",t.ParticipantPermissionsChanged="participantPermissionsChanged",t.PCTrackAdded="pcTrackAdded",t.AttributesChanged="attributesChanged",t.LocalTrackSubscribed="localTrackSubscribed",t.ChatMessage="chatMessage",t.Active="active"})(A||(A={}));var O;(function(t){t.TransportsCreated="transportsCreated",t.Connected="connected",t.Disconnected="disconnected",t.Resuming="resuming",t.Resumed="resumed",t.Restarting="restarting",t.Restarted="restarted",t.SignalResumed="signalResumed",t.SignalRestarted="signalRestarted",t.Closing="closing",t.MediaTrackAdded="mediaTrackAdded",t.ActiveSpeakersUpdate="activeSpeakersUpdate",t.DataPacketReceived="dataPacketReceived",t.RTPVideoMapUpdate="rtpVideoMapUpdate",t.DCBufferStatusChanged="dcBufferStatusChanged",t.ParticipantUpdate="participantUpdate",t.RoomUpdate="roomUpdate",t.SpeakersChanged="speakersChanged",t.StreamStateChanged="streamStateChanged",t.ConnectionQualityUpdate="connectionQualityUpdate",t.SubscriptionError="subscriptionError",t.SubscriptionPermissionUpdate="subscriptionPermissionUpdate",t.RemoteMute="remoteMute",t.SubscribedQualityUpdate="subscribedQualityUpdate",t.LocalTrackUnpublished="localTrackUnpublished",t.LocalTrackSubscribed="localTrackSubscribed",t.Offline="offline",t.SignalRequestResponse="signalRequestResponse",t.SignalConnected="signalConnected",t.RoomMoved="roomMoved"})(O||(O={}));var D;(function(t){t.Message="message",t.Muted="muted",t.Unmuted="unmuted",t.Restarted="restarted",t.Ended="ended",t.Subscribed="subscribed",t.Unsubscribed="unsubscribed",t.UpdateSettings="updateSettings",t.UpdateSubscription="updateSubscription",t.AudioPlaybackStarted="audioPlaybackStarted",t.AudioPlaybackFailed="audioPlaybackFailed",t.AudioSilenceDetected="audioSilenceDetected",t.VisibilityChanged="visibilityChanged",t.VideoDimensionsChanged="videoDimensionsChanged",t.VideoPlaybackStarted="videoPlaybackStarted",t.VideoPlaybackFailed="videoPlaybackFailed",t.ElementAttached="elementAttached",t.ElementDetached="elementDetached",t.UpstreamPaused="upstreamPaused",t.UpstreamResumed="upstreamResumed",t.SubscriptionPermissionChanged="subscriptionPermissionChanged",t.SubscriptionStatusChanged="subscriptionStatusChanged",t.SubscriptionFailed="subscriptionFailed",t.TrackProcessorUpdate="trackProcessorUpdate",t.AudioTrackFeatureUpdate="audioTrackFeatureUpdate",t.TranscriptionReceived="transcriptionReceived",t.TimeSyncUpdate="timeSyncUpdate",t.PreConnectBufferFlushed="preConnectBufferFlushed"})(D||(D={}));function rm(t){return typeof t>"u"?t:typeof structuredClone=="function"?structuredClone(t):JSON.parse(JSON.stringify(t))}const om=/version\/(\d+(\.?_?\d+)+)/i;let Nn;function Oe(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(typeof navigator>"u")return;const i=navigator.userAgent.toLowerCase();if(Nn===void 0||e){const n=am.find(s=>{let{test:r}=s;return r.test(i)});Nn=n==null?void 0:n.describe(i)}return Nn}const am=[{test:/firefox|iceweasel|fxios/i,describe(t){return{name:"Firefox",version:Yi(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,t),os:t.toLowerCase().includes("fxios")?"iOS":void 0,osVersion:Un(t)}}},{test:/chrom|crios|crmo/i,describe(t){return{name:"Chrome",version:Yi(/(?:chrome|chromium|crios|crmo)\/(\d+(\.?_?\d+)+)/i,t),os:t.toLowerCase().includes("crios")?"iOS":void 0,osVersion:Un(t)}}},{test:/safari|applewebkit/i,describe(t){return{name:"Safari",version:Yi(om,t),os:t.includes("mobile/")?"iOS":"macOS",osVersion:Un(t)}}}];function Yi(t,e){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;const n=e.match(t);return n&&n.length>=i&&n[i]||""}function Un(t){return t.includes("mac os")?Yi(/\(.+?(\d+_\d+(:?_\d+)?)/,t,1).replace(/_/g,"."):void 0}var cm="2.13.3";const dm=cm,lm=16;class Pe{}Pe.setTimeout=function(){return setTimeout(...arguments)};Pe.setInterval=function(){return setInterval(...arguments)};Pe.clearTimeout=function(){return clearTimeout(...arguments)};Pe.clearInterval=function(){return clearInterval(...arguments)};const um=5e3,ui=[];var Le;(function(t){t[t.LOW=0]="LOW",t[t.MEDIUM=1]="MEDIUM",t[t.HIGH=2]="HIGH"})(Le||(Le={}));class P extends Ye.EventEmitter{constructor(e,i){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};var s;super(),this.attachedElements=[],this.isMuted=!1,this.streamState=P.StreamState.Active,this.isInBackground=!1,this._currentBitrate=0,this.log=Q,this.appVisibilityChangedListener=()=>{this.backgroundTimeout&&clearTimeout(this.backgroundTimeout),document.visibilityState==="hidden"?this.backgroundTimeout=setTimeout(()=>this.handleAppVisibilityChanged(),um):this.handleAppVisibilityChanged()},this.log=nt((s=n.loggerName)!==null&&s!==void 0?s:We.Track),this.loggerContextCb=n.loggerContextCb,this.setMaxListeners(100),this.kind=i,this._mediaStreamTrack=e,this._mediaStreamID=e.id,this.source=P.Source.Unknown}get logContext(){var e;return Object.assign(Object.assign({},(e=this.loggerContextCb)===null||e===void 0?void 0:e.call(this)),J(this))}get currentBitrate(){return this._currentBitrate}get mediaStreamTrack(){return this._mediaStreamTrack}get mediaStreamID(){return this._mediaStreamID}attach(e){let i="audio";this.kind===P.Kind.Video&&(i="video"),this.attachedElements.length===0&&this.kind===P.Kind.Video&&this.addAppVisibilityListener(),e||(i==="audio"&&(ui.forEach(r=>{r.parentElement===null&&!e&&(e=r)}),e&&ui.splice(ui.indexOf(e),1)),e||(e=document.createElement(i))),this.attachedElements.includes(e)||this.attachedElements.push(e),zt(this.mediaStreamTrack,e);const n=e.srcObject.getTracks(),s=n.some(r=>r.kind==="audio");return e.play().then(()=>{this.emit(s?D.AudioPlaybackStarted:D.VideoPlaybackStarted)}).catch(r=>{r.name==="NotAllowedError"?this.emit(s?D.AudioPlaybackFailed:D.VideoPlaybackFailed,r):r.name==="AbortError"?Q.debug("".concat(s?"audio":"video"," playback aborted, likely due to new play request")):Q.warn("could not playback ".concat(s?"audio":"video"),r),s&&e&&n.some(o=>o.kind==="video")&&r.name==="NotAllowedError"&&(e.muted=!0,e.play().catch(()=>{}))}),this.emit(D.ElementAttached,e),e}detach(e){try{if(e){Jt(this.mediaStreamTrack,e);const n=this.attachedElements.indexOf(e);return n>=0&&(this.attachedElements.splice(n,1),this.recycleElement(e),this.emit(D.ElementDetached,e)),e}const i=[];return this.attachedElements.forEach(n=>{Jt(this.mediaStreamTrack,n),i.push(n),this.recycleElement(n),this.emit(D.ElementDetached,n)}),this.attachedElements=[],i}finally{this.attachedElements.length===0&&this.removeAppVisibilityListener()}}stop(){this.stopMonitor(),this._mediaStreamTrack.stop()}enable(){this._mediaStreamTrack.enabled=!0}disable(){this._mediaStreamTrack.enabled=!1}stopMonitor(){this.monitorInterval&&clearInterval(this.monitorInterval),this.timeSyncHandle&&cancelAnimationFrame(this.timeSyncHandle)}updateLoggerOptions(e){e.loggerName&&(this.log=nt(e.loggerName)),e.loggerContextCb&&(this.loggerContextCb=e.loggerContextCb)}recycleElement(e){if(e instanceof HTMLAudioElement){let i=!0;e.pause(),ui.forEach(n=>{n.parentElement||(i=!1)}),i&&ui.push(e)}}handleAppVisibilityChanged(){return m(this,void 0,void 0,function*(){this.isInBackground=document.visibilityState==="hidden",!this.isInBackground&&this.kind===P.Kind.Video&&setTimeout(()=>this.attachedElements.forEach(e=>e.play().catch(()=>{})),0)})}addAppVisibilityListener(){De()?(this.isInBackground=document.visibilityState==="hidden",document.addEventListener("visibilitychange",this.appVisibilityChangedListener)):this.isInBackground=!1}removeAppVisibilityListener(){De()&&document.removeEventListener("visibilitychange",this.appVisibilityChangedListener)}}function zt(t,e){let i;e.srcObject instanceof MediaStream?i=e.srcObject:i=new MediaStream;let n;t.kind==="audio"?n=i.getAudioTracks():n=i.getVideoTracks(),n.includes(t)||(n.forEach(s=>{i.removeTrack(s)}),i.addTrack(t)),(!Ot()||!(e instanceof HTMLVideoElement))&&(e.autoplay=!0),e.muted=i.getAudioTracks().length===0,e instanceof HTMLVideoElement&&(e.playsInline=!0),e.srcObject!==i&&(e.srcObject=i,(Ot()||ei())&&e instanceof HTMLVideoElement&&setTimeout(()=>{e.srcObject=i,e.play().catch(()=>{})},0))}function Jt(t,e){if(e.srcObject instanceof MediaStream){const i=e.srcObject;i.removeTrack(t),i.getTracks().length>0?e.srcObject=i:e.srcObject=null}}(function(t){let e;(function(d){d.Audio="audio",d.Video="video",d.Unknown="unknown"})(e=t.Kind||(t.Kind={}));let i;(function(d){d.Camera="camera",d.Microphone="microphone",d.ScreenShare="screen_share",d.ScreenShareAudio="screen_share_audio",d.Unknown="unknown"})(i=t.Source||(t.Source={}));let n;(function(d){d.Active="active",d.Paused="paused",d.Unknown="unknown"})(n=t.StreamState||(t.StreamState={}));function s(d){switch(d){case e.Audio:return ze.AUDIO;case e.Video:return ze.VIDEO;default:return ze.DATA}}t.kindToProto=s;function r(d){switch(d){case ze.AUDIO:return e.Audio;case ze.VIDEO:return e.Video;default:return e.Unknown}}t.kindFromProto=r;function o(d){switch(d){case i.Camera:return pe.CAMERA;case i.Microphone:return pe.MICROPHONE;case i.ScreenShare:return pe.SCREEN_SHARE;case i.ScreenShareAudio:return pe.SCREEN_SHARE_AUDIO;default:return pe.UNKNOWN}}t.sourceToProto=o;function a(d){switch(d){case pe.CAMERA:return i.Camera;case pe.MICROPHONE:return i.Microphone;case pe.SCREEN_SHARE:return i.ScreenShare;case pe.SCREEN_SHARE_AUDIO:return i.ScreenShareAudio;default:return i.Unknown}}t.sourceFromProto=a;function c(d){switch(d){case vs.ACTIVE:return n.Active;case vs.PAUSED:return n.Paused;default:return n.Unknown}}t.streamStateFromProto=c})(P||(P={}));class ne{constructor(e,i,n,s,r){if(typeof e=="object")this.width=e.width,this.height=e.height,this.aspectRatio=e.aspectRatio,this.encoding={maxBitrate:e.maxBitrate,maxFramerate:e.maxFramerate,priority:e.priority};else if(i!==void 0&&n!==void 0)this.width=e,this.height=i,this.aspectRatio=e/i,this.encoding={maxBitrate:n,maxFramerate:s,priority:r};else throw new TypeError("Unsupported options: provide at least width, height and maxBitrate")}get resolution(){return{width:this.width,height:this.height,frameRate:this.encoding.maxFramerate,aspectRatio:this.aspectRatio}}}const hm=["vp8","h264"],mm=["vp8","h264","vp9","av1"];function pm(t){return!!hm.find(e=>e===t)}var mo;(function(t){t[t.PREFER_REGRESSION=0]="PREFER_REGRESSION",t[t.SIMULCAST=1]="SIMULCAST",t[t.REGRESSION=2]="REGRESSION"})(mo||(mo={}));var _s;(function(t){t.telephone={maxBitrate:12e3},t.speech={maxBitrate:24e3},t.music={maxBitrate:48e3},t.musicStereo={maxBitrate:64e3},t.musicHighQuality={maxBitrate:96e3},t.musicHighQualityStereo={maxBitrate:128e3}})(_s||(_s={}));const Dt={h90:new ne(160,90,9e4,20),h180:new ne(320,180,16e4,20),h216:new ne(384,216,18e4,20),h360:new ne(640,360,45e4,20),h540:new ne(960,540,8e5,25),h720:new ne(1280,720,17e5,30),h1080:new ne(1920,1080,3e6,30),h1440:new ne(2560,1440,5e6,30),h2160:new ne(3840,2160,8e6,30)},Ms={h120:new ne(160,120,7e4,20),h180:new ne(240,180,125e3,20),h240:new ne(320,240,14e4,20),h360:new ne(480,360,33e4,20),h480:new ne(640,480,5e5,20),h540:new ne(720,540,6e5,25),h720:new ne(960,720,13e5,30),h1080:new ne(1440,1080,23e5,30),h1440:new ne(1920,1440,38e5,30)},yn={h360fps3:new ne(640,360,2e5,3,"medium"),h360fps15:new ne(640,360,4e5,15,"medium"),h720fps5:new ne(1280,720,8e5,5,"medium"),h720fps15:new ne(1280,720,15e5,15,"medium"),h720fps30:new ne(1280,720,2e6,30,"medium"),h1080fps15:new ne(1920,1080,25e5,15,"medium"),h1080fps30:new ne(1920,1080,5e6,30,"medium"),original:new ne(0,0,7e6,30,"medium")},fm="|",po="https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension";function gm(t){const e=t.split(fm);return e.length>1?[e[0],t.substr(e[0].length+1)]:[t,""]}function Ae(t){return m(this,void 0,void 0,function*(){return new Promise(e=>Pe.setTimeout(e,t))})}function Ds(){return"addTransceiver"in RTCPeerConnection.prototype}function Os(){return"addTrack"in RTCPeerConnection.prototype}function vm(){if(!("getCapabilities"in RTCRtpSender)||Ot())return!1;const t=RTCRtpSender.getCapabilities("video");let e=!1;if(t){for(const i of t.codecs)if(i.mimeType==="video/AV1"){e=!0;break}}return e}function bm(){if(!("getCapabilities"in RTCRtpSender)||ei())return!1;if(Ot()){const i=Oe();if(i!=null&&i.version&&ht(i.version,"16")<0)return!1}const t=RTCRtpSender.getCapabilities("video");let e=!1;if(t){for(const i of t.codecs)if(i.mimeType==="video/VP9"){e=!0;break}}return e}function it(t){return t==="av1"||t==="vp9"}function As(t){return document?(t||(t=document.createElement("audio")),"setSinkId"in t):!1}function ym(){return typeof RTCPeerConnection>"u"?!1:Ds()||Os()}function ei(){var t;return((t=Oe())===null||t===void 0?void 0:t.name)==="Firefox"}function Ot(){var t;return((t=Oe())===null||t===void 0?void 0:t.name)==="Safari"}function fo(){const t=Oe();return(t==null?void 0:t.name)==="Safari"||(t==null?void 0:t.os)==="iOS"}function Tc(){const t=Oe();return(t==null?void 0:t.name)==="Safari"&&t.version.startsWith("17.")}function Sm(t){return t||(t=Oe()),(t==null?void 0:t.name)==="Safari"&&ht(t.version,"18.3")>0}function Pc(){var t,e;return De()?(e=(t=navigator.userAgentData)===null||t===void 0?void 0:t.mobile)!==null&&e!==void 0?e:/Tablet|iPad|Mobile|Android|BlackBerry/.test(navigator.userAgent):!1}function km(){const t=Oe(),e="17.2";if(t)return t.name!=="Safari"&&t.os!=="iOS"||t.os==="iOS"&&t.osVersion&&ht(e,t.osVersion)>=0?!0:t.name==="Safari"&&ht(e,t.version)>=0}function De(){return typeof document<"u"}function Qe(){return navigator.product=="ReactNative"}function xs(t){return t.hostname.endsWith(".livekit.cloud")||t.hostname.endsWith(".livekit.run")}function Ec(){if(global&&global.LiveKitReactNativeGlobal)return global.LiveKitReactNativeGlobal}function Ic(){if(!Qe())return;let t=Ec();if(t)return t.platform}function go(){if(De())return window.devicePixelRatio;if(Qe()){let t=Ec();if(t)return t.devicePixelRatio}return 1}function ht(t,e){const i=t.split("."),n=e.split("."),s=Math.min(i.length,n.length);for(let r=0;r<s;++r){const o=parseInt(i[r],10),a=parseInt(n[r],10);if(o>a)return 1;if(o<a)return-1;if(r===s-1&&o===a)return 0}return t===""&&e!==""?-1:e===""?1:i.length==n.length?0:i.length<n.length?-1:1}function Cm(t){for(const e of t)e.target.handleResize(e)}function Tm(t){for(const e of t)e.target.handleVisibilityChanged(e)}let Fn=null;const vo=()=>(Fn||(Fn=new ResizeObserver(Cm)),Fn);let Vn=null;const bo=()=>(Vn||(Vn=new IntersectionObserver(Tm,{root:null,rootMargin:"0px"})),Vn);function Pm(){var t;const e=new hh({sdk:Ma.JS,protocol:lm,version:dm});return Qe()&&(e.os=(t=Ic())!==null&&t!==void 0?t:""),e}function yo(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:16,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:16,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;const s=document.createElement("canvas");s.width=t,s.height=e;const r=s.getContext("2d");r==null||r.fillRect(0,0,s.width,s.height),n&&r&&(r.beginPath(),r.arc(t/2,e/2,50,0,Math.PI*2,!0),r.closePath(),r.fillStyle="grey",r.fill());const o=s.captureStream(),[a]=o.getTracks();if(!a)throw Error("Could not get empty media stream video track");return a.enabled=i,a}let hi;function Bn(){if(!hi){const t=new AudioContext,e=t.createOscillator(),i=t.createGain();i.gain.setValueAtTime(0,0);const n=t.createMediaStreamDestination();if(e.connect(i),i.connect(n),e.start(),[hi]=n.stream.getAudioTracks(),!hi)throw Error("Could not get empty media stream audio track");hi.enabled=!1}return hi.clone()}class qt{constructor(e,i){this.onFinally=i,this.promise=new Promise((n,s)=>m(this,void 0,void 0,function*(){this.resolve=n,this.reject=s,e&&(yield e(n,s))})).finally(()=>{var n;return(n=this.onFinally)===null||n===void 0?void 0:n.call(this)})}}function Em(t){return mm.includes(t)}function lt(t){if(typeof t=="string"||typeof t=="number")return t;if(Array.isArray(t))return t[0];if(t.exact)return Array.isArray(t.exact)?t.exact[0]:t.exact;if(t.ideal)return Array.isArray(t.ideal)?t.ideal[0]:t.ideal;throw Error("could not unwrap constraint")}function Im(t){return t.startsWith("http")?t.replace(/^(http)/,"ws"):t}function Ls(t){return t.startsWith("ws")?t.replace(/^(ws)/,"http"):t}function Rm(t,e){return t.segments.map(i=>{let{id:n,text:s,language:r,startTime:o,endTime:a,final:c}=i;var d;const l=(d=e.get(n))!==null&&d!==void 0?d:Date.now(),u=Date.now();return c?e.delete(n):e.set(n,l),{id:n,text:s,startTime:Number.parseInt(o.toString()),endTime:Number.parseInt(a.toString()),final:c,language:r,firstReceivedTime:l,lastReceivedTime:u}})}function wm(t){const{id:e,timestamp:i,message:n,editTimestamp:s}=t;return{id:e,timestamp:Number.parseInt(i.toString()),editTimestamp:s?Number.parseInt(s.toString()):void 0,message:n}}function So(t){switch(t.reason){case Y.LeaveRequest:return t.context;case Y.Cancelled:return Ge.CLIENT_INITIATED;case Y.NotAllowed:return Ge.USER_REJECTED;case Y.ServerUnreachable:return Ge.JOIN_FAILURE;default:return Ge.UNKNOWN_REASON}}function Xi(t){return t!==void 0?Number(t):void 0}function Ct(t){return t!==void 0?BigInt(t):void 0}function Qt(t){return!!t&&!(t instanceof MediaStreamTrack)&&t.isLocal}function Je(t){return!!t&&t.kind==P.Kind.Audio}function ii(t){return!!t&&t.kind==P.Kind.Video}function rt(t){return Qt(t)&&ii(t)}function Xe(t){return Qt(t)&&Je(t)}function Ns(t){return!!t&&!t.isLocal}function _m(t){return!!t&&!t.isLocal}function jn(t){return Ns(t)&&ii(t)}function Mm(t){return t.isLocal}function Dm(t,e){const i=[];let n=new TextEncoder().encode(t);for(;n.length>e;){let s=e;for(;s>0;){const r=n[s];if(r!==void 0&&(r&192)!==128)break;s--}i.push(n.slice(0,s)),n=n.slice(s)}return n.length>0&&i.push(n),i}function Rc(t,e,i){var n,s,r,o;const{optionsWithoutProcessor:a,audioProcessor:c,videoProcessor:d}=Dc(t??{}),l=e==null?void 0:e.processor,u=i==null?void 0:i.processor,h=a??{};return h.audio===!0&&(h.audio={}),h.video===!0&&(h.video={}),h.audio&&(Us(h.audio,e),(n=(r=h.audio).deviceId)!==null&&n!==void 0||(r.deviceId={ideal:"default"}),(c||l)&&(h.audio.processor=c??l)),h.video&&(Us(h.video,i),(s=(o=h.video).deviceId)!==null&&s!==void 0||(o.deviceId={ideal:"default"}),(d||u)&&(h.video.processor=d??u)),h}function Us(t,e){return Object.keys(e).forEach(i=>{t[i]===void 0&&(t[i]=e[i])}),t}function tr(t){var e,i,n,s;const r={};if(t.video)if(typeof t.video=="object"){const o={},a=o,c=t.video;Object.keys(c).forEach(d=>{switch(d){case"resolution":Us(a,c.resolution);break;default:a[d]=c[d]}}),r.video=o,(e=(n=r.video).deviceId)!==null&&e!==void 0||(n.deviceId={ideal:"default"})}else r.video=t.video?{deviceId:{ideal:"default"}}:!1;else r.video=!1;return t.audio?typeof t.audio=="object"?(r.audio=t.audio,(i=(s=r.audio).deviceId)!==null&&i!==void 0||(s.deviceId={ideal:"default"})):r.audio={deviceId:{ideal:"default"}}:r.audio=!1,r}function wc(t){return m(this,arguments,void 0,function(e){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:200;return function*(){const n=_c();if(n){const s=n.createAnalyser();s.fftSize=2048;const r=s.frequencyBinCount,o=new Uint8Array(r);n.createMediaStreamSource(new MediaStream([e.mediaStreamTrack])).connect(s),yield Ae(i),s.getByteTimeDomainData(o);const c=o.some(d=>d!==128&&d!==0);return n.close(),!c}return!1}()})}function _c(){var t;const e=typeof window<"u"&&(window.AudioContext||window.webkitAudioContext);if(e){const i=new e({latencyHint:"interactive"});if(i.state==="suspended"&&typeof window<"u"&&(!((t=window.document)===null||t===void 0)&&t.body)){const n=()=>m(this,void 0,void 0,function*(){var s;try{i.state==="suspended"&&(yield i.resume())}catch(r){console.warn("Error trying to auto-resume audio context",r)}(s=window.document.body)===null||s===void 0||s.removeEventListener("click",n)});window.document.body.addEventListener("click",n)}return i}}function Om(t){return t==="audioinput"?P.Source.Microphone:t==="videoinput"?P.Source.Camera:P.Source.Unknown}function Fs(t){return t===P.Source.Microphone?"audioinput":t===P.Source.Camera?"videoinput":void 0}function Mc(t){var e,i;let n=(e=t.video)!==null&&e!==void 0?e:!0;return t.resolution&&t.resolution.width>0&&t.resolution.height>0&&(n=typeof n=="boolean"?{}:n,Ot()?n=Object.assign(Object.assign({},n),{width:{max:t.resolution.width},height:{max:t.resolution.height},frameRate:t.resolution.frameRate}):n=Object.assign(Object.assign({},n),{width:{ideal:t.resolution.width},height:{ideal:t.resolution.height},frameRate:t.resolution.frameRate})),{audio:(i=t.audio)!==null&&i!==void 0?i:!1,video:n,controller:t.controller,selfBrowserSurface:t.selfBrowserSurface,surfaceSwitching:t.surfaceSwitching,systemAudio:t.systemAudio,preferCurrentTab:t.preferCurrentTab}}function Zi(t){return t.split("/")[1].toLowerCase()}function Am(t){const e=[];return t.forEach(i=>{i.track!==void 0&&e.push(new Ys({cid:i.track.mediaStreamID,track:i.trackInfo}))}),e}function J(t){return"mediaStreamTrack"in t?{trackID:t.sid,source:t.source,muted:t.isMuted,enabled:t.mediaStreamTrack.enabled,kind:t.kind,streamID:t.mediaStreamID,streamTrackID:t.mediaStreamTrack.id}:{trackID:t.trackSid,enabled:t.isEnabled,muted:t.isMuted,trackInfo:Object.assign({mimeType:t.mimeType,name:t.trackName,encrypted:t.isEncrypted,kind:t.kind,source:t.source},t.track?J(t.track):{})}}function xm(){return typeof RTCRtpReceiver<"u"&&"getSynchronizationSources"in RTCRtpReceiver}function Lm(t,e){var i;t===void 0&&(t={}),e===void 0&&(e={});const n=[...Object.keys(e),...Object.keys(t)],s={};for(const r of n)t[r]!==e[r]&&(s[r]=(i=e[r])!==null&&i!==void 0?i:"");return s}function Dc(t){const e=Object.assign({},t);let i,n;return typeof e.audio=="object"&&e.audio.processor&&(i=e.audio.processor,e.audio=Object.assign(Object.assign({},e.audio),{processor:void 0})),typeof e.video=="object"&&e.video.processor&&(n=e.video.processor,e.video=Object.assign(Object.assign({},e.video),{processor:void 0})),{audioProcessor:i,videoProcessor:n,optionsWithoutProcessor:rm(e)}}function Nm(t){switch(t){case pe.CAMERA:return P.Source.Camera;case pe.MICROPHONE:return P.Source.Microphone;case pe.SCREEN_SHARE:return P.Source.ScreenShare;case pe.SCREEN_SHARE_AUDIO:return P.Source.ScreenShareAudio;default:return P.Source.Unknown}}class Um extends Ye.EventEmitter{constructor(e){super(),this.onWorkerMessage=i=>{var n,s;const{kind:r,data:o}=i.data;switch(r){case"error":Q.error(o.error.message),this.emit(ct.EncryptionError,o.error);break;case"initAck":o.enabled&&this.keyProvider.getKeys().forEach(a=>{this.postKey(a)});break;case"enable":if(o.enabled&&this.keyProvider.getKeys().forEach(a=>{this.postKey(a)}),this.encryptionEnabled!==o.enabled&&o.participantIdentity===((n=this.room)===null||n===void 0?void 0:n.localParticipant.identity))this.emit(ct.ParticipantEncryptionStatusChanged,o.enabled,this.room.localParticipant),this.encryptionEnabled=o.enabled;else if(o.participantIdentity){const a=(s=this.room)===null||s===void 0?void 0:s.getParticipantByIdentity(o.participantIdentity);if(!a)throw TypeError("couldn't set encryption status, participant not found".concat(o.participantIdentity));this.emit(ct.ParticipantEncryptionStatusChanged,o.enabled,a)}break;case"ratchetKey":this.keyProvider.emit(dt.KeyRatcheted,o.ratchetResult,o.participantIdentity,o.keyIndex);break}},this.onWorkerError=i=>{Q.error("e2ee worker encountered an error:",{error:i.error}),this.emit(ct.EncryptionError,i.error)},this.keyProvider=e.keyProvider,this.worker=e.worker,this.encryptionEnabled=!1}setup(e){if(!im())throw new bn("tried to setup end-to-end encryption on an unsupported browser");if(Q.info("setting up e2ee"),e!==this.room){this.room=e,this.setupEventListeners(e,this.keyProvider);const i={kind:"init",data:{keyProviderOptions:this.keyProvider.getOptions(),loglevel:Bh.getLevel()}};this.worker&&(Q.info("initializing worker",{worker:this.worker}),this.worker.onmessage=this.onWorkerMessage,this.worker.onerror=this.onWorkerError,this.worker.postMessage(i))}}setParticipantCryptorEnabled(e,i){Q.debug("set e2ee to ".concat(e," for participant ").concat(i)),this.postEnable(e,i)}setSifTrailer(e){!e||e.length===0?Q.warn("ignoring server sent trailer as it's empty"):this.postSifTrailer(e)}setupEngine(e){e.on(O.RTPVideoMapUpdate,i=>{this.postRTPMap(i)})}setupEventListeners(e,i){e.on(I.TrackPublished,(n,s)=>this.setParticipantCryptorEnabled(n.trackInfo.encryption!==xe.NONE,s.identity)),e.on(I.ConnectionStateChanged,n=>{n===te.Connected&&e.remoteParticipants.forEach(s=>{s.trackPublications.forEach(r=>{this.setParticipantCryptorEnabled(r.trackInfo.encryption!==xe.NONE,s.identity)})})}).on(I.TrackUnsubscribed,(n,s,r)=>{var o;const a={kind:"removeTransform",data:{participantIdentity:r.identity,trackId:n.mediaStreamID}};(o=this.worker)===null||o===void 0||o.postMessage(a)}).on(I.TrackSubscribed,(n,s,r)=>{this.setupE2EEReceiver(n,r.identity,s.trackInfo)}).on(I.SignalConnected,()=>{if(!this.room)throw new TypeError("expected room to be present on signal connect");i.getKeys().forEach(n=>{this.postKey(n)}),this.setParticipantCryptorEnabled(this.room.localParticipant.isE2EEEnabled,this.room.localParticipant.identity)}),e.localParticipant.on(A.LocalTrackPublished,n=>m(this,void 0,void 0,function*(){this.setupE2EESender(n.track,n.track.sender)})),i.on(dt.SetKey,n=>this.postKey(n)).on(dt.RatchetRequest,(n,s)=>this.postRatchetRequest(n,s))}postRatchetRequest(e,i){if(!this.worker)throw Error("could not ratchet key, worker is missing");const n={kind:"ratchetRequest",data:{participantIdentity:e,keyIndex:i}};this.worker.postMessage(n)}postKey(e){let{key:i,participantIdentity:n,keyIndex:s}=e;var r;if(!this.worker)throw Error("could not set key, worker is missing");const o={kind:"setKey",data:{participantIdentity:n,isPublisher:n===((r=this.room)===null||r===void 0?void 0:r.localParticipant.identity),key:i,keyIndex:s}};this.worker.postMessage(o)}postEnable(e,i){if(this.worker){const n={kind:"enable",data:{enabled:e,participantIdentity:i}};this.worker.postMessage(n)}else throw new ReferenceError("failed to enable e2ee, worker is not ready")}postRTPMap(e){var i;if(!this.worker)throw TypeError("could not post rtp map, worker is missing");if(!(!((i=this.room)===null||i===void 0)&&i.localParticipant.identity))throw TypeError("could not post rtp map, local participant identity is missing");const n={kind:"setRTPMap",data:{map:e,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(n)}postSifTrailer(e){if(!this.worker)throw Error("could not post SIF trailer, worker is missing");const i={kind:"setSifTrailer",data:{trailer:e}};this.worker.postMessage(i)}setupE2EEReceiver(e,i,n){if(e.receiver){if(!(n!=null&&n.mimeType)||n.mimeType==="")throw new TypeError("MimeType missing from trackInfo, cannot set up E2EE cryptor");this.handleReceiver(e.receiver,e.mediaStreamID,i,e.kind==="video"?Zi(n.mimeType):void 0)}}setupE2EESender(e,i){if(!Qt(e)||!i){i||Q.warn("early return because sender is not ready");return}this.handleSender(i,e.mediaStreamID,void 0)}handleReceiver(e,i,n,s){return m(this,void 0,void 0,function*(){if(this.worker){if(Rs()){const r={kind:"decode",participantIdentity:n,trackId:i,codec:s};e.transform=new RTCRtpScriptTransform(this.worker,r)}else{if(Ui in e&&s){const c={kind:"updateCodec",data:{trackId:i,codec:s,participantIdentity:n}};this.worker.postMessage(c);return}let r=e.writableStream,o=e.readableStream;if(!r||!o){const c=e.createEncodedStreams();e.writableStream=c.writable,r=c.writable,e.readableStream=c.readable,o=c.readable}const a={kind:"decode",data:{readableStream:o,writableStream:r,trackId:i,codec:s,participantIdentity:n}};this.worker.postMessage(a,[o,r])}e[Ui]=!0}})}handleSender(e,i,n){var s;if(!(Ui in e||!this.worker)){if(!(!((s=this.room)===null||s===void 0)&&s.localParticipant.identity)||this.room.localParticipant.identity==="")throw TypeError("local identity needs to be known in order to set up encrypted sender");if(Rs()){Q.info("initialize script transform");const r={kind:"encode",participantIdentity:this.room.localParticipant.identity,trackId:i,codec:n};e.transform=new RTCRtpScriptTransform(this.worker,r)}else{Q.info("initialize encoded streams");const r=e.createEncodedStreams(),o={kind:"encode",data:{readableStream:r.readable,writableStream:r.writable,codec:n,trackId:i,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(o,[r.readable,r.writable])}e[Ui]=!0}}}const zn="default";class ke{constructor(){this._previousDevices=[]}static getInstance(){return this.instance===void 0&&(this.instance=new ke),this.instance}get previousDevices(){return this._previousDevices}getDevices(e){return m(this,arguments,void 0,function(i){var n=this;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return function*(){var r;if(((r=ke.userMediaPromiseMap)===null||r===void 0?void 0:r.size)>0){Q.debug("awaiting getUserMedia promise");try{i?yield ke.userMediaPromiseMap.get(i):yield Promise.all(ke.userMediaPromiseMap.values())}catch{Q.warn("error waiting for media permissons")}}let o=yield navigator.mediaDevices.enumerateDevices();if(s&&!(Ot()&&n.hasDeviceInUse(i))&&(o.filter(c=>c.kind===i).length===0||o.some(c=>{const d=c.label==="",l=i?c.kind===i:!0;return d&&l}))){const c={video:i!=="audioinput"&&i!=="audiooutput",audio:i!=="videoinput"&&{deviceId:{ideal:"default"}}},d=yield navigator.mediaDevices.getUserMedia(c);o=yield navigator.mediaDevices.enumerateDevices(),d.getTracks().forEach(l=>{l.stop()})}return n._previousDevices=o,i&&(o=o.filter(a=>a.kind===i)),o}()})}normalizeDeviceId(e,i,n){return m(this,void 0,void 0,function*(){if(i!==zn)return i;const s=yield this.getDevices(e),r=s.find(a=>a.deviceId===zn);if(!r){Q.warn("could not reliably determine default device");return}const o=s.find(a=>a.deviceId!==zn&&a.groupId===(n??r.groupId));if(!o){Q.warn("could not reliably determine default device");return}return o==null?void 0:o.deviceId})}hasDeviceInUse(e){return e?ke.userMediaPromiseMap.has(e):ke.userMediaPromiseMap.size>0}}ke.mediaDeviceKinds=["audioinput","audiooutput","videoinput"];ke.userMediaPromiseMap=new Map;var Ti;(function(t){t[t.WAITING=0]="WAITING",t[t.RUNNING=1]="RUNNING",t[t.COMPLETED=2]="COMPLETED"})(Ti||(Ti={}));class Fm{constructor(){this.pendingTasks=new Map,this.taskMutex=new Me,this.nextTaskIndex=0}run(e){return m(this,void 0,void 0,function*(){const i={id:this.nextTaskIndex++,enqueuedAt:Date.now(),status:Ti.WAITING};this.pendingTasks.set(i.id,i);const n=yield this.taskMutex.lock();try{return i.executedAt=Date.now(),i.status=Ti.RUNNING,yield e()}finally{i.status=Ti.COMPLETED,this.pendingTasks.delete(i.id),n()}})}flush(){return m(this,void 0,void 0,function*(){return this.run(()=>m(this,void 0,void 0,function*(){}))})}snapshot(){return Array.from(this.pendingTasks.values())}}function Vm(t,e){const i=new URL(Im(t));return e.forEach((n,s)=>{i.searchParams.set(s,n)}),Oc(i,"rtc")}function Bm(t){const e=new URL(Ls(t));return Oc(e,"validate")}function jm(t){return t.endsWith("/")?t:"".concat(t,"/")}function Oc(t,e){return t.pathname="".concat(jm(t.pathname)).concat(e),t.toString()}const zm=["syncState","trickle","offer","answer","simulate","leave"];function qm(t){const e=zm.indexOf(t.case)>=0;return Q.trace("request allowed to bypass queue:",{canPass:e,req:t}),e}var oe;(function(t){t[t.CONNECTING=0]="CONNECTING",t[t.CONNECTED=1]="CONNECTED",t[t.RECONNECTING=2]="RECONNECTING",t[t.DISCONNECTING=3]="DISCONNECTING",t[t.DISCONNECTED=4]="DISCONNECTED"})(oe||(oe={}));class ir{get currentState(){return this.state}get isDisconnected(){return this.state===oe.DISCONNECTING||this.state===oe.DISCONNECTED}get isEstablishingConnection(){return this.state===oe.CONNECTING||this.state===oe.RECONNECTING}getNextRequestId(){return this._requestId+=1,this._requestId}constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};var n;this.rtt=0,this.state=oe.DISCONNECTED,this.log=Q,this._requestId=0,this.resetCallbacks=()=>{this.onAnswer=void 0,this.onLeave=void 0,this.onLocalTrackPublished=void 0,this.onLocalTrackUnpublished=void 0,this.onNegotiateRequested=void 0,this.onOffer=void 0,this.onRemoteMuteChanged=void 0,this.onSubscribedQualityUpdate=void 0,this.onTokenRefresh=void 0,this.onTrickle=void 0,this.onClose=void 0},this.log=nt((n=i.loggerName)!==null&&n!==void 0?n:We.Signal),this.loggerContextCb=i.loggerContextCb,this.useJSON=e,this.requestQueue=new Fm,this.queuedRequests=[],this.closingLock=new Me,this.connectionLock=new Me,this.state=oe.DISCONNECTED}get logContext(){var e,i;return(i=(e=this.loggerContextCb)===null||e===void 0?void 0:e.call(this))!==null&&i!==void 0?i:{}}join(e,i,n,s){return m(this,void 0,void 0,function*(){return this.state=oe.CONNECTING,this.options=n,yield this.connect(e,i,n,s)})}reconnect(e,i,n,s){return m(this,void 0,void 0,function*(){if(!this.options){this.log.warn("attempted to reconnect without signal options being set, ignoring",this.logContext);return}return this.state=oe.RECONNECTING,this.clearPingInterval(),yield this.connect(e,i,Object.assign(Object.assign({},this.options),{reconnect:!0,sid:n,reconnectReason:s}))})}connect(e,i,n,s){this.connectOptions=n;const r=Pm(),o=Gm(i,r,n),a=Vm(e,o),c=Bm(a);return new Promise((d,l)=>m(this,void 0,void 0,function*(){const u=yield this.connectionLock.lock();try{const h=()=>m(this,void 0,void 0,function*(){this.close(),clearTimeout(p),l(new re("room connection has been cancelled (signal)",Y.Cancelled))}),p=setTimeout(()=>{this.close(),l(new re("room connection has timed out (signal)",Y.ServerUnreachable))},n.websocketTimeout);s!=null&&s.aborted&&h(),s==null||s.addEventListener("abort",h);const g=new URL(a);g.searchParams.has("access_token")&&g.searchParams.set("access_token","<redacted>"),this.log.debug("connecting to ".concat(g),Object.assign({reconnect:n.reconnect,reconnectReason:n.reconnectReason},this.logContext)),this.ws&&(yield this.close(!1)),this.ws=new WebSocket(a),this.ws.binaryType="arraybuffer",this.ws.onopen=()=>{clearTimeout(p)},this.ws.onerror=f=>m(this,void 0,void 0,function*(){if(this.state!==oe.CONNECTED){this.state=oe.DISCONNECTED,clearTimeout(p);try{const y=yield fetch(c);if(y.status.toFixed(0).startsWith("4")){const S=yield y.text();l(new re(S,Y.NotAllowed,y.status))}else l(new re("Encountered unknown websocket error during connection: ".concat(f.toString()),Y.InternalError,y.status))}catch(y){l(new re(y instanceof Error?y.message:"server was not reachable",Y.ServerUnreachable))}return}this.handleWSError(f)}),this.ws.onmessage=f=>m(this,void 0,void 0,function*(){var y,S,x;let M;if(typeof f.data=="string"){const b=JSON.parse(f.data);M=Qr.fromJson(b,{ignoreUnknownFields:!0})}else if(f.data instanceof ArrayBuffer)M=Qr.fromBinary(new Uint8Array(f.data));else{this.log.error("could not decode websocket message: ".concat(typeof f.data),this.logContext);return}if(this.state!==oe.CONNECTED){let b=!1;if(((y=M.message)===null||y===void 0?void 0:y.case)==="join"?(this.state=oe.CONNECTED,s==null||s.removeEventListener("abort",h),this.pingTimeoutDuration=M.message.value.pingTimeout,this.pingIntervalDuration=M.message.value.pingInterval,this.pingTimeoutDuration&&this.pingTimeoutDuration>0&&(this.log.debug("ping config",Object.assign(Object.assign({},this.logContext),{timeout:this.pingTimeoutDuration,interval:this.pingIntervalDuration})),this.startPingInterval()),d(M.message.value)):this.state===oe.RECONNECTING&&M.message.case!=="leave"?(this.state=oe.CONNECTED,s==null||s.removeEventListener("abort",h),this.startPingInterval(),((S=M.message)===null||S===void 0?void 0:S.case)==="reconnect"?d(M.message.value):(this.log.debug("declaring signal reconnected without reconnect response received",this.logContext),d(void 0),b=!0)):this.isEstablishingConnection&&M.message.case==="leave"?l(new re("Received leave request while trying to (re)connect",Y.LeaveRequest,void 0,M.message.value.reason)):n.reconnect||l(new re("did not receive join response, got ".concat((x=M.message)===null||x===void 0?void 0:x.case," instead"),Y.InternalError)),!b)return}this.signalLatency&&(yield Ae(this.signalLatency)),this.handleSignalResponse(M)}),this.ws.onclose=f=>{this.isEstablishingConnection&&l(new re("Websocket got closed during a (re)connection attempt",Y.InternalError)),this.log.warn("websocket closed",Object.assign(Object.assign({},this.logContext),{reason:f.reason,code:f.code,wasClean:f.wasClean,state:this.state})),this.handleOnClose(f.reason)}}finally{u()}}))}close(){return m(this,arguments,void 0,function(){var e=this;let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return function*(){const n=yield e.closingLock.lock();try{if(e.clearPingInterval(),i&&(e.state=oe.DISCONNECTING),e.ws){e.ws.onmessage=null,e.ws.onopen=null,e.ws.onclose=null;const s=new Promise(r=>{e.ws?e.ws.onclose=()=>{r()}:r()});e.ws.readyState<e.ws.CLOSING&&(e.ws.close(),yield Promise.race([s,Ae(250)])),e.ws=void 0}}finally{i&&(e.state=oe.DISCONNECTED),n()}}()})}sendOffer(e){this.log.debug("sending offer",Object.assign(Object.assign({},this.logContext),{offerSdp:e.sdp})),this.sendRequest({case:"offer",value:dn(e)})}sendAnswer(e){return this.log.debug("sending answer",Object.assign(Object.assign({},this.logContext),{answerSdp:e.sdp})),this.sendRequest({case:"answer",value:dn(e)})}sendIceCandidate(e,i){return this.log.debug("sending ice candidate",Object.assign(Object.assign({},this.logContext),{candidate:e})),this.sendRequest({case:"trickle",value:new Js({candidateInit:JSON.stringify(e),target:i})})}sendMuteTrack(e,i){return this.sendRequest({case:"mute",value:new Qs({sid:e,muted:i})})}sendAddTrack(e){return this.sendRequest({case:"addTrack",value:e})}sendUpdateLocalMetadata(e,i){return m(this,arguments,void 0,function(n,s){var r=this;let o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return function*(){const a=r.getNextRequestId();return yield r.sendRequest({case:"updateMetadata",value:new Fa({requestId:a,metadata:n,name:s,attributes:o})}),a}()})}sendUpdateTrackSettings(e){this.sendRequest({case:"trackSetting",value:e})}sendUpdateSubscription(e){return this.sendRequest({case:"subscription",value:e})}sendSyncState(e){return this.sendRequest({case:"syncState",value:e})}sendUpdateVideoLayers(e,i){return this.sendRequest({case:"updateLayers",value:new Ua({trackSid:e,layers:i})})}sendUpdateSubscriptionPermissions(e,i){return this.sendRequest({case:"subscriptionPermission",value:new ja({allParticipants:e,trackPermissions:i})})}sendSimulateScenario(e){return this.sendRequest({case:"simulate",value:e})}sendPing(){return Promise.all([this.sendRequest({case:"ping",value:de.parse(Date.now())}),this.sendRequest({case:"pingReq",value:new Ga({timestamp:de.parse(Date.now()),rtt:de.parse(this.rtt)})})])}sendUpdateLocalAudioTrack(e,i){return this.sendRequest({case:"updateAudioTrack",value:new Na({trackSid:e,features:i})})}sendLeave(){return this.sendRequest({case:"leave",value:new gn({reason:Ge.CLIENT_INITIATED,action:Kt.DISCONNECT})})}sendRequest(e){return m(this,arguments,void 0,function(i){var n=this;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return function*(){if(!s&&!qm(i)&&n.state===oe.RECONNECTING){n.queuedRequests.push(()=>m(n,void 0,void 0,function*(){yield this.sendRequest(i,!0)}));return}if(s||(yield n.requestQueue.flush()),n.signalLatency&&(yield Ae(n.signalLatency)),!n.ws||n.ws.readyState!==n.ws.OPEN){n.log.error("cannot send signal request before connected, type: ".concat(i==null?void 0:i.case),n.logContext);return}const o=new fh({message:i});try{n.useJSON?n.ws.send(o.toJsonString()):n.ws.send(o.toBinary())}catch(a){n.log.error("error sending signal message",Object.assign(Object.assign({},n.logContext),{error:a}))}}()})}handleSignalResponse(e){var i,n;const s=e.message;if(s==null){this.log.debug("received unsupported message",this.logContext);return}let r=!1;if(s.case==="answer"){const o=ko(s.value);this.onAnswer&&this.onAnswer(o)}else if(s.case==="offer"){const o=ko(s.value);this.onOffer&&this.onOffer(o)}else if(s.case==="trickle"){const o=JSON.parse(s.value.candidateInit);this.onTrickle&&this.onTrickle(o,s.value.target)}else s.case==="update"?this.onParticipantUpdate&&this.onParticipantUpdate((i=s.value.participants)!==null&&i!==void 0?i:[]):s.case==="trackPublished"?this.onLocalTrackPublished&&this.onLocalTrackPublished(s.value):s.case==="speakersChanged"?this.onSpeakersChanged&&this.onSpeakersChanged((n=s.value.speakers)!==null&&n!==void 0?n:[]):s.case==="leave"?this.onLeave&&this.onLeave(s.value):s.case==="mute"?this.onRemoteMuteChanged&&this.onRemoteMuteChanged(s.value.sid,s.value.muted):s.case==="roomUpdate"?this.onRoomUpdate&&s.value.room&&this.onRoomUpdate(s.value.room):s.case==="connectionQuality"?this.onConnectionQuality&&this.onConnectionQuality(s.value):s.case==="streamStateUpdate"?this.onStreamStateUpdate&&this.onStreamStateUpdate(s.value):s.case==="subscribedQualityUpdate"?this.onSubscribedQualityUpdate&&this.onSubscribedQualityUpdate(s.value):s.case==="subscriptionPermissionUpdate"?this.onSubscriptionPermissionUpdate&&this.onSubscriptionPermissionUpdate(s.value):s.case==="refreshToken"?this.onTokenRefresh&&this.onTokenRefresh(s.value):s.case==="trackUnpublished"?this.onLocalTrackUnpublished&&this.onLocalTrackUnpublished(s.value):s.case==="subscriptionResponse"?this.onSubscriptionError&&this.onSubscriptionError(s.value):s.case==="pong"||(s.case==="pongResp"?(this.rtt=Date.now()-Number.parseInt(s.value.lastPingTimestamp.toString()),this.resetPingTimeout(),r=!0):s.case==="requestResponse"?this.onRequestResponse&&this.onRequestResponse(s.value):s.case==="trackSubscribed"?this.onLocalTrackSubscribed&&this.onLocalTrackSubscribed(s.value.trackSid):s.case==="roomMoved"?(this.onTokenRefresh&&this.onTokenRefresh(s.value.token),this.onRoomMoved&&this.onRoomMoved(s.value)):this.log.debug("unsupported message",Object.assign(Object.assign({},this.logContext),{msgCase:s.case})));r||this.resetPingTimeout()}setReconnected(){for(;this.queuedRequests.length>0;){const e=this.queuedRequests.shift();e&&this.requestQueue.run(e)}}handleOnClose(e){return m(this,void 0,void 0,function*(){if(this.state===oe.DISCONNECTED)return;const i=this.onClose;yield this.close(),this.log.debug("websocket connection closed: ".concat(e),Object.assign(Object.assign({},this.logContext),{reason:e})),i&&i(e)})}handleWSError(e){this.log.error("websocket error",Object.assign(Object.assign({},this.logContext),{error:e}))}resetPingTimeout(){if(this.clearPingTimeout(),!this.pingTimeoutDuration){this.log.warn("ping timeout duration not set",this.logContext);return}this.pingTimeout=Pe.setTimeout(()=>{this.log.warn("ping timeout triggered. last pong received at: ".concat(new Date(Date.now()-this.pingTimeoutDuration*1e3).toUTCString()),this.logContext),this.handleOnClose("ping timeout")},this.pingTimeoutDuration*1e3)}clearPingTimeout(){this.pingTimeout&&Pe.clearTimeout(this.pingTimeout)}startPingInterval(){if(this.clearPingInterval(),this.resetPingTimeout(),!this.pingIntervalDuration){this.log.warn("ping interval duration not set",this.logContext);return}this.log.debug("start ping interval",this.logContext),this.pingInterval=Pe.setInterval(()=>{this.sendPing()},this.pingIntervalDuration*1e3)}clearPingInterval(){this.log.debug("clearing ping interval",this.logContext),this.clearPingTimeout(),this.pingInterval&&Pe.clearInterval(this.pingInterval)}}function ko(t){const e={type:"offer",sdp:t.sdp};switch(t.type){case"answer":case"offer":case"pranswer":case"rollback":e.type=t.type;break}return e}function dn(t){return new Mt({sdp:t.sdp,type:t.type})}function Gm(t,e,i){var n;const s=new URLSearchParams;return s.set("access_token",t),i.reconnect&&(s.set("reconnect","1"),i.sid&&s.set("sid",i.sid)),s.set("auto_subscribe",i.autoSubscribe?"1":"0"),s.set("sdk",Qe()?"reactnative":"js"),s.set("version",e.version),s.set("protocol",e.protocol.toString()),e.deviceModel&&s.set("device_model",e.deviceModel),e.os&&s.set("os",e.os),e.osVersion&&s.set("os_version",e.osVersion),e.browser&&s.set("browser",e.browser),e.browserVersion&&s.set("browser_version",e.browserVersion),i.adaptiveStream&&s.set("adaptive_stream","1"),i.reconnectReason&&s.set("reconnect_reason",i.reconnectReason.toString()),!((n=navigator.connection)===null||n===void 0)&&n.type&&s.set("network",navigator.connection.type),s}var Fe={},qn={},Gn={exports:{}},Co;function nr(){if(Co)return Gn.exports;Co=1;var t=Gn.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return e.address!=null?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return e.subtype!=null?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return e.sessionConfig!=null?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var i="candidate:%s %d %s %d %s %d typ %s";return i+=e.raddr!=null?" raddr %s rport %d":"%v%v",i+=e.tcptype!=null?" tcptype %s":"%v",e.generation!=null&&(i+=" generation %d"),i+=e["network-id"]!=null?" network-id %d":"%v",i+=e["network-cost"]!=null?" network-cost %d":"%v",i}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var i="ssrc:%d";return e.attribute!=null&&(i+=" %s",e.value!=null&&(i+=":%s")),i}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return e.maxMessageSize!=null?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(e.clksrcExt!=null?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var i="mediaclk:";return i+=e.id!=null?"id=%s %s":"%v%s",i+=e.mediaClockValue!=null?"=%s":"",i+=e.rateNumerator!=null?" rate=%s":"",i+=e.rateDenominator!=null?"/%s":"",i}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};return Object.keys(t).forEach(function(e){var i=t[e];i.forEach(function(n){n.reg||(n.reg=/(.*)/),n.format||(n.format="%s")})}),Gn.exports}var To;function Wm(){return To||(To=1,function(t){var e=function(a){return String(Number(a))===a?Number(a):a},i=function(a,c,d,l){if(l&&!d)c[l]=e(a[1]);else for(var u=0;u<d.length;u+=1)a[u+1]!=null&&(c[d[u]]=e(a[u+1]))},n=function(a,c,d){var l=a.name&&a.names;a.push&&!c[a.push]?c[a.push]=[]:l&&!c[a.name]&&(c[a.name]={});var u=a.push?{}:l?c[a.name]:c;i(d.match(a.reg),u,a.names,a.name),a.push&&c[a.push].push(u)},s=nr(),r=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(a){var c={},d=[],l=c;return a.split(/(\r\n|\r|\n)/).filter(r).forEach(function(u){var h=u[0],p=u.slice(2);h==="m"&&(d.push({rtp:[],fmtp:[]}),l=d[d.length-1]);for(var g=0;g<(s[h]||[]).length;g+=1){var f=s[h][g];if(f.reg.test(p))return n(f,l,p)}}),c.media=d,c};var o=function(a,c){var d=c.split(/=(.+)/,2);return d.length===2?a[d[0]]=e(d[1]):d.length===1&&c.length>1&&(a[d[0]]=void 0),a};t.parseParams=function(a){return a.split(/;\s?/).reduce(o,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(a){return a.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(a){for(var c=[],d=a.split(" ").map(e),l=0;l<d.length;l+=3)c.push({component:d[l],ip:d[l+1],port:d[l+2]});return c},t.parseImageAttributes=function(a){return a.split(" ").map(function(c){return c.substring(1,c.length-1).split(",").reduce(o,{})})},t.parseSimulcastStreamList=function(a){return a.split(";").map(function(c){return c.split(",").map(function(d){var l,u=!1;return d[0]!=="~"?l=e(d):(l=e(d.substring(1,d.length)),u=!0),{scid:l,paused:u}})})}}(qn)),qn}var Wn,Po;function Hm(){if(Po)return Wn;Po=1;var t=nr(),e=/%[sdv%]/g,i=function(o){var a=1,c=arguments,d=c.length;return o.replace(e,function(l){if(a>=d)return l;var u=c[a];switch(a+=1,l){case"%%":return"%";case"%s":return String(u);case"%d":return Number(u);case"%v":return""}})},n=function(o,a,c){var d=a.format instanceof Function?a.format(a.push?c:c[a.name]):a.format,l=[o+"="+d];if(a.names)for(var u=0;u<a.names.length;u+=1){var h=a.names[u];a.name?l.push(c[a.name][h]):l.push(c[a.names[u]])}else l.push(c[a.name]);return i.apply(null,l)},s=["v","o","s","i","u","e","p","c","b","t","r","z","a"],r=["i","c","b","a"];return Wn=function(o,a){a=a||{},o.version==null&&(o.version=0),o.name==null&&(o.name=" "),o.media.forEach(function(u){u.payloads==null&&(u.payloads="")});var c=a.outerOrder||s,d=a.innerOrder||r,l=[];return c.forEach(function(u){t[u].forEach(function(h){h.name in o&&o[h.name]!=null?l.push(n(u,h,o)):h.push in o&&o[h.push]!=null&&o[h.push].forEach(function(p){l.push(n(u,h,p))})})}),o.media.forEach(function(u){l.push(n("m",t.m[0],u)),d.forEach(function(h){t[h].forEach(function(p){p.name in u&&u[p.name]!=null?l.push(n(h,p,u)):p.push in u&&u[p.push]!=null&&u[p.push].forEach(function(g){l.push(n(h,p,g))})})})}),l.join(`\r
`)+`\r
`},Wn}var Eo;function Km(){if(Eo)return Fe;Eo=1;var t=Wm(),e=Hm(),i=nr();return Fe.grammar=i,Fe.write=e,Fe.parse=t.parse,Fe.parseParams=t.parseParams,Fe.parseFmtpConfig=t.parseFmtpConfig,Fe.parsePayloads=t.parsePayloads,Fe.parseRemoteCandidates=t.parseRemoteCandidates,Fe.parseImageAttributes=t.parseImageAttributes,Fe.parseSimulcastStreamList=t.parseSimulcastStreamList,Fe}var ot=Km();function sr(t,e,i){var n,s,r;e===void 0&&(e=50),i===void 0&&(i={});var o=(n=i.isImmediate)!=null&&n,a=(s=i.callback)!=null&&s,c=i.maxWait,d=Date.now(),l=[];function u(){if(c!==void 0){var p=Date.now()-d;if(p+e>=c)return c-p}return e}var h=function(){var p=[].slice.call(arguments),g=this;return new Promise(function(f,y){var S=o&&r===void 0;if(r!==void 0&&clearTimeout(r),r=setTimeout(function(){if(r=void 0,d=Date.now(),!o){var M=t.apply(g,p);a&&a(M),l.forEach(function(b){return(0,b.resolve)(M)}),l=[]}},u()),S){var x=t.apply(g,p);return a&&a(x),f(x)}l.push({resolve:f,reject:y})})};return h.cancel=function(p){r!==void 0&&clearTimeout(r),l.forEach(function(g){return(0,g.reject)(p)}),l=[]},h}const $m=.7,Jm=20,Yt={NegotiationStarted:"negotiationStarted",NegotiationComplete:"negotiationComplete",RTPVideoPayloadTypes:"rtpVideoPayloadTypes"};class Io extends Ye.EventEmitter{get pc(){return this._pc||(this._pc=this.createPC()),this._pc}constructor(e){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};var n;super(),this.log=Q,this.ddExtID=0,this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate=!1,this.trackBitrates=[],this.remoteStereoMids=[],this.remoteNackMids=[],this.negotiate=sr(s=>m(this,void 0,void 0,function*(){this.emit(Yt.NegotiationStarted);try{yield this.createAndSendOffer()}catch(r){if(s)s(r);else throw r}}),Jm),this.close=()=>{this._pc&&(this._pc.close(),this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc.onicegatheringstatechange=null,this._pc.ondatachannel=null,this._pc.onnegotiationneeded=null,this._pc.onsignalingstatechange=null,this._pc.onicecandidate=null,this._pc.ondatachannel=null,this._pc.ontrack=null,this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc=null)},this.log=nt((n=i.loggerName)!==null&&n!==void 0?n:We.PCTransport),this.loggerOptions=i,this.config=e,this._pc=this.createPC()}createPC(){const e=new RTCPeerConnection(this.config);return e.onicecandidate=i=>{var n;i.candidate&&((n=this.onIceCandidate)===null||n===void 0||n.call(this,i.candidate))},e.onicecandidateerror=i=>{var n;(n=this.onIceCandidateError)===null||n===void 0||n.call(this,i)},e.oniceconnectionstatechange=()=>{var i;(i=this.onIceConnectionStateChange)===null||i===void 0||i.call(this,e.iceConnectionState)},e.onsignalingstatechange=()=>{var i;(i=this.onSignalingStatechange)===null||i===void 0||i.call(this,e.signalingState)},e.onconnectionstatechange=()=>{var i;(i=this.onConnectionStateChange)===null||i===void 0||i.call(this,e.connectionState)},e.ondatachannel=i=>{var n;(n=this.onDataChannel)===null||n===void 0||n.call(this,i)},e.ontrack=i=>{var n;(n=this.onTrack)===null||n===void 0||n.call(this,i)},e}get logContext(){var e,i;return Object.assign({},(i=(e=this.loggerOptions).loggerContextCb)===null||i===void 0?void 0:i.call(e))}get isICEConnected(){return this._pc!==null&&(this.pc.iceConnectionState==="connected"||this.pc.iceConnectionState==="completed")}addIceCandidate(e){return m(this,void 0,void 0,function*(){if(this.pc.remoteDescription&&!this.restartingIce)return this.pc.addIceCandidate(e);this.pendingCandidates.push(e)})}setRemoteDescription(e){return m(this,void 0,void 0,function*(){var i;let n;if(e.type==="offer"){let{stereoMids:s,nackMids:r}=Qm(e);this.remoteStereoMids=s,this.remoteNackMids=r}else if(e.type==="answer"){const s=ot.parse((i=e.sdp)!==null&&i!==void 0?i:"");s.media.forEach(r=>{r.type==="audio"&&this.trackBitrates.some(o=>{if(!o.transceiver||r.mid!=o.transceiver.mid)return!1;let a=0;if(r.rtp.some(d=>d.codec.toUpperCase()===o.codec.toUpperCase()?(a=d.payload,!0):!1),a===0)return!0;let c=!1;for(const d of r.fmtp)if(d.payload===a){d.config=d.config.split(";").filter(l=>!l.includes("maxaveragebitrate")).join(";"),o.maxbr>0&&(d.config+=";maxaveragebitrate=".concat(o.maxbr*1e3)),c=!0;break}return c||o.maxbr>0&&r.fmtp.push({payload:a,config:"maxaveragebitrate=".concat(o.maxbr*1e3)}),!0})}),n=ot.write(s)}yield this.setMungedSDP(e,n,!0),this.pendingCandidates.forEach(s=>{this.pc.addIceCandidate(s)}),this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate?(this.renegotiate=!1,yield this.createAndSendOffer()):e.type==="answer"&&(this.emit(Yt.NegotiationComplete),e.sdp&&ot.parse(e.sdp).media.forEach(r=>{r.type==="video"&&this.emit(Yt.RTPVideoPayloadTypes,r.rtp)}))})}createAndSendOffer(e){return m(this,void 0,void 0,function*(){var i;if(this.onOffer===void 0)return;if(e!=null&&e.iceRestart&&(this.log.debug("restarting ICE",this.logContext),this.restartingIce=!0),this._pc&&this._pc.signalingState==="have-local-offer"){const r=this._pc.remoteDescription;if(e!=null&&e.iceRestart&&r)yield this._pc.setRemoteDescription(r);else{this.renegotiate=!0;return}}else if(!this._pc||this._pc.signalingState==="closed"){this.log.warn("could not createOffer with closed peer connection",this.logContext);return}this.log.debug("starting to negotiate",this.logContext);const n=yield this.pc.createOffer(e);this.log.debug("original offer",Object.assign({sdp:n.sdp},this.logContext));const s=ot.parse((i=n.sdp)!==null&&i!==void 0?i:"");s.media.forEach(r=>{wo(r),r.type==="audio"?Ro(r,[],[]):r.type==="video"&&this.trackBitrates.some(o=>{if(!r.msid||!o.cid||!r.msid.includes(o.cid))return!1;let a=0;if(r.rtp.some(d=>d.codec.toUpperCase()===o.codec.toUpperCase()?(a=d.payload,!0):!1),a===0||(it(o.codec)&&this.ensureVideoDDExtensionForSVC(r,s),o.codec!=="av1"))return!0;const c=Math.round(o.maxbr*$m);for(const d of r.fmtp)if(d.payload===a){d.config.includes("x-google-start-bitrate")||(d.config+=";x-google-start-bitrate=".concat(c));break}return!0})}),yield this.setMungedSDP(n,ot.write(s)),this.onOffer(n)})}createAndSetAnswer(){return m(this,void 0,void 0,function*(){var e;const i=yield this.pc.createAnswer(),n=ot.parse((e=i.sdp)!==null&&e!==void 0?e:"");return n.media.forEach(s=>{wo(s),s.type==="audio"&&Ro(s,this.remoteStereoMids,this.remoteNackMids)}),yield this.setMungedSDP(i,ot.write(n)),i})}createDataChannel(e,i){return this.pc.createDataChannel(e,i)}addTransceiver(e,i){return this.pc.addTransceiver(e,i)}addTrack(e){if(!this._pc)throw new fe("PC closed, cannot add track");return this._pc.addTrack(e)}setTrackCodecBitrate(e){this.trackBitrates.push(e)}setConfiguration(e){var i;if(!this._pc)throw new fe("PC closed, cannot configure");return(i=this._pc)===null||i===void 0?void 0:i.setConfiguration(e)}canRemoveTrack(){var e;return!!(!((e=this._pc)===null||e===void 0)&&e.removeTrack)}removeTrack(e){var i;return(i=this._pc)===null||i===void 0?void 0:i.removeTrack(e)}getConnectionState(){var e,i;return(i=(e=this._pc)===null||e===void 0?void 0:e.connectionState)!==null&&i!==void 0?i:"closed"}getICEConnectionState(){var e,i;return(i=(e=this._pc)===null||e===void 0?void 0:e.iceConnectionState)!==null&&i!==void 0?i:"closed"}getSignallingState(){var e,i;return(i=(e=this._pc)===null||e===void 0?void 0:e.signalingState)!==null&&i!==void 0?i:"closed"}getTransceivers(){var e,i;return(i=(e=this._pc)===null||e===void 0?void 0:e.getTransceivers())!==null&&i!==void 0?i:[]}getSenders(){var e,i;return(i=(e=this._pc)===null||e===void 0?void 0:e.getSenders())!==null&&i!==void 0?i:[]}getLocalDescription(){var e;return(e=this._pc)===null||e===void 0?void 0:e.localDescription}getRemoteDescription(){var e;return(e=this.pc)===null||e===void 0?void 0:e.remoteDescription}getStats(){return this.pc.getStats()}getConnectedAddress(){return m(this,void 0,void 0,function*(){var e;if(!this._pc)return;let i="";const n=new Map,s=new Map;if((yield this._pc.getStats()).forEach(a=>{switch(a.type){case"transport":i=a.selectedCandidatePairId;break;case"candidate-pair":i===""&&a.selected&&(i=a.id),n.set(a.id,a);break;case"remote-candidate":s.set(a.id,"".concat(a.address,":").concat(a.port));break}}),i==="")return;const o=(e=n.get(i))===null||e===void 0?void 0:e.remoteCandidateId;if(o!==void 0)return s.get(o)})}setMungedSDP(e,i,n){return m(this,void 0,void 0,function*(){if(i){const s=e.sdp;e.sdp=i;try{this.log.debug("setting munged ".concat(n?"remote":"local"," description"),this.logContext),n?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e);return}catch(r){this.log.warn("not able to set ".concat(e.type,", falling back to unmodified sdp"),Object.assign(Object.assign({},this.logContext),{error:r,sdp:i})),e.sdp=s}}try{n?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e)}catch(s){let r="unknown error";s instanceof Error?r=s.message:typeof s=="string"&&(r=s);const o={error:r,sdp:e.sdp};throw!n&&this.pc.remoteDescription&&(o.remoteSdp=this.pc.remoteDescription),this.log.error("unable to set ".concat(e.type),Object.assign(Object.assign({},this.logContext),{fields:o})),new ws(r)}})}ensureVideoDDExtensionForSVC(e,i){var n,s;if(!((n=e.ext)===null||n===void 0?void 0:n.some(o=>o.uri===po))){if(this.ddExtID===0){let o=0;i.media.forEach(a=>{var c;a.type==="video"&&((c=a.ext)===null||c===void 0||c.forEach(d=>{d.value>o&&(o=d.value)}))}),this.ddExtID=o+1}(s=e.ext)===null||s===void 0||s.push({value:this.ddExtID,uri:po})}}}function Ro(t,e,i){let n=0;t.rtp.some(s=>s.codec==="opus"?(n=s.payload,!0):!1),n>0&&(t.rtcpFb||(t.rtcpFb=[]),i.includes(t.mid)&&!t.rtcpFb.some(s=>s.payload===n&&s.type==="nack")&&t.rtcpFb.push({payload:n,type:"nack"}),e.includes(t.mid)&&t.fmtp.some(s=>s.payload===n?(s.config.includes("stereo=1")||(s.config+=";stereo=1"),!0):!1))}function Qm(t){var e;const i=[],n=[],s=ot.parse((e=t.sdp)!==null&&e!==void 0?e:"");let r=0;return s.media.forEach(o=>{var a;o.type==="audio"&&(o.rtp.some(c=>c.codec==="opus"?(r=c.payload,!0):!1),!((a=o.rtcpFb)===null||a===void 0)&&a.some(c=>c.payload===r&&c.type==="nack")&&n.push(o.mid),o.fmtp.some(c=>c.payload===r?(c.config.includes("sprop-stereo=1")&&i.push(o.mid),!0):!1))}),{stereoMids:i,nackMids:n}}function wo(t){if(t.connection){const e=t.connection.ip.indexOf(":")>=0;(t.connection.version===4&&e||t.connection.version===6&&!e)&&(t.connection.ip="0.0.0.0",t.connection.version=4)}}const Vs="vp8",Ym={audioPreset:_s.music,dtx:!0,red:!0,forceStereo:!1,simulcast:!0,screenShareEncoding:yn.h1080fps15.encoding,stopMicTrackOnMute:!1,videoCodec:Vs,backupCodec:!0,preConnectBuffer:!1},Ac={deviceId:{ideal:"default"},autoGainControl:!0,echoCancellation:!0,noiseSuppression:!0,voiceIsolation:!0},xc={deviceId:{ideal:"default"},resolution:Dt.h720.resolution},Xm={adaptiveStream:!1,dynacast:!1,stopLocalTrackOnUnpublish:!0,reconnectPolicy:new zh,disconnectOnPageLeave:!0,webAudioMix:!1},rr={autoSubscribe:!0,maxRetries:1,peerConnectionTimeout:15e3,websocketTimeout:15e3};var le;(function(t){t[t.NEW=0]="NEW",t[t.CONNECTING=1]="CONNECTING",t[t.CONNECTED=2]="CONNECTED",t[t.FAILED=3]="FAILED",t[t.CLOSING=4]="CLOSING",t[t.CLOSED=5]="CLOSED"})(le||(le={}));class Zm{get needsPublisher(){return this.isPublisherConnectionRequired}get needsSubscriber(){return this.isSubscriberConnectionRequired}get currentState(){return this.state}constructor(e,i,n){var s;this.peerConnectionTimeout=rr.peerConnectionTimeout,this.log=Q,this.updateState=()=>{var r;const o=this.state,a=this.requiredTransports.map(c=>c.getConnectionState());a.every(c=>c==="connected")?this.state=le.CONNECTED:a.some(c=>c==="failed")?this.state=le.FAILED:a.some(c=>c==="connecting")?this.state=le.CONNECTING:a.every(c=>c==="closed")?this.state=le.CLOSED:a.some(c=>c==="closed")?this.state=le.CLOSING:a.every(c=>c==="new")&&(this.state=le.NEW),o!==this.state&&(this.log.debug("pc state change: from ".concat(le[o]," to ").concat(le[this.state]),this.logContext),(r=this.onStateChange)===null||r===void 0||r.call(this,this.state,this.publisher.getConnectionState(),this.subscriber.getConnectionState()))},this.log=nt((s=n.loggerName)!==null&&s!==void 0?s:We.PCManager),this.loggerOptions=n,this.isPublisherConnectionRequired=!i,this.isSubscriberConnectionRequired=i,this.publisher=new Io(e,n),this.subscriber=new Io(e,n),this.publisher.onConnectionStateChange=this.updateState,this.subscriber.onConnectionStateChange=this.updateState,this.publisher.onIceConnectionStateChange=this.updateState,this.subscriber.onIceConnectionStateChange=this.updateState,this.publisher.onSignalingStatechange=this.updateState,this.subscriber.onSignalingStatechange=this.updateState,this.publisher.onIceCandidate=r=>{var o;(o=this.onIceCandidate)===null||o===void 0||o.call(this,r,qe.PUBLISHER)},this.subscriber.onIceCandidate=r=>{var o;(o=this.onIceCandidate)===null||o===void 0||o.call(this,r,qe.SUBSCRIBER)},this.subscriber.onDataChannel=r=>{var o;(o=this.onDataChannel)===null||o===void 0||o.call(this,r)},this.subscriber.onTrack=r=>{var o;(o=this.onTrack)===null||o===void 0||o.call(this,r)},this.publisher.onOffer=r=>{var o;(o=this.onPublisherOffer)===null||o===void 0||o.call(this,r)},this.state=le.NEW,this.connectionLock=new Me,this.remoteOfferLock=new Me}get logContext(){var e,i;return Object.assign({},(i=(e=this.loggerOptions).loggerContextCb)===null||i===void 0?void 0:i.call(e))}requirePublisher(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.isPublisherConnectionRequired=e,this.updateState()}requireSubscriber(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.isSubscriberConnectionRequired=e,this.updateState()}createAndSendPublisherOffer(e){return this.publisher.createAndSendOffer(e)}setPublisherAnswer(e){return this.publisher.setRemoteDescription(e)}removeTrack(e){return this.publisher.removeTrack(e)}close(){return m(this,void 0,void 0,function*(){if(this.publisher&&this.publisher.getSignallingState()!=="closed"){const e=this.publisher;for(const i of e.getSenders())try{e.canRemoveTrack()&&e.removeTrack(i)}catch(n){this.log.warn("could not removeTrack",Object.assign(Object.assign({},this.logContext),{error:n}))}}yield Promise.all([this.publisher.close(),this.subscriber.close()]),this.updateState()})}triggerIceRestart(){return m(this,void 0,void 0,function*(){this.subscriber.restartingIce=!0,this.needsPublisher&&(yield this.createAndSendPublisherOffer({iceRestart:!0}))})}addIceCandidate(e,i){return m(this,void 0,void 0,function*(){i===qe.PUBLISHER?yield this.publisher.addIceCandidate(e):yield this.subscriber.addIceCandidate(e)})}createSubscriberAnswerFromOffer(e){return m(this,void 0,void 0,function*(){this.log.debug("received server offer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type,sdp:e.sdp,signalingState:this.subscriber.getSignallingState().toString()}));const i=yield this.remoteOfferLock.lock();try{return yield this.subscriber.setRemoteDescription(e),yield this.subscriber.createAndSetAnswer()}finally{i()}})}updateConfiguration(e,i){this.publisher.setConfiguration(e),this.subscriber.setConfiguration(e),i&&this.triggerIceRestart()}ensurePCTransportConnection(e,i){return m(this,void 0,void 0,function*(){var n;const s=yield this.connectionLock.lock();try{this.isPublisherConnectionRequired&&this.publisher.getConnectionState()!=="connected"&&this.publisher.getConnectionState()!=="connecting"&&(this.log.debug("negotiation required, start negotiating",this.logContext),this.publisher.negotiate()),yield Promise.all((n=this.requiredTransports)===null||n===void 0?void 0:n.map(r=>this.ensureTransportConnected(r,e,i)))}finally{s()}})}negotiate(e){return m(this,void 0,void 0,function*(){return new Promise((i,n)=>m(this,void 0,void 0,function*(){const s=setTimeout(()=>{n("negotiation timed out")},this.peerConnectionTimeout),r=()=>{clearTimeout(s),n("negotiation aborted")};e.signal.addEventListener("abort",r),this.publisher.once(Yt.NegotiationStarted,()=>{e.signal.aborted||this.publisher.once(Yt.NegotiationComplete,()=>{clearTimeout(s),i()})}),yield this.publisher.negotiate(o=>{clearTimeout(s),n(o)})}))})}addPublisherTransceiver(e,i){return this.publisher.addTransceiver(e,i)}addPublisherTrack(e){return this.publisher.addTrack(e)}createPublisherDataChannel(e,i){return this.publisher.createDataChannel(e,i)}getConnectedAddress(e){return e===qe.PUBLISHER?this.publisher.getConnectedAddress():e===qe.SUBSCRIBER?this.publisher.getConnectedAddress():this.requiredTransports[0].getConnectedAddress()}get requiredTransports(){const e=[];return this.isPublisherConnectionRequired&&e.push(this.publisher),this.isSubscriberConnectionRequired&&e.push(this.subscriber),e}ensureTransportConnected(e,i){return m(this,arguments,void 0,function(n,s){var r=this;let o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.peerConnectionTimeout;return function*(){if(n.getConnectionState()!=="connected")return new Promise((c,d)=>m(r,void 0,void 0,function*(){const l=()=>{this.log.warn("abort transport connection",this.logContext),Pe.clearTimeout(u),d(new re("room connection has been cancelled",Y.Cancelled))};s!=null&&s.signal.aborted&&l(),s==null||s.signal.addEventListener("abort",l);const u=Pe.setTimeout(()=>{s==null||s.signal.removeEventListener("abort",l),d(new re("could not establish pc connection",Y.InternalError))},o);for(;this.state!==le.CONNECTED;)if(yield Ae(50),s!=null&&s.signal.aborted){d(new re("room connection has been cancelled",Y.Cancelled));return}Pe.clearTimeout(u),s==null||s.signal.removeEventListener("abort",l),c()}))}()})}}class he extends Error{constructor(e,i,n){super(i),this.code=e,this.message=_o(i,he.MAX_MESSAGE_BYTES),this.data=n?_o(n,he.MAX_DATA_BYTES):void 0}static fromProto(e){return new he(e.code,e.message,e.data)}toProto(){return new Ra({code:this.code,message:this.message,data:this.data})}static builtIn(e,i){return new he(he.ErrorCode[e],he.ErrorMessage[e],i)}}he.MAX_MESSAGE_BYTES=256;he.MAX_DATA_BYTES=15360;he.ErrorCode={APPLICATION_ERROR:1500,CONNECTION_TIMEOUT:1501,RESPONSE_TIMEOUT:1502,RECIPIENT_DISCONNECTED:1503,RESPONSE_PAYLOAD_TOO_LARGE:1504,SEND_FAILED:1505,UNSUPPORTED_METHOD:1400,RECIPIENT_NOT_FOUND:1401,REQUEST_PAYLOAD_TOO_LARGE:1402,UNSUPPORTED_SERVER:1403,UNSUPPORTED_VERSION:1404};he.ErrorMessage={APPLICATION_ERROR:"Application error in method handler",CONNECTION_TIMEOUT:"Connection timeout",RESPONSE_TIMEOUT:"Response timeout",RECIPIENT_DISCONNECTED:"Recipient disconnected",RESPONSE_PAYLOAD_TOO_LARGE:"Response payload too large",SEND_FAILED:"Failed to send",UNSUPPORTED_METHOD:"Method not supported at destination",RECIPIENT_NOT_FOUND:"Recipient not found",REQUEST_PAYLOAD_TOO_LARGE:"Request payload too large",UNSUPPORTED_SERVER:"RPC not supported by server",UNSUPPORTED_VERSION:"Unsupported RPC version"};const Lc=15360;function or(t){return new TextEncoder().encode(t).length}function _o(t,e){if(or(t)<=e)return t;let i=0,n=t.length;const s=new TextEncoder;for(;i<n;){const r=Math.floor((i+n+1)/2);s.encode(t.slice(0,r)).length<=e?i=r:n=r-1}return t.slice(0,i)}const ar=2e3;function Sn(t,e){if(!e)return 0;let i,n;return"bytesReceived"in t?(i=t.bytesReceived,n=e.bytesReceived):"bytesSent"in t&&(i=t.bytesSent,n=e.bytesSent),i===void 0||n===void 0||t.timestamp===void 0||e.timestamp===void 0?0:(i-n)*8*1e3/(t.timestamp-e.timestamp)}const cr=typeof MediaRecorder<"u";class ep{constructor(){throw new Error("MediaRecorder is not available in this environment")}}const tp=cr?MediaRecorder:ep;class ip extends tp{constructor(e,i){if(!cr)throw new Error("MediaRecorder is not available in this environment");super(new MediaStream([e.mediaStreamTrack]),i);let n,s;const r=()=>s===void 0,o=()=>{this.removeEventListener("dataavailable",n),this.removeEventListener("stop",o),this.removeEventListener("error",a),s==null||s.close(),s=void 0},a=c=>{s==null||s.error(c),this.removeEventListener("dataavailable",n),this.removeEventListener("stop",o),this.removeEventListener("error",a),s=void 0};this.byteStream=new ReadableStream({start:c=>{s=c,n=d=>m(this,void 0,void 0,function*(){const l=yield d.data.arrayBuffer();r()||c.enqueue(new Uint8Array(l))}),this.addEventListener("dataavailable",n)},cancel:()=>{o()}}),this.addEventListener("stop",o),this.addEventListener("error",a)}}function np(){return cr}const sp=1e3,rp=1e4;class Nc extends P{get sender(){return this._sender}set sender(e){this._sender=e}get constraints(){return this._constraints}get hasPreConnectBuffer(){return!!this.localTrackRecorder}constructor(e,i,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,r=arguments.length>4?arguments[4]:void 0;super(e,i,r),this.manuallyStopped=!1,this._isUpstreamPaused=!1,this.handleTrackMuteEvent=()=>this.debouncedTrackMuteHandler().catch(()=>this.log.debug("track mute bounce got cancelled by an unmute event",this.logContext)),this.debouncedTrackMuteHandler=sr(()=>m(this,void 0,void 0,function*(){yield this.pauseUpstream()}),5e3),this.handleTrackUnmuteEvent=()=>m(this,void 0,void 0,function*(){this.debouncedTrackMuteHandler.cancel("unmute"),yield this.resumeUpstream()}),this.handleEnded=()=>{this.isInBackground&&(this.reacquireTrack=!0),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),this.emit(D.Ended,this)},this.reacquireTrack=!1,this.providedByUser=s,this.muteLock=new Me,this.pauseUpstreamLock=new Me,this.processorLock=new Me,this.restartLock=new Me,this.setMediaStreamTrack(e,!0),this._constraints=e.getConstraints(),n&&(this._constraints=n)}get id(){return this._mediaStreamTrack.id}get dimensions(){if(this.kind!==P.Kind.Video)return;const{width:e,height:i}=this._mediaStreamTrack.getSettings();if(e&&i)return{width:e,height:i}}get isUpstreamPaused(){return this._isUpstreamPaused}get isUserProvided(){return this.providedByUser}get mediaStreamTrack(){var e,i;return(i=(e=this.processor)===null||e===void 0?void 0:e.processedTrack)!==null&&i!==void 0?i:this._mediaStreamTrack}get isLocal(){return!0}getSourceTrackSettings(){return this._mediaStreamTrack.getSettings()}setMediaStreamTrack(e,i){return m(this,void 0,void 0,function*(){var n;if(e===this._mediaStreamTrack&&!i)return;this._mediaStreamTrack&&(this.attachedElements.forEach(r=>{Jt(this._mediaStreamTrack,r)}),this.debouncedTrackMuteHandler.cancel("new-track"),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent)),this.mediaStream=new MediaStream([e]),e&&(e.addEventListener("ended",this.handleEnded),e.addEventListener("mute",this.handleTrackMuteEvent),e.addEventListener("unmute",this.handleTrackUnmuteEvent),this._constraints=e.getConstraints());let s;if(this.processor&&e){const r=yield this.processorLock.lock();try{if(this.log.debug("restarting processor",this.logContext),this.kind==="unknown")throw TypeError("cannot set processor on track of unknown kind");this.processorElement&&(zt(e,this.processorElement),this.processorElement.muted=!0),yield this.processor.restart({track:e,kind:this.kind,element:this.processorElement}),s=this.processor.processedTrack}finally{r()}}this.sender&&((n=this.sender.transport)===null||n===void 0?void 0:n.state)!=="closed"&&(yield this.sender.replaceTrack(s??e)),!this.providedByUser&&this._mediaStreamTrack!==e&&this._mediaStreamTrack.stop(),this._mediaStreamTrack=e,e&&(this._mediaStreamTrack.enabled=!this.isMuted,yield this.resumeUpstream(),this.attachedElements.forEach(r=>{zt(s??e,r)}))})}waitForDimensions(){return m(this,arguments,void 0,function(){var e=this;let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:sp;return function*(){var n;if(e.kind===P.Kind.Audio)throw new Error("cannot get dimensions for audio tracks");((n=Oe())===null||n===void 0?void 0:n.os)==="iOS"&&(yield Ae(10));const s=Date.now();for(;Date.now()-s<i;){const r=e.dimensions;if(r)return r;yield Ae(50)}throw new $e("unable to get track dimensions after timeout")}()})}setDeviceId(e){return m(this,void 0,void 0,function*(){return this._constraints.deviceId===e&&this._mediaStreamTrack.getSettings().deviceId===lt(e)||(this._constraints.deviceId=e,this.isMuted)?!0:(yield this.restartTrack(),lt(e)===this._mediaStreamTrack.getSettings().deviceId)})}getDeviceId(){return m(this,arguments,void 0,function(){var e=this;let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return function*(){if(e.source===P.Source.ScreenShare)return;const{deviceId:n,groupId:s}=e._mediaStreamTrack.getSettings(),r=e.kind===P.Kind.Audio?"audioinput":"videoinput";return i?ke.getInstance().normalizeDeviceId(r,n,s):n}()})}mute(){return m(this,void 0,void 0,function*(){return this.setTrackMuted(!0),this})}unmute(){return m(this,void 0,void 0,function*(){return this.setTrackMuted(!1),this})}replaceTrack(e,i){return m(this,void 0,void 0,function*(){if(!this.sender)throw new $e("unable to replace an unpublished track");let n,s;return typeof i=="boolean"?n=i:i!==void 0&&(n=i.userProvidedTrack,s=i.stopProcessor),this.providedByUser=n??!0,this.log.debug("replace MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(e),s&&this.processor&&(yield this.stopProcessor()),this})}restart(e){return m(this,void 0,void 0,function*(){this.manuallyStopped=!1;const i=yield this.restartLock.lock();try{e||(e=this._constraints);const{deviceId:n,facingMode:s}=e,r=qh(e,["deviceId","facingMode"]);this.log.debug("restarting track with constraints",Object.assign(Object.assign({},this.logContext),{constraints:e}));const o={audio:!1,video:!1};this.kind===P.Kind.Video?o.video=n||s?{deviceId:n,facingMode:s}:!0:o.audio=n?{deviceId:n}:!0,this.attachedElements.forEach(d=>{Jt(this.mediaStreamTrack,d)}),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.stop();const c=(yield navigator.mediaDevices.getUserMedia(o)).getTracks()[0];return yield c.applyConstraints(r),c.addEventListener("ended",this.handleEnded),this.log.debug("re-acquired MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(c),this._constraints=e,this.emit(D.Restarted,this),this.manuallyStopped&&(this.log.warn("track was stopped during a restart, stopping restarted track",this.logContext),this.stop()),this}finally{i()}})}setTrackMuted(e){this.log.debug("setting ".concat(this.kind," track ").concat(e?"muted":"unmuted"),this.logContext),!(this.isMuted===e&&this._mediaStreamTrack.enabled!==e)&&(this.isMuted=e,this._mediaStreamTrack.enabled=!e,this.emit(e?D.Muted:D.Unmuted,this))}get needsReAcquisition(){return this._mediaStreamTrack.readyState!=="live"||this._mediaStreamTrack.muted||!this._mediaStreamTrack.enabled||this.reacquireTrack}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return m(this,void 0,void 0,function*(){yield e.handleAppVisibilityChanged.call(this),Pc()&&(this.log.debug("visibility changed, is in Background: ".concat(this.isInBackground),this.logContext),!this.isInBackground&&this.needsReAcquisition&&!this.isUserProvided&&!this.isMuted&&(this.log.debug("track needs to be reacquired, restarting ".concat(this.source),this.logContext),yield this.restart(),this.reacquireTrack=!1))})}stop(){var e;this.manuallyStopped=!0,super.stop(),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),(e=this.processor)===null||e===void 0||e.destroy(),this.processor=void 0}pauseUpstream(){return m(this,void 0,void 0,function*(){var e;const i=yield this.pauseUpstreamLock.lock();try{if(this._isUpstreamPaused===!0)return;if(!this.sender){this.log.warn("unable to pause upstream for an unpublished track",this.logContext);return}this._isUpstreamPaused=!0,this.emit(D.UpstreamPaused,this);const n=Oe();if((n==null?void 0:n.name)==="Safari"&&ht(n.version,"12.0")<0)throw new bn("pauseUpstream is not supported on Safari < 12.");((e=this.sender.transport)===null||e===void 0?void 0:e.state)!=="closed"&&(yield this.sender.replaceTrack(null))}finally{i()}})}resumeUpstream(){return m(this,void 0,void 0,function*(){var e;const i=yield this.pauseUpstreamLock.lock();try{if(this._isUpstreamPaused===!1)return;if(!this.sender){this.log.warn("unable to resume upstream for an unpublished track",this.logContext);return}this._isUpstreamPaused=!1,this.emit(D.UpstreamResumed,this),((e=this.sender.transport)===null||e===void 0?void 0:e.state)!=="closed"&&(yield this.sender.replaceTrack(this.mediaStreamTrack))}finally{i()}})}getRTCStatsReport(){return m(this,void 0,void 0,function*(){var e;return!((e=this.sender)===null||e===void 0)&&e.getStats?yield this.sender.getStats():void 0})}setProcessor(e){return m(this,arguments,void 0,function(i){var n=this;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return function*(){var r;const o=yield n.processorLock.lock();try{n.log.debug("setting up processor",n.logContext);const a=document.createElement(n.kind),c={kind:n.kind,track:n._mediaStreamTrack,element:a,audioContext:n.audioContext};if(yield i.init(c),n.log.debug("processor initialized",n.logContext),n.processor&&(yield n.stopProcessor()),n.kind==="unknown")throw TypeError("cannot set processor on track of unknown kind");if(zt(n._mediaStreamTrack,a),a.muted=!0,a.play().catch(d=>n.log.error("failed to play processor element",Object.assign(Object.assign({},n.logContext),{error:d}))),n.processor=i,n.processorElement=a,n.processor.processedTrack){for(const d of n.attachedElements)d!==n.processorElement&&s&&(Jt(n._mediaStreamTrack,d),zt(n.processor.processedTrack,d));yield(r=n.sender)===null||r===void 0?void 0:r.replaceTrack(n.processor.processedTrack)}n.emit(D.TrackProcessorUpdate,n.processor)}finally{o()}}()})}getProcessor(){return this.processor}stopProcessor(){return m(this,arguments,void 0,function(){var e=this;let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return function*(){var n,s;e.processor&&(e.log.debug("stopping processor",e.logContext),(n=e.processor.processedTrack)===null||n===void 0||n.stop(),yield e.processor.destroy(),e.processor=void 0,i||((s=e.processorElement)===null||s===void 0||s.remove(),e.processorElement=void 0),yield e._mediaStreamTrack.applyConstraints(e._constraints),yield e.setMediaStreamTrack(e._mediaStreamTrack,!0),e.emit(D.TrackProcessorUpdate))}()})}startPreConnectBuffer(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:100;if(!np()){this.log.warn("MediaRecorder is not available, cannot start preconnect buffer",this.logContext);return}if(!this.localTrackRecorder)this.localTrackRecorder=new ip(this,{mimeType:"audio/webm;codecs=opus"});else{this.log.warn("preconnect buffer already started");return}this.localTrackRecorder.start(e),this.autoStopPreConnectBuffer=setTimeout(()=>{this.log.warn("preconnect buffer timed out, stopping recording automatically",this.logContext),this.stopPreConnectBuffer()},rp)}stopPreConnectBuffer(){clearTimeout(this.autoStopPreConnectBuffer),this.localTrackRecorder&&(this.localTrackRecorder.stop(),this.localTrackRecorder=void 0)}getPreConnectBuffer(){var e;return(e=this.localTrackRecorder)===null||e===void 0?void 0:e.byteStream}}class _i extends Nc{get enhancedNoiseCancellation(){return this.isKrispNoiseFilterEnabled}constructor(e,i){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,s=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0;super(e,P.Kind.Audio,i,n,r),this.stopOnMute=!1,this.isKrispNoiseFilterEnabled=!1,this.monitorSender=()=>m(this,void 0,void 0,function*(){if(!this.sender){this._currentBitrate=0;return}let o;try{o=yield this.getSenderStats()}catch(a){this.log.error("could not get audio sender stats",Object.assign(Object.assign({},this.logContext),{error:a}));return}o&&this.prevStats&&(this._currentBitrate=Sn(o,this.prevStats)),this.prevStats=o}),this.handleKrispNoiseFilterEnable=()=>{this.isKrispNoiseFilterEnabled=!0,this.log.debug("Krisp noise filter enabled",this.logContext),this.emit(D.AudioTrackFeatureUpdate,this,Se.TF_ENHANCED_NOISE_CANCELLATION,!0)},this.handleKrispNoiseFilterDisable=()=>{this.isKrispNoiseFilterEnabled=!1,this.log.debug("Krisp noise filter disabled",this.logContext),this.emit(D.AudioTrackFeatureUpdate,this,Se.TF_ENHANCED_NOISE_CANCELLATION,!1)},this.audioContext=s,this.checkForSilence()}mute(){const e=Object.create(null,{mute:{get:()=>super.mute}});return m(this,void 0,void 0,function*(){const i=yield this.muteLock.lock();try{return this.isMuted?(this.log.debug("Track already muted",this.logContext),this):(this.source===P.Source.Microphone&&this.stopOnMute&&!this.isUserProvided&&(this.log.debug("stopping mic track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this)}finally{i()}})}unmute(){const e=Object.create(null,{unmute:{get:()=>super.unmute}});return m(this,void 0,void 0,function*(){const i=yield this.muteLock.lock();try{if(!this.isMuted)return this.log.debug("Track already unmuted",this.logContext),this;const n=this._constraints.deviceId&&this._mediaStreamTrack.getSettings().deviceId!==lt(this._constraints.deviceId);return this.source===P.Source.Microphone&&(this.stopOnMute||this._mediaStreamTrack.readyState==="ended"||n)&&!this.isUserProvided&&(this.log.debug("reacquiring mic track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this}finally{i()}})}restartTrack(e){return m(this,void 0,void 0,function*(){let i;if(e){const n=tr({audio:e});typeof n.audio!="boolean"&&(i=n.audio)}yield this.restart(i)})}restart(e){const i=Object.create(null,{restart:{get:()=>super.restart}});return m(this,void 0,void 0,function*(){const n=yield i.restart.call(this,e);return this.checkForSilence(),n})}startMonitor(){De()&&(this.monitorInterval||(this.monitorInterval=setInterval(()=>{this.monitorSender()},ar)))}setProcessor(e){return m(this,void 0,void 0,function*(){var i;const n=yield this.processorLock.lock();try{if(!Qe()&&!this.audioContext)throw Error("Audio context needs to be set on LocalAudioTrack in order to enable processors");this.processor&&(yield this.stopProcessor());const s={kind:this.kind,track:this._mediaStreamTrack,audioContext:this.audioContext};this.log.debug("setting up audio processor ".concat(e.name),this.logContext),yield e.init(s),this.processor=e,this.processor.processedTrack&&(yield(i=this.sender)===null||i===void 0?void 0:i.replaceTrack(this.processor.processedTrack),this.processor.processedTrack.addEventListener("enable-lk-krisp-noise-filter",this.handleKrispNoiseFilterEnable),this.processor.processedTrack.addEventListener("disable-lk-krisp-noise-filter",this.handleKrispNoiseFilterDisable)),this.emit(D.TrackProcessorUpdate,this.processor)}finally{n()}})}setAudioContext(e){this.audioContext=e}getSenderStats(){return m(this,void 0,void 0,function*(){var e;if(!(!((e=this.sender)===null||e===void 0)&&e.getStats))return;const i=yield this.sender.getStats();let n;return i.forEach(s=>{s.type==="outbound-rtp"&&(n={type:"audio",streamId:s.id,packetsSent:s.packetsSent,packetsLost:s.packetsLost,bytesSent:s.bytesSent,timestamp:s.timestamp,roundTripTime:s.roundTripTime,jitter:s.jitter})}),n})}checkForSilence(){return m(this,void 0,void 0,function*(){const e=yield wc(this);return e&&(this.isMuted||this.log.warn("silence detected on local audio track",this.logContext),this.emit(D.AudioSilenceDetected)),e})}}function op(t,e,i){switch(t.kind){case"audio":return new _i(t,e,!1,void 0,i);case"video":return new Mi(t,e,!1,i);default:throw new $e("unsupported track type: ".concat(t.kind))}}const ap=Object.values(Dt),cp=Object.values(Ms),dp=Object.values(yn),lp=[Dt.h180,Dt.h360],up=[Ms.h180,Ms.h360],hp=t=>[{scaleResolutionDownBy:2,fps:t.encoding.maxFramerate}].map(i=>{var n,s;return new ne(Math.floor(t.width/i.scaleResolutionDownBy),Math.floor(t.height/i.scaleResolutionDownBy),Math.max(15e4,Math.floor(t.encoding.maxBitrate/(Math.pow(i.scaleResolutionDownBy,2)*(((n=t.encoding.maxFramerate)!==null&&n!==void 0?n:30)/((s=i.fps)!==null&&s!==void 0?s:30))))),i.fps,t.encoding.priority)}),Bs=["q","h","f"];function js(t,e,i,n){var s,r;let o=n==null?void 0:n.videoEncoding;t&&(o=n==null?void 0:n.screenShareEncoding);const a=n==null?void 0:n.simulcast,c=n==null?void 0:n.scalabilityMode,d=n==null?void 0:n.videoCodec;if(!o&&!a&&!c||!e||!i)return[{}];o||(o=pp(t,e,i,d),Q.debug("using video encoding",o));const l=o.maxFramerate,u=new ne(e,i,o.maxBitrate,o.maxFramerate,o.priority);if(c&&it(d)){const g=new ln(c),f=[];if(g.spatial>3)throw new Error("unsupported scalabilityMode: ".concat(c));const y=Oe();if(Ot()||Qe()||(y==null?void 0:y.name)==="Chrome"&&ht(y==null?void 0:y.version,"113")<0){const S=g.suffix=="h"?2:3,x=Sm(y);for(let M=0;M<g.spatial;M+=1)f.push({rid:Bs[2-M],maxBitrate:o.maxBitrate/Math.pow(S,M),maxFramerate:u.encoding.maxFramerate,scaleResolutionDownBy:x?Math.pow(2,M):void 0});f[0].scalabilityMode=c}else f.push({maxBitrate:o.maxBitrate,maxFramerate:u.encoding.maxFramerate,scalabilityMode:c});return u.encoding.priority&&(f[0].priority=u.encoding.priority,f[0].networkPriority=u.encoding.priority),Q.debug("using svc encoding",{encodings:f}),f}if(!a)return[o];let h=[];t?h=(s=Do(n==null?void 0:n.screenShareSimulcastLayers))!==null&&s!==void 0?s:Mo(t,u):h=(r=Do(n==null?void 0:n.videoSimulcastLayers))!==null&&r!==void 0?r:Mo(t,u);let p;if(h.length>0){const g=h[0];h.length>1&&([,p]=h);const f=Math.max(e,i);if(f>=960&&p)return Hn(e,i,[g,p,u],l);if(f>=480)return Hn(e,i,[g,u],l)}return Hn(e,i,[u])}function mp(t,e,i){var n,s,r,o;if(!i.backupCodec||i.backupCodec===!0||i.backupCodec.codec===i.videoCodec)return;e!==i.backupCodec.codec&&Q.warn("requested a different codec than specified as backup",{serverRequested:e,backup:i.backupCodec.codec}),i.videoCodec=e,i.videoEncoding=i.backupCodec.encoding;const a=t.mediaStreamTrack.getSettings(),c=(n=a.width)!==null&&n!==void 0?n:(s=t.dimensions)===null||s===void 0?void 0:s.width,d=(r=a.height)!==null&&r!==void 0?r:(o=t.dimensions)===null||o===void 0?void 0:o.height;return t.source===P.Source.ScreenShare&&i.simulcast&&(i.simulcast=!1),js(t.source===P.Source.ScreenShare,c,d,i)}function pp(t,e,i,n){const s=fp(t,e,i);let{encoding:r}=s[0];const o=Math.max(e,i);for(let a=0;a<s.length;a+=1){const c=s[a];if(r=c.encoding,c.width>=o)break}if(n)switch(n){case"av1":r=Object.assign({},r),r.maxBitrate=r.maxBitrate*.7;break;case"vp9":r=Object.assign({},r),r.maxBitrate=r.maxBitrate*.85;break}return r}function fp(t,e,i){if(t)return dp;const n=e>i?e/i:i/e;return Math.abs(n-16/9)<Math.abs(n-4/3)?ap:cp}function Mo(t,e){if(t)return hp(e);const{width:i,height:n}=e,s=i>n?i/n:n/i;return Math.abs(s-16/9)<Math.abs(s-4/3)?lp:up}function Hn(t,e,i,n){const s=[];if(i.forEach((r,o)=>{if(o>=Bs.length)return;const a=Math.min(t,e),d={rid:Bs[o],scaleResolutionDownBy:Math.max(1,a/Math.min(r.width,r.height)),maxBitrate:r.encoding.maxBitrate},l=n&&r.encoding.maxFramerate?Math.min(n,r.encoding.maxFramerate):r.encoding.maxFramerate;l&&(d.maxFramerate=l);const u=ei()||o===0;r.encoding.priority&&u&&(d.priority=r.encoding.priority,d.networkPriority=r.encoding.priority),s.push(d)}),Qe()&&Ic()==="ios"){let r;s.forEach(a=>{r?a.maxFramerate&&a.maxFramerate>r&&(r=a.maxFramerate):r=a.maxFramerate});let o=!0;s.forEach(a=>{var c;a.maxFramerate!=r&&(o&&(o=!1,Q.info("Simulcast on iOS React-Native requires all encodings to share the same framerate.")),Q.info('Setting framerate of encoding "'.concat((c=a.rid)!==null&&c!==void 0?c:"",'" to ').concat(r)),a.maxFramerate=r)})}return s}function Do(t){if(t)return t.sort((e,i)=>{const{encoding:n}=e,{encoding:s}=i;return n.maxBitrate>s.maxBitrate?1:n.maxBitrate<s.maxBitrate?-1:n.maxBitrate===s.maxBitrate&&n.maxFramerate&&s.maxFramerate?n.maxFramerate>s.maxFramerate?1:-1:0})}class ln{constructor(e){const i=e.match(/^L(\d)T(\d)(h|_KEY|_KEY_SHIFT){0,1}$/);if(!i)throw new Error("invalid scalability mode");if(this.spatial=parseInt(i[1]),this.temporal=parseInt(i[2]),i.length>3)switch(i[3]){case"h":case"_KEY":case"_KEY_SHIFT":this.suffix=i[3]}}toString(){var e;return"L".concat(this.spatial,"T").concat(this.temporal).concat((e=this.suffix)!==null&&e!==void 0?e:"")}}function gp(t){return t.source===P.Source.ScreenShare||t.constraints.height&&lt(t.constraints.height)>=1080?"maintain-resolution":"balanced"}const vp=5e3;class Mi extends Nc{get sender(){return this._sender}set sender(e){this._sender=e,this.degradationPreference&&this.setDegradationPreference(this.degradationPreference)}constructor(e,i){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,s=arguments.length>3?arguments[3]:void 0;super(e,P.Kind.Video,i,n,s),this.simulcastCodecs=new Map,this.degradationPreference="balanced",this.monitorSender=()=>m(this,void 0,void 0,function*(){if(!this.sender){this._currentBitrate=0;return}let r;try{r=yield this.getSenderStats()}catch(a){this.log.error("could not get audio sender stats",Object.assign(Object.assign({},this.logContext),{error:a}));return}const o=new Map(r.map(a=>[a.rid,a]));if(this.prevStats){let a=0;o.forEach((c,d)=>{var l;const u=(l=this.prevStats)===null||l===void 0?void 0:l.get(d);a+=Sn(c,u)}),this._currentBitrate=a}this.prevStats=o}),this.senderLock=new Me}get isSimulcast(){return!!(this.sender&&this.sender.getParameters().encodings.length>1)}startMonitor(e){var i;if(this.signalClient=e,!De())return;const n=(i=this.sender)===null||i===void 0?void 0:i.getParameters();n&&(this.encodings=n.encodings),!this.monitorInterval&&(this.monitorInterval=setInterval(()=>{this.monitorSender()},ar))}stop(){this._mediaStreamTrack.getConstraints(),this.simulcastCodecs.forEach(e=>{e.mediaStreamTrack.stop()}),super.stop()}pauseUpstream(){const e=Object.create(null,{pauseUpstream:{get:()=>super.pauseUpstream}});return m(this,void 0,void 0,function*(){var i,n,s,r,o;yield e.pauseUpstream.call(this);try{for(var a=!0,c=tt(this.simulcastCodecs.values()),d;d=yield c.next(),i=d.done,!i;a=!0)r=d.value,a=!1,yield(o=r.sender)===null||o===void 0?void 0:o.replaceTrack(null)}catch(l){n={error:l}}finally{try{!a&&!i&&(s=c.return)&&(yield s.call(c))}finally{if(n)throw n.error}}})}resumeUpstream(){const e=Object.create(null,{resumeUpstream:{get:()=>super.resumeUpstream}});return m(this,void 0,void 0,function*(){var i,n,s,r,o;yield e.resumeUpstream.call(this);try{for(var a=!0,c=tt(this.simulcastCodecs.values()),d;d=yield c.next(),i=d.done,!i;a=!0){r=d.value,a=!1;const l=r;yield(o=l.sender)===null||o===void 0?void 0:o.replaceTrack(l.mediaStreamTrack)}}catch(l){n={error:l}}finally{try{!a&&!i&&(s=c.return)&&(yield s.call(c))}finally{if(n)throw n.error}}})}mute(){const e=Object.create(null,{mute:{get:()=>super.mute}});return m(this,void 0,void 0,function*(){const i=yield this.muteLock.lock();try{return this.isMuted?(this.log.debug("Track already muted",this.logContext),this):(this.source===P.Source.Camera&&!this.isUserProvided&&(this.log.debug("stopping camera track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this)}finally{i()}})}unmute(){const e=Object.create(null,{unmute:{get:()=>super.unmute}});return m(this,void 0,void 0,function*(){const i=yield this.muteLock.lock();try{return this.isMuted?(this.source===P.Source.Camera&&!this.isUserProvided&&(this.log.debug("reacquiring camera track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this):(this.log.debug("Track already unmuted",this.logContext),this)}finally{i()}})}setTrackMuted(e){super.setTrackMuted(e);for(const i of this.simulcastCodecs.values())i.mediaStreamTrack.enabled=!e}getSenderStats(){return m(this,void 0,void 0,function*(){var e;if(!(!((e=this.sender)===null||e===void 0)&&e.getStats))return[];const i=[],n=yield this.sender.getStats();return n.forEach(s=>{var r;if(s.type==="outbound-rtp"){const o={type:"video",streamId:s.id,frameHeight:s.frameHeight,frameWidth:s.frameWidth,framesPerSecond:s.framesPerSecond,framesSent:s.framesSent,firCount:s.firCount,pliCount:s.pliCount,nackCount:s.nackCount,packetsSent:s.packetsSent,bytesSent:s.bytesSent,qualityLimitationReason:s.qualityLimitationReason,qualityLimitationDurations:s.qualityLimitationDurations,qualityLimitationResolutionChanges:s.qualityLimitationResolutionChanges,rid:(r=s.rid)!==null&&r!==void 0?r:s.id,retransmittedPacketsSent:s.retransmittedPacketsSent,targetBitrate:s.targetBitrate,timestamp:s.timestamp},a=n.get(s.remoteId);a&&(o.jitter=a.jitter,o.packetsLost=a.packetsLost,o.roundTripTime=a.roundTripTime),i.push(o)}}),i.sort((s,r)=>{var o,a;return((o=r.frameWidth)!==null&&o!==void 0?o:0)-((a=s.frameWidth)!==null&&a!==void 0?a:0)}),i})}setPublishingQuality(e){const i=[];for(let n=Le.LOW;n<=Le.HIGH;n+=1)i.push(new Xs({quality:n,enabled:n<=e}));this.log.debug("setting publishing quality. max quality ".concat(e),this.logContext),this.setPublishingLayers(it(this.codec),i)}restartTrack(e){return m(this,void 0,void 0,function*(){var i,n,s,r,o;let a;if(e){const u=tr({video:e});typeof u.video!="boolean"&&(a=u.video)}yield this.restart(a);try{for(var c=!0,d=tt(this.simulcastCodecs.values()),l;l=yield d.next(),i=l.done,!i;c=!0){r=l.value,c=!1;const u=r;u.sender&&((o=u.sender.transport)===null||o===void 0?void 0:o.state)!=="closed"&&(u.mediaStreamTrack=this.mediaStreamTrack.clone(),yield u.sender.replaceTrack(u.mediaStreamTrack))}}catch(u){n={error:u}}finally{try{!c&&!i&&(s=d.return)&&(yield s.call(d))}finally{if(n)throw n.error}}})}setProcessor(e){const i=Object.create(null,{setProcessor:{get:()=>super.setProcessor}});return m(this,arguments,void 0,function(n){var s=this;let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return function*(){var o,a,c,d,l,u;if(yield i.setProcessor.call(s,n,r),!((l=s.processor)===null||l===void 0)&&l.processedTrack)try{for(var h=!0,p=tt(s.simulcastCodecs.values()),g;g=yield p.next(),o=g.done,!o;h=!0)d=g.value,h=!1,yield(u=d.sender)===null||u===void 0?void 0:u.replaceTrack(s.processor.processedTrack)}catch(f){a={error:f}}finally{try{!h&&!o&&(c=p.return)&&(yield c.call(p))}finally{if(a)throw a.error}}}()})}setDegradationPreference(e){return m(this,void 0,void 0,function*(){if(this.degradationPreference=e,this.sender)try{this.log.debug("setting degradationPreference to ".concat(e),this.logContext);const i=this.sender.getParameters();i.degradationPreference=e,this.sender.setParameters(i)}catch(i){this.log.warn("failed to set degradationPreference",Object.assign({error:i},this.logContext))}})}addSimulcastTrack(e,i){if(this.simulcastCodecs.has(e)){this.log.error("".concat(e," already added, skipping adding simulcast codec"),this.logContext);return}const n={codec:e,mediaStreamTrack:this.mediaStreamTrack.clone(),sender:void 0,encodings:i};return this.simulcastCodecs.set(e,n),n}setSimulcastTrackSender(e,i){const n=this.simulcastCodecs.get(e);n&&(n.sender=i,setTimeout(()=>{this.subscribedCodecs&&this.setPublishingCodecs(this.subscribedCodecs)},vp))}setPublishingCodecs(e){return m(this,void 0,void 0,function*(){var i,n,s,r,o,a,c;if(this.log.debug("setting publishing codecs",Object.assign(Object.assign({},this.logContext),{codecs:e,currentCodec:this.codec})),!this.codec&&e.length>0)return yield this.setPublishingLayers(it(e[0].codec),e[0].qualities),[];this.subscribedCodecs=e;const d=[];try{for(i=!0,n=tt(e);s=yield n.next(),r=s.done,!r;i=!0){c=s.value,i=!1;const l=c;if(!this.codec||this.codec===l.codec)yield this.setPublishingLayers(it(l.codec),l.qualities);else{const u=this.simulcastCodecs.get(l.codec);if(this.log.debug("try setPublishingCodec for ".concat(l.codec),Object.assign(Object.assign({},this.logContext),{simulcastCodecInfo:u})),!u||!u.sender){for(const h of l.qualities)if(h.enabled){d.push(l.codec);break}}else u.encodings&&(this.log.debug("try setPublishingLayersForSender ".concat(l.codec),this.logContext),yield Oo(u.sender,u.encodings,l.qualities,this.senderLock,it(l.codec),this.log,this.logContext))}}}catch(l){o={error:l}}finally{try{!i&&!r&&(a=n.return)&&(yield a.call(n))}finally{if(o)throw o.error}}return d})}setPublishingLayers(e,i){return m(this,void 0,void 0,function*(){this.log.debug("setting publishing layers",Object.assign(Object.assign({},this.logContext),{qualities:i})),!(!this.sender||!this.encodings)&&(yield Oo(this.sender,this.encodings,i,this.senderLock,e,this.log,this.logContext))})}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return m(this,void 0,void 0,function*(){yield e.handleAppVisibilityChanged.call(this),Pc()&&this.isInBackground&&this.source===P.Source.Camera&&(this._mediaStreamTrack.enabled=!1)})}}function Oo(t,e,i,n,s,r,o){return m(this,void 0,void 0,function*(){const a=yield n.lock();r.debug("setPublishingLayersForSender",Object.assign(Object.assign({},o),{sender:t,qualities:i,senderEncodings:e}));try{const c=t.getParameters(),{encodings:d}=c;if(!d)return;if(d.length!==e.length){r.warn("cannot set publishing layers, encodings mismatch",Object.assign(Object.assign({},o),{encodings:d,senderEncodings:e}));return}let l=!1;const u=Oe();if((u==null?void 0:u.name)==="Chrome"&&ht(u==null?void 0:u.version,"133")>0&&d[0].scalabilityMode){const p=d[0],g=new ln(p.scalabilityMode);let f=Wt.OFF;if(i.forEach(y=>{y.enabled&&(f===Wt.OFF||y.quality>f)&&(f=y.quality)}),f===Wt.OFF)p.active&&(p.active=!1,l=!0);else if(!p.active||g.spatial!==f+1){l=!0,p.active=!0;const y=new ln(e[0].scalabilityMode);g.spatial=f+1,g.suffix=y.suffix,g.spatial===1&&(g.suffix=void 0),p.scalabilityMode=g.toString(),p.scaleResolutionDownBy=Math.pow(2,2-f),e[0].maxBitrate&&(p.maxBitrate=e[0].maxBitrate/(p.scaleResolutionDownBy*p.scaleResolutionDownBy))}}else s&&i.some(g=>g.enabled)&&i.forEach(g=>g.enabled=!0),d.forEach((p,g)=>{var f;let y=(f=p.rid)!==null&&f!==void 0?f:"";y===""&&(y="q");const S=Uc(y),x=i.find(M=>M.quality===S);x&&p.active!==x.enabled&&(l=!0,p.active=x.enabled,r.debug("setting layer ".concat(x.quality," to ").concat(p.active?"enabled":"disabled"),o),ei()&&(x.enabled?(p.scaleResolutionDownBy=e[g].scaleResolutionDownBy,p.maxBitrate=e[g].maxBitrate,p.maxFrameRate=e[g].maxFrameRate):(p.scaleResolutionDownBy=4,p.maxBitrate=10,p.maxFrameRate=2)))});l&&(c.encodings=d,r.debug("setting encodings",Object.assign(Object.assign({},o),{encodings:c.encodings})),yield t.setParameters(c))}finally{a()}})}function Uc(t){switch(t){case"f":return Le.HIGH;case"h":return Le.MEDIUM;case"q":return Le.LOW;default:return Le.HIGH}}function Ao(t,e,i,n){if(!i)return[new wt({quality:Le.HIGH,width:t,height:e,bitrate:0,ssrc:0})];if(n){const s=i[0].scalabilityMode,r=new ln(s),o=[],a=r.suffix=="h"?1.5:2,c=r.suffix=="h"?2:3;for(let d=0;d<r.spatial;d+=1)o.push(new wt({quality:Math.min(Le.HIGH,r.spatial-1)-d,width:Math.ceil(t/Math.pow(a,d)),height:Math.ceil(e/Math.pow(a,d)),bitrate:i[0].maxBitrate?Math.ceil(i[0].maxBitrate/Math.pow(c,d)):0,ssrc:0}));return o}return i.map(s=>{var r,o,a;const c=(r=s.scaleResolutionDownBy)!==null&&r!==void 0?r:1;let d=Uc((o=s.rid)!==null&&o!==void 0?o:"");return new wt({quality:d,width:Math.ceil(t/c),height:Math.ceil(e/c),bitrate:(a=s.maxBitrate)!==null&&a!==void 0?a:0,ssrc:0})})}const xo="_lossy",Lo="_reliable",bp=2*1e3,Kn="leave-reconnect";var Ve;(function(t){t[t.New=0]="New",t[t.Connected=1]="Connected",t[t.Disconnected=2]="Disconnected",t[t.Reconnecting=3]="Reconnecting",t[t.Closed=4]="Closed"})(Ve||(Ve={}));class yp extends Ye.EventEmitter{get isClosed(){return this._isClosed}get pendingReconnect(){return!!this.reconnectTimeout}constructor(e){var i;super(),this.options=e,this.rtcConfig={},this.peerConnectionTimeout=rr.peerConnectionTimeout,this.fullReconnectOnNext=!1,this.subscriberPrimary=!1,this.pcState=Ve.New,this._isClosed=!0,this.pendingTrackResolvers={},this.reconnectAttempts=0,this.reconnectStart=0,this.attemptingReconnect=!1,this.joinAttempts=0,this.maxJoinAttempts=1,this.shouldFailNext=!1,this.log=Q,this.handleDataChannel=n=>m(this,[n],void 0,function(s){var r=this;let{channel:o}=s;return function*(){if(o){if(o.label===Lo)r.reliableDCSub=o;else if(o.label===xo)r.lossyDCSub=o;else return;r.log.debug("on data channel ".concat(o.id,", ").concat(o.label),r.logContext),o.onmessage=r.handleDataMessage}}()}),this.handleDataMessage=n=>m(this,void 0,void 0,function*(){var s,r;const o=yield this.dataProcessLock.lock();try{let a;if(n.data instanceof ArrayBuffer)a=n.data;else if(n.data instanceof Blob)a=yield n.data.arrayBuffer();else{this.log.error("unsupported data type",Object.assign(Object.assign({},this.logContext),{data:n.data}));return}const c=Ie.fromBinary(new Uint8Array(a));((s=c.value)===null||s===void 0?void 0:s.case)==="speaker"?this.emit(O.ActiveSpeakersUpdate,c.value.value.speakers):(((r=c.value)===null||r===void 0?void 0:r.case)==="user"&&kp(c,c.value.value),this.emit(O.DataPacketReceived,c))}finally{o()}}),this.handleDataError=n=>{const r=n.currentTarget.maxRetransmits===0?"lossy":"reliable";if(n instanceof ErrorEvent&&n.error){const{error:o}=n.error;this.log.error("DataChannel error on ".concat(r,": ").concat(n.message),Object.assign(Object.assign({},this.logContext),{error:o}))}else this.log.error("Unknown DataChannel error on ".concat(r),Object.assign(Object.assign({},this.logContext),{event:n}))},this.handleBufferedAmountLow=n=>{const r=n.currentTarget.maxRetransmits===0?ee.LOSSY:ee.RELIABLE;this.updateAndEmitDCBufferStatus(r)},this.handleDisconnect=(n,s)=>{if(this._isClosed)return;this.log.warn("".concat(n," disconnected"),this.logContext),this.reconnectAttempts===0&&(this.reconnectStart=Date.now());const r=c=>{this.log.warn("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(c,"ms. giving up"),this.logContext),this.emit(O.Disconnected),this.close()},o=Date.now()-this.reconnectStart;let a=this.getNextRetryDelay({elapsedMs:o,retryCount:this.reconnectAttempts});if(a===null){r(o);return}n===Kn&&(a=0),this.log.debug("reconnecting in ".concat(a,"ms"),this.logContext),this.clearReconnectTimeout(),this.token&&this.regionUrlProvider&&this.regionUrlProvider.updateToken(this.token),this.reconnectTimeout=Pe.setTimeout(()=>this.attemptReconnect(s).finally(()=>this.reconnectTimeout=void 0),a)},this.waitForRestarted=()=>new Promise((n,s)=>{this.pcState===Ve.Connected&&n();const r=()=>{this.off(O.Disconnected,o),n()},o=()=>{this.off(O.Restarted,r),s()};this.once(O.Restarted,r),this.once(O.Disconnected,o)}),this.updateAndEmitDCBufferStatus=n=>{const s=this.isBufferStatusLow(n);typeof s<"u"&&s!==this.dcBufferStatus.get(n)&&(this.dcBufferStatus.set(n,s),this.emit(O.DCBufferStatusChanged,s,n))},this.isBufferStatusLow=n=>{const s=this.dataChannelForKind(n);if(s)return s.bufferedAmount<=s.bufferedAmountLowThreshold},this.handleBrowserOnLine=()=>{this.client.currentState===oe.RECONNECTING&&(this.clearReconnectTimeout(),this.attemptReconnect(Vt.RR_SIGNAL_DISCONNECTED))},this.log=nt((i=e.loggerName)!==null&&i!==void 0?i:We.Engine),this.loggerOptions={loggerName:e.loggerName,loggerContextCb:()=>this.logContext},this.client=new ir(void 0,this.loggerOptions),this.client.signalLatency=this.options.expSignalLatency,this.reconnectPolicy=this.options.reconnectPolicy,this.registerOnLineListener(),this.closingLock=new Me,this.dataProcessLock=new Me,this.dcBufferStatus=new Map([[ee.LOSSY,!0],[ee.RELIABLE,!0]]),this.client.onParticipantUpdate=n=>this.emit(O.ParticipantUpdate,n),this.client.onConnectionQuality=n=>this.emit(O.ConnectionQualityUpdate,n),this.client.onRoomUpdate=n=>this.emit(O.RoomUpdate,n),this.client.onSubscriptionError=n=>this.emit(O.SubscriptionError,n),this.client.onSubscriptionPermissionUpdate=n=>this.emit(O.SubscriptionPermissionUpdate,n),this.client.onSpeakersChanged=n=>this.emit(O.SpeakersChanged,n),this.client.onStreamStateUpdate=n=>this.emit(O.StreamStateChanged,n),this.client.onRequestResponse=n=>this.emit(O.SignalRequestResponse,n)}get logContext(){var e,i,n,s,r,o;return{room:(i=(e=this.latestJoinResponse)===null||e===void 0?void 0:e.room)===null||i===void 0?void 0:i.name,roomID:(s=(n=this.latestJoinResponse)===null||n===void 0?void 0:n.room)===null||s===void 0?void 0:s.sid,participant:(o=(r=this.latestJoinResponse)===null||r===void 0?void 0:r.participant)===null||o===void 0?void 0:o.identity,pID:this.participantSid}}join(e,i,n,s){return m(this,void 0,void 0,function*(){this.url=e,this.token=i,this.signalOpts=n,this.maxJoinAttempts=n.maxRetries;try{this.joinAttempts+=1,this.setupSignalClientCallbacks();const r=yield this.client.join(e,i,n,s);return this._isClosed=!1,this.latestJoinResponse=r,this.subscriberPrimary=r.subscriberPrimary,this.pcManager||(yield this.configure(r)),(!this.subscriberPrimary||r.fastPublish)&&this.negotiate(),this.clientConfiguration=r.clientConfiguration,this.emit(O.SignalConnected,r),r}catch(r){if(r instanceof re&&r.reason===Y.ServerUnreachable&&(this.log.warn("Couldn't connect to server, attempt ".concat(this.joinAttempts," of ").concat(this.maxJoinAttempts),this.logContext),this.joinAttempts<this.maxJoinAttempts))return this.join(e,i,n,s);throw r}})}close(){return m(this,void 0,void 0,function*(){const e=yield this.closingLock.lock();if(this.isClosed){e();return}try{this._isClosed=!0,this.joinAttempts=0,this.emit(O.Closing),this.removeAllListeners(),this.deregisterOnLineListener(),this.clearPendingReconnect(),yield this.cleanupPeerConnections(),yield this.cleanupClient()}finally{e()}})}cleanupPeerConnections(){return m(this,void 0,void 0,function*(){var e;yield(e=this.pcManager)===null||e===void 0?void 0:e.close(),this.pcManager=void 0;const i=n=>{n&&(n.close(),n.onbufferedamountlow=null,n.onclose=null,n.onclosing=null,n.onerror=null,n.onmessage=null,n.onopen=null)};i(this.lossyDC),i(this.lossyDCSub),i(this.reliableDC),i(this.reliableDCSub),this.lossyDC=void 0,this.lossyDCSub=void 0,this.reliableDC=void 0,this.reliableDCSub=void 0})}cleanupClient(){return m(this,void 0,void 0,function*(){yield this.client.close(),this.client.resetCallbacks()})}addTrack(e){if(this.pendingTrackResolvers[e.cid])throw new $e("a track with the same ID has already been published");return new Promise((i,n)=>{const s=setTimeout(()=>{delete this.pendingTrackResolvers[e.cid],n(new re("publication of local track timed out, no response from server",Y.Timeout))},1e4);this.pendingTrackResolvers[e.cid]={resolve:r=>{clearTimeout(s),i(r)},reject:()=>{clearTimeout(s),n(new Error("Cancelled publication by calling unpublish"))}},this.client.sendAddTrack(e)})}removeTrack(e){if(e.track&&this.pendingTrackResolvers[e.track.id]){const{reject:i}=this.pendingTrackResolvers[e.track.id];i&&i(),delete this.pendingTrackResolvers[e.track.id]}try{return this.pcManager.removeTrack(e),!0}catch(i){this.log.warn("failed to remove track",Object.assign(Object.assign({},this.logContext),{error:i}))}return!1}updateMuteStatus(e,i){this.client.sendMuteTrack(e,i)}get dataSubscriberReadyState(){var e;return(e=this.reliableDCSub)===null||e===void 0?void 0:e.readyState}getConnectedServerAddress(){return m(this,void 0,void 0,function*(){var e;return(e=this.pcManager)===null||e===void 0?void 0:e.getConnectedAddress()})}setRegionUrlProvider(e){this.regionUrlProvider=e}configure(e){return m(this,void 0,void 0,function*(){var i,n;if(this.pcManager&&this.pcManager.currentState!==le.NEW)return;this.participantSid=(i=e.participant)===null||i===void 0?void 0:i.sid;const s=this.makeRTCConfiguration(e);this.pcManager=new Zm(s,e.subscriberPrimary,this.loggerOptions),this.emit(O.TransportsCreated,this.pcManager.publisher,this.pcManager.subscriber),this.pcManager.onIceCandidate=(r,o)=>{this.client.sendIceCandidate(r,o)},this.pcManager.onPublisherOffer=r=>{this.client.sendOffer(r)},this.pcManager.onDataChannel=this.handleDataChannel,this.pcManager.onStateChange=(r,o,a)=>m(this,void 0,void 0,function*(){if(this.log.debug("primary PC state changed ".concat(r),this.logContext),["closed","disconnected","failed"].includes(o)&&(this.publisherConnectionPromise=void 0),r===le.CONNECTED){const l=this.pcState===Ve.New;this.pcState=Ve.Connected,l&&this.emit(O.Connected,e)}else r===le.FAILED&&this.pcState===Ve.Connected&&(this.pcState=Ve.Disconnected,this.handleDisconnect("peerconnection failed",a==="failed"?Vt.RR_SUBSCRIBER_FAILED:Vt.RR_PUBLISHER_FAILED));const c=this.client.isDisconnected||this.client.currentState===oe.RECONNECTING,d=[le.FAILED,le.CLOSING,le.CLOSED].includes(r);c&&d&&!this._isClosed&&this.emit(O.Offline)}),this.pcManager.onTrack=r=>{this.emit(O.MediaTrackAdded,r.track,r.streams[0],r.receiver)},Sp((n=e.serverInfo)===null||n===void 0?void 0:n.protocol)||this.createDataChannels()})}setupSignalClientCallbacks(){this.client.onAnswer=e=>m(this,void 0,void 0,function*(){this.pcManager&&(this.log.debug("received server answer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type})),yield this.pcManager.setPublisherAnswer(e))}),this.client.onTrickle=(e,i)=>{this.pcManager&&(this.log.debug("got ICE candidate from peer",Object.assign(Object.assign({},this.logContext),{candidate:e,target:i})),this.pcManager.addIceCandidate(e,i))},this.client.onOffer=e=>m(this,void 0,void 0,function*(){if(!this.pcManager)return;const i=yield this.pcManager.createSubscriberAnswerFromOffer(e);this.client.sendAnswer(i)}),this.client.onLocalTrackPublished=e=>{var i;if(this.log.debug("received trackPublishedResponse",Object.assign(Object.assign({},this.logContext),{cid:e.cid,track:(i=e.track)===null||i===void 0?void 0:i.sid})),!this.pendingTrackResolvers[e.cid]){this.log.error("missing track resolver for ".concat(e.cid),Object.assign(Object.assign({},this.logContext),{cid:e.cid}));return}const{resolve:n}=this.pendingTrackResolvers[e.cid];delete this.pendingTrackResolvers[e.cid],n(e.track)},this.client.onLocalTrackUnpublished=e=>{this.emit(O.LocalTrackUnpublished,e)},this.client.onLocalTrackSubscribed=e=>{this.emit(O.LocalTrackSubscribed,e)},this.client.onTokenRefresh=e=>{this.token=e},this.client.onRemoteMuteChanged=(e,i)=>{this.emit(O.RemoteMute,e,i)},this.client.onSubscribedQualityUpdate=e=>{this.emit(O.SubscribedQualityUpdate,e)},this.client.onRoomMoved=e=>{var i;this.participantSid=(i=e.participant)===null||i===void 0?void 0:i.sid,this.latestJoinResponse&&(this.latestJoinResponse.room=e.room),this.emit(O.RoomMoved,e)},this.client.onClose=()=>{this.handleDisconnect("signal",Vt.RR_SIGNAL_DISCONNECTED)},this.client.onLeave=e=>{switch(this.log.debug("client leave request",Object.assign(Object.assign({},this.logContext),{reason:e==null?void 0:e.reason})),e.regions&&this.regionUrlProvider&&(this.log.debug("updating regions",this.logContext),this.regionUrlProvider.setServerReportedRegions(e.regions)),e.action){case Kt.DISCONNECT:this.emit(O.Disconnected,e==null?void 0:e.reason),this.close();break;case Kt.RECONNECT:this.fullReconnectOnNext=!0,this.handleDisconnect(Kn);break;case Kt.RESUME:this.handleDisconnect(Kn)}}}makeRTCConfiguration(e){var i;const n=Object.assign({},this.rtcConfig);if(!((i=this.signalOpts)===null||i===void 0)&&i.e2eeEnabled&&(this.log.debug("E2EE - setting up transports with insertable streams",this.logContext),n.encodedInsertableStreams=!0),e.iceServers&&!n.iceServers){const s=[];e.iceServers.forEach(r=>{const o={urls:r.urls};r.username&&(o.username=r.username),r.credential&&(o.credential=r.credential),s.push(o)}),n.iceServers=s}return e.clientConfiguration&&e.clientConfiguration.forceRelay===Ri.ENABLED&&(n.iceTransportPolicy="relay"),n.sdpSemantics="unified-plan",n.continualGatheringPolicy="gather_continually",n}createDataChannels(){this.pcManager&&(this.lossyDC&&(this.lossyDC.onmessage=null,this.lossyDC.onerror=null),this.reliableDC&&(this.reliableDC.onmessage=null,this.reliableDC.onerror=null),this.lossyDC=this.pcManager.createPublisherDataChannel(xo,{ordered:!1,maxRetransmits:0}),this.reliableDC=this.pcManager.createPublisherDataChannel(Lo,{ordered:!0}),this.lossyDC.onmessage=this.handleDataMessage,this.reliableDC.onmessage=this.handleDataMessage,this.lossyDC.onerror=this.handleDataError,this.reliableDC.onerror=this.handleDataError,this.lossyDC.bufferedAmountLowThreshold=65535,this.reliableDC.bufferedAmountLowThreshold=65535,this.lossyDC.onbufferedamountlow=this.handleBufferedAmountLow,this.reliableDC.onbufferedamountlow=this.handleBufferedAmountLow)}createSender(e,i,n){return m(this,void 0,void 0,function*(){if(Ds())return yield this.createTransceiverRTCRtpSender(e,i,n);if(Os())return this.log.warn("using add-track fallback",this.logContext),yield this.createRTCRtpSender(e.mediaStreamTrack);throw new fe("Required webRTC APIs not supported on this device")})}createSimulcastSender(e,i,n,s){return m(this,void 0,void 0,function*(){if(Ds())return this.createSimulcastTransceiverSender(e,i,n,s);if(Os())return this.log.debug("using add-track fallback",this.logContext),this.createRTCRtpSender(e.mediaStreamTrack);throw new fe("Cannot stream on this device")})}createTransceiverRTCRtpSender(e,i,n){return m(this,void 0,void 0,function*(){if(!this.pcManager)throw new fe("publisher is closed");const s=[];e.mediaStream&&s.push(e.mediaStream),ii(e)&&(e.codec=i.videoCodec);const r={direction:"sendonly",streams:s};return n&&(r.sendEncodings=n),(yield this.pcManager.addPublisherTransceiver(e.mediaStreamTrack,r)).sender})}createSimulcastTransceiverSender(e,i,n,s){return m(this,void 0,void 0,function*(){if(!this.pcManager)throw new fe("publisher is closed");const r={direction:"sendonly"};s&&(r.sendEncodings=s);const o=yield this.pcManager.addPublisherTransceiver(i.mediaStreamTrack,r);if(n.videoCodec)return e.setSimulcastTrackSender(n.videoCodec,o.sender),o.sender})}createRTCRtpSender(e){return m(this,void 0,void 0,function*(){if(!this.pcManager)throw new fe("publisher is closed");return this.pcManager.addPublisherTrack(e)})}attemptReconnect(e){return m(this,void 0,void 0,function*(){var i,n,s;if(!this._isClosed){if(this.attemptingReconnect){Q.warn("already attempting reconnect, returning early",this.logContext);return}(((i=this.clientConfiguration)===null||i===void 0?void 0:i.resumeConnection)===Ri.DISABLED||((s=(n=this.pcManager)===null||n===void 0?void 0:n.currentState)!==null&&s!==void 0?s:le.NEW)===le.NEW)&&(this.fullReconnectOnNext=!0);try{this.attemptingReconnect=!0,this.fullReconnectOnNext?yield this.restartConnection():yield this.resumeConnection(e),this.clearPendingReconnect(),this.fullReconnectOnNext=!1}catch(r){this.reconnectAttempts+=1;let o=!0;r instanceof fe?(this.log.debug("received unrecoverable error",Object.assign(Object.assign({},this.logContext),{error:r})),o=!1):r instanceof Bt||(this.fullReconnectOnNext=!0),o?this.handleDisconnect("reconnect",Vt.RR_UNKNOWN):(this.log.info("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(Date.now()-this.reconnectStart,"ms. giving up"),this.logContext),this.emit(O.Disconnected),yield this.close())}finally{this.attemptingReconnect=!1}}})}getNextRetryDelay(e){try{return this.reconnectPolicy.nextRetryDelayInMs(e)}catch(i){this.log.warn("encountered error in reconnect policy",Object.assign(Object.assign({},this.logContext),{error:i}))}return null}restartConnection(e){return m(this,void 0,void 0,function*(){var i,n,s;try{if(!this.url||!this.token)throw new fe("could not reconnect, url or token not saved");this.log.info("reconnecting, attempt: ".concat(this.reconnectAttempts),this.logContext),this.emit(O.Restarting),this.client.isDisconnected||(yield this.client.sendLeave()),yield this.cleanupPeerConnections(),yield this.cleanupClient();let r;try{if(!this.signalOpts)throw this.log.warn("attempted connection restart, without signal options present",this.logContext),new Bt;r=yield this.join(e??this.url,this.token,this.signalOpts)}catch(o){throw o instanceof re&&o.reason===Y.NotAllowed?new fe("could not reconnect, token might be expired"):new Bt}if(this.shouldFailNext)throw this.shouldFailNext=!1,new Error("simulated failure");if(this.client.setReconnected(),this.emit(O.SignalRestarted,r),yield this.waitForPCReconnected(),this.client.currentState!==oe.CONNECTED)throw new Bt("Signal connection got severed during reconnect");(i=this.regionUrlProvider)===null||i===void 0||i.resetAttempts(),this.emit(O.Restarted)}catch(r){const o=yield(n=this.regionUrlProvider)===null||n===void 0?void 0:n.getNextBestRegionUrl();if(o){yield this.restartConnection(o);return}else throw(s=this.regionUrlProvider)===null||s===void 0||s.resetAttempts(),r}})}resumeConnection(e){return m(this,void 0,void 0,function*(){var i;if(!this.url||!this.token)throw new fe("could not reconnect, url or token not saved");if(!this.pcManager)throw new fe("publisher and subscriber connections unset");this.log.info("resuming signal connection, attempt ".concat(this.reconnectAttempts),this.logContext),this.emit(O.Resuming);let n;try{this.setupSignalClientCallbacks(),n=yield this.client.reconnect(this.url,this.token,this.participantSid,e)}catch(s){let r="";throw s instanceof Error&&(r=s.message,this.log.error(s.message,Object.assign(Object.assign({},this.logContext),{error:s}))),s instanceof re&&s.reason===Y.NotAllowed?new fe("could not reconnect, token might be expired"):s instanceof re&&s.reason===Y.LeaveRequest?s:new Bt(r)}if(this.emit(O.SignalResumed),n){const s=this.makeRTCConfiguration(n);this.pcManager.updateConfiguration(s)}else this.log.warn("Did not receive reconnect response",this.logContext);if(this.shouldFailNext)throw this.shouldFailNext=!1,new Error("simulated failure");if(yield this.pcManager.triggerIceRestart(),yield this.waitForPCReconnected(),this.client.currentState!==oe.CONNECTED)throw new Bt("Signal connection got severed during reconnect");this.client.setReconnected(),((i=this.reliableDC)===null||i===void 0?void 0:i.readyState)==="open"&&this.reliableDC.id===null&&this.createDataChannels(),this.emit(O.Resumed)})}waitForPCInitialConnection(e,i){return m(this,void 0,void 0,function*(){if(!this.pcManager)throw new fe("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(i,e)})}waitForPCReconnected(){return m(this,void 0,void 0,function*(){this.pcState=Ve.Reconnecting,this.log.debug("waiting for peer connection to reconnect",this.logContext);try{if(yield Ae(bp),!this.pcManager)throw new fe("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(void 0,this.peerConnectionTimeout),this.pcState=Ve.Connected}catch(e){throw this.pcState=Ve.Disconnected,new re("could not establish PC connection, ".concat(e.message),Y.InternalError)}})}publishRpcResponse(e,i,n,s){return m(this,void 0,void 0,function*(){const r=new Ie({destinationIdentities:[e],kind:ee.RELIABLE,value:{case:"rpcResponse",value:new Ia({requestId:i,value:s?{case:"error",value:s.toProto()}:{case:"payload",value:n??""}})}});yield this.sendDataPacket(r,ee.RELIABLE)})}publishRpcAck(e,i){return m(this,void 0,void 0,function*(){const n=new Ie({destinationIdentities:[e],kind:ee.RELIABLE,value:{case:"rpcAck",value:new Ea({requestId:i})}});yield this.sendDataPacket(n,ee.RELIABLE)})}sendDataPacket(e,i){return m(this,void 0,void 0,function*(){const n=e.toBinary();yield this.ensurePublisherConnected(i);const s=this.dataChannelForKind(i);s&&s.send(n),this.updateAndEmitDCBufferStatus(i)})}waitForBufferStatusLow(e){return new Promise((i,n)=>m(this,void 0,void 0,function*(){if(this.isBufferStatusLow(e))i();else{const s=()=>n("Engine closed");for(this.once(O.Closing,s);!this.dcBufferStatus.get(e);)yield Ae(10);this.off(O.Closing,s),i()}}))}ensureDataTransportConnected(e){return m(this,arguments,void 0,function(i){var n=this;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.subscriberPrimary;return function*(){var r;if(!n.pcManager)throw new fe("PC manager is closed");const o=s?n.pcManager.subscriber:n.pcManager.publisher,a=s?"Subscriber":"Publisher";if(!o)throw new re("".concat(a," connection not set"),Y.InternalError);let c=!1;!s&&!n.dataChannelForKind(i,s)&&(n.createDataChannels(),c=!0),!c&&!s&&!n.pcManager.publisher.isICEConnected&&n.pcManager.publisher.getICEConnectionState()!=="checking"&&(c=!0),c&&n.negotiate();const d=n.dataChannelForKind(i,s);if((d==null?void 0:d.readyState)==="open")return;const l=new Date().getTime()+n.peerConnectionTimeout;for(;new Date().getTime()<l;){if(o.isICEConnected&&((r=n.dataChannelForKind(i,s))===null||r===void 0?void 0:r.readyState)==="open")return;yield Ae(50)}throw new re("could not establish ".concat(a," connection, state: ").concat(o.getICEConnectionState()),Y.InternalError)}()})}ensurePublisherConnected(e){return m(this,void 0,void 0,function*(){this.publisherConnectionPromise||(this.publisherConnectionPromise=this.ensureDataTransportConnected(e,!1)),yield this.publisherConnectionPromise})}verifyTransport(){return!(!this.pcManager||this.pcManager.currentState!==le.CONNECTED||!this.client.ws||this.client.ws.readyState===WebSocket.CLOSED)}negotiate(){return m(this,void 0,void 0,function*(){return new Promise((e,i)=>m(this,void 0,void 0,function*(){if(!this.pcManager){i(new ws("PC manager is closed"));return}this.pcManager.requirePublisher(),this.pcManager.publisher.getTransceivers().length==0&&!this.lossyDC&&!this.reliableDC&&this.createDataChannels();const n=new AbortController,s=()=>{n.abort(),this.log.debug("engine disconnected while negotiation was ongoing",this.logContext),e()};this.isClosed&&i("cannot negotiate on closed engine"),this.on(O.Closing,s),this.pcManager.publisher.once(Yt.RTPVideoPayloadTypes,r=>{const o=new Map;r.forEach(a=>{const c=a.codec.toLowerCase();Em(c)&&o.set(a.payload,c)}),this.emit(O.RTPVideoMapUpdate,o)});try{yield this.pcManager.negotiate(n),e()}catch(r){r instanceof ws&&(this.fullReconnectOnNext=!0),this.handleDisconnect("negotiation",Vt.RR_UNKNOWN),i(r)}finally{this.off(O.Closing,s)}}))})}dataChannelForKind(e,i){if(i){if(e===ee.LOSSY)return this.lossyDCSub;if(e===ee.RELIABLE)return this.reliableDCSub}else{if(e===ee.LOSSY)return this.lossyDC;if(e===ee.RELIABLE)return this.reliableDC}}sendSyncState(e,i){var n,s;if(!this.pcManager){this.log.warn("sync state cannot be sent without peer connection setup",this.logContext);return}const r=this.pcManager.subscriber.getLocalDescription(),o=this.pcManager.subscriber.getRemoteDescription(),a=(s=(n=this.signalOpts)===null||n===void 0?void 0:n.autoSubscribe)!==null&&s!==void 0?s:!0,c=new Array,d=new Array;e.forEach(l=>{l.isDesired!==a&&c.push(l.trackSid),l.isEnabled||d.push(l.trackSid)}),this.client.sendSyncState(new za({answer:r?dn({sdp:r.sdp,type:r.type}):void 0,offer:o?dn({sdp:o.sdp,type:o.type}):void 0,subscription:new fn({trackSids:c,subscribe:!a,participantTracks:[]}),publishTracks:Am(i),dataChannels:this.dataChannelsInfo(),trackSidsDisabled:d}))}failNext(){this.shouldFailNext=!0}dataChannelsInfo(){const e=[],i=(n,s)=>{(n==null?void 0:n.id)!==void 0&&n.id!==null&&e.push(new qa({label:n.label,id:n.id,target:s}))};return i(this.dataChannelForKind(ee.LOSSY),qe.PUBLISHER),i(this.dataChannelForKind(ee.RELIABLE),qe.PUBLISHER),i(this.dataChannelForKind(ee.LOSSY,!0),qe.SUBSCRIBER),i(this.dataChannelForKind(ee.RELIABLE,!0),qe.SUBSCRIBER),e}clearReconnectTimeout(){this.reconnectTimeout&&Pe.clearTimeout(this.reconnectTimeout)}clearPendingReconnect(){this.clearReconnectTimeout(),this.reconnectAttempts=0}registerOnLineListener(){De()&&window.addEventListener("online",this.handleBrowserOnLine)}deregisterOnLineListener(){De()&&window.removeEventListener("online",this.handleBrowserOnLine)}}class Bt extends Error{}function Sp(t){return t!==void 0&&t>13}function kp(t,e){const i=t.participantIdentity?t.participantIdentity:e.participantIdentity;t.participantIdentity=i,e.participantIdentity=i;const n=t.destinationIdentities.length!==0?t.destinationIdentities:e.destinationIdentities;t.destinationIdentities=n,e.destinationIdentities=n}class zs{constructor(e,i){this.lastUpdateAt=0,this.settingsCacheTime=3e3,this.attemptedRegions=[],this.serverUrl=new URL(e),this.token=i}updateToken(e){this.token=e}isCloud(){return xs(this.serverUrl)}getServerUrl(){return this.serverUrl}getNextBestRegionUrl(e){return m(this,void 0,void 0,function*(){if(!this.isCloud())throw Error("region availability is only supported for LiveKit Cloud domains");(!this.regionSettings||Date.now()-this.lastUpdateAt>this.settingsCacheTime)&&(this.regionSettings=yield this.fetchRegionSettings(e));const i=this.regionSettings.regions.filter(n=>!this.attemptedRegions.find(s=>s.url===n.url));if(i.length>0){const n=i[0];return this.attemptedRegions.push(n),Q.debug("next region: ".concat(n.region)),n.url}else return null})}resetAttempts(){this.attemptedRegions=[]}fetchRegionSettings(e){return m(this,void 0,void 0,function*(){const i=yield fetch("".concat(Cp(this.serverUrl),"/regions"),{headers:{authorization:"Bearer ".concat(this.token)},signal:e});if(i.ok){const n=yield i.json();return this.lastUpdateAt=Date.now(),n}else throw new re("Could not fetch region settings: ".concat(i.statusText),i.status===401?Y.NotAllowed:Y.InternalError,i.status)})}setServerReportedRegions(e){this.regionSettings=e,this.lastUpdateAt=Date.now()}}function Cp(t){return"".concat(t.protocol.replace("ws","http"),"//").concat(t.host,"/settings")}class Fc{get info(){return this._info}constructor(e,i,n){this.reader=i,this.totalByteSize=n,this._info=e,this.bytesReceived=0}}class Tp extends Fc{handleChunkReceived(e){var i;this.bytesReceived+=e.content.byteLength;const n=this.totalByteSize?this.bytesReceived/this.totalByteSize:void 0;(i=this.onProgress)===null||i===void 0||i.call(this,n)}[Symbol.asyncIterator](){const e=this.reader.getReader();return{next:()=>m(this,void 0,void 0,function*(){try{const{done:i,value:n}=yield e.read();return i?{done:!0,value:void 0}:(this.handleChunkReceived(n),{done:!1,value:n.content})}catch{return{done:!0,value:void 0}}}),return(){return m(this,void 0,void 0,function*(){return e.releaseLock(),{done:!0,value:void 0}})}}}readAll(){return m(this,void 0,void 0,function*(){var e,i,n,s;let r=new Set;try{for(var o=!0,a=tt(this),c;c=yield a.next(),e=c.done,!e;o=!0){s=c.value,o=!1;const d=s;r.add(d)}}catch(d){i={error:d}}finally{try{!o&&!e&&(n=a.return)&&(yield n.call(a))}finally{if(i)throw i.error}}return Array.from(r)})}}class Pp extends Fc{constructor(e,i,n){super(e,i,n),this.receivedChunks=new Map}handleChunkReceived(e){var i;const n=Xi(e.chunkIndex),s=this.receivedChunks.get(n);if(s&&s.version>e.version)return;this.receivedChunks.set(n,e),this.bytesReceived+=e.content.byteLength;const r=this.totalByteSize?this.bytesReceived/this.totalByteSize:void 0;(i=this.onProgress)===null||i===void 0||i.call(this,r)}[Symbol.asyncIterator](){const e=this.reader.getReader(),i=new TextDecoder;return{next:()=>m(this,void 0,void 0,function*(){try{const{done:n,value:s}=yield e.read();return n?{done:!0,value:void 0}:(this.handleChunkReceived(s),{done:!1,value:i.decode(s.content)})}catch{return{done:!0,value:void 0}}}),return(){return m(this,void 0,void 0,function*(){return e.releaseLock(),{done:!0,value:void 0}})}}}readAll(){return m(this,void 0,void 0,function*(){var e,i,n,s;let r="";try{for(var o=!0,a=tt(this),c;c=yield a.next(),e=c.done,!e;o=!0)s=c.value,o=!1,r+=s}catch(d){i={error:d}}finally{try{!o&&!e&&(n=a.return)&&(yield n.call(a))}finally{if(i)throw i.error}}return r})}}class Vc{constructor(e,i,n){this.writableStream=e,this.defaultWriter=e.getWriter(),this.onClose=n,this.info=i}write(e){return this.defaultWriter.write(e)}close(){return m(this,void 0,void 0,function*(){var e;yield this.defaultWriter.close(),this.defaultWriter.releaseLock(),(e=this.onClose)===null||e===void 0||e.call(this)})}}class Ep extends Vc{}class Ip extends Vc{}class Bc extends P{constructor(e,i,n,s,r){super(e,n,r),this.sid=i,this.receiver=s}get isLocal(){return!1}setMuted(e){this.isMuted!==e&&(this.isMuted=e,this._mediaStreamTrack.enabled=!e,this.emit(e?D.Muted:D.Unmuted,this))}setMediaStream(e){this.mediaStream=e;const i=n=>{n.track===this._mediaStreamTrack&&(e.removeEventListener("removetrack",i),this.receiver&&"playoutDelayHint"in this.receiver&&(this.receiver.playoutDelayHint=void 0),this.receiver=void 0,this._currentBitrate=0,this.emit(D.Ended,this))};e.addEventListener("removetrack",i)}start(){this.startMonitor(),super.enable()}stop(){this.stopMonitor(),super.disable()}getRTCStatsReport(){return m(this,void 0,void 0,function*(){var e;return!((e=this.receiver)===null||e===void 0)&&e.getStats?yield this.receiver.getStats():void 0})}setPlayoutDelay(e){this.receiver?"playoutDelayHint"in this.receiver?this.receiver.playoutDelayHint=e:this.log.warn("Playout delay not supported in this browser"):this.log.warn("Cannot set playout delay, track already ended")}getPlayoutDelay(){if(this.receiver){if("playoutDelayHint"in this.receiver)return this.receiver.playoutDelayHint;this.log.warn("Playout delay not supported in this browser")}else this.log.warn("Cannot get playout delay, track already ended");return 0}startMonitor(){this.monitorInterval||(this.monitorInterval=setInterval(()=>this.monitorReceiver(),ar)),xm()&&this.registerTimeSyncUpdate()}registerTimeSyncUpdate(){const e=()=>{var i;this.timeSyncHandle=requestAnimationFrame(()=>e());const n=(i=this.receiver)===null||i===void 0?void 0:i.getSynchronizationSources()[0];if(n){const{timestamp:s,rtpTimestamp:r}=n;r&&this.rtpTimestamp!==r&&(this.emit(D.TimeSyncUpdate,{timestamp:s,rtpTimestamp:r}),this.rtpTimestamp=r)}};e()}}class Rp extends Bc{constructor(e,i,n,s,r,o){super(e,i,P.Kind.Audio,n,o),this.monitorReceiver=()=>m(this,void 0,void 0,function*(){if(!this.receiver){this._currentBitrate=0;return}const a=yield this.getReceiverStats();a&&this.prevStats&&this.receiver&&(this._currentBitrate=Sn(a,this.prevStats)),this.prevStats=a}),this.audioContext=s,this.webAudioPluginNodes=[],r&&(this.sinkId=r.deviceId)}setVolume(e){var i;for(const n of this.attachedElements)this.audioContext?(i=this.gainNode)===null||i===void 0||i.gain.setTargetAtTime(e,0,.1):n.volume=e;Qe()&&this._mediaStreamTrack._setVolume(e),this.elementVolume=e}getVolume(){if(this.elementVolume)return this.elementVolume;if(Qe())return 1;let e=0;return this.attachedElements.forEach(i=>{i.volume>e&&(e=i.volume)}),e}setSinkId(e){return m(this,void 0,void 0,function*(){this.sinkId=e,yield Promise.all(this.attachedElements.map(i=>{if(As(i))return i.setSinkId(e)}))})}attach(e){const i=this.attachedElements.length===0;return e?super.attach(e):e=super.attach(),this.sinkId&&As(e)&&e.setSinkId(this.sinkId).catch(n=>{this.log.error("Failed to set sink id on remote audio track",n,this.logContext)}),this.audioContext&&i&&(this.log.debug("using audio context mapping",this.logContext),this.connectWebAudio(this.audioContext,e),e.volume=0,e.muted=!0),this.elementVolume&&this.setVolume(this.elementVolume),e}detach(e){let i;return e?(i=super.detach(e),this.audioContext&&(this.attachedElements.length>0?this.connectWebAudio(this.audioContext,this.attachedElements[0]):this.disconnectWebAudio())):(i=super.detach(),this.disconnectWebAudio()),i}setAudioContext(e){this.audioContext=e,e&&this.attachedElements.length>0?this.connectWebAudio(e,this.attachedElements[0]):e||this.disconnectWebAudio()}setWebAudioPlugins(e){this.webAudioPluginNodes=e,this.attachedElements.length>0&&this.audioContext&&this.connectWebAudio(this.audioContext,this.attachedElements[0])}connectWebAudio(e,i){this.disconnectWebAudio(),this.sourceNode=e.createMediaStreamSource(i.srcObject);let n=this.sourceNode;this.webAudioPluginNodes.forEach(s=>{n.connect(s),n=s}),this.gainNode=e.createGain(),n.connect(this.gainNode),this.gainNode.connect(e.destination),this.elementVolume&&this.gainNode.gain.setTargetAtTime(this.elementVolume,0,.1),e.state!=="running"&&e.resume().then(()=>{e.state!=="running"&&this.emit(D.AudioPlaybackFailed,new Error("Audio Context couldn't be started automatically"))}).catch(s=>{this.emit(D.AudioPlaybackFailed,s)})}disconnectWebAudio(){var e,i;(e=this.gainNode)===null||e===void 0||e.disconnect(),(i=this.sourceNode)===null||i===void 0||i.disconnect(),this.gainNode=void 0,this.sourceNode=void 0}getReceiverStats(){return m(this,void 0,void 0,function*(){if(!this.receiver||!this.receiver.getStats)return;const e=yield this.receiver.getStats();let i;return e.forEach(n=>{n.type==="inbound-rtp"&&(i={type:"audio",streamId:n.id,timestamp:n.timestamp,jitter:n.jitter,bytesReceived:n.bytesReceived,concealedSamples:n.concealedSamples,concealmentEvents:n.concealmentEvents,silentConcealedSamples:n.silentConcealedSamples,silentConcealmentEvents:n.silentConcealmentEvents,totalAudioEnergy:n.totalAudioEnergy,totalSamplesDuration:n.totalSamplesDuration})}),i})}}const $n=100;class wp extends Bc{constructor(e,i,n,s,r){super(e,i,P.Kind.Video,n,r),this.elementInfos=[],this.monitorReceiver=()=>m(this,void 0,void 0,function*(){if(!this.receiver){this._currentBitrate=0;return}const o=yield this.getReceiverStats();o&&this.prevStats&&this.receiver&&(this._currentBitrate=Sn(o,this.prevStats)),this.prevStats=o}),this.debouncedHandleResize=sr(()=>{this.updateDimensions()},$n),this.adaptiveStreamSettings=s}get isAdaptiveStream(){return this.adaptiveStreamSettings!==void 0}get mediaStreamTrack(){return this._mediaStreamTrack}setMuted(e){super.setMuted(e),this.attachedElements.forEach(i=>{e?Jt(this._mediaStreamTrack,i):zt(this._mediaStreamTrack,i)})}attach(e){if(e?super.attach(e):e=super.attach(),this.adaptiveStreamSettings&&this.elementInfos.find(i=>i.element===e)===void 0){const i=new _p(e);this.observeElementInfo(i)}return e}observeElementInfo(e){this.adaptiveStreamSettings&&this.elementInfos.find(i=>i===e)===void 0?(e.handleResize=()=>{this.debouncedHandleResize()},e.handleVisibilityChanged=()=>{this.updateVisibility()},this.elementInfos.push(e),e.observe(),this.debouncedHandleResize(),this.updateVisibility()):this.log.warn("visibility resize observer not triggered",this.logContext)}stopObservingElementInfo(e){if(!this.isAdaptiveStream){this.log.warn("stopObservingElementInfo ignored",this.logContext);return}const i=this.elementInfos.filter(n=>n===e);for(const n of i)n.stopObserving();this.elementInfos=this.elementInfos.filter(n=>n!==e),this.updateVisibility(),this.debouncedHandleResize()}detach(e){let i=[];if(e)return this.stopObservingElement(e),super.detach(e);i=super.detach();for(const n of i)this.stopObservingElement(n);return i}getDecoderImplementation(){var e;return(e=this.prevStats)===null||e===void 0?void 0:e.decoderImplementation}getReceiverStats(){return m(this,void 0,void 0,function*(){if(!this.receiver||!this.receiver.getStats)return;const e=yield this.receiver.getStats();let i,n="",s=new Map;return e.forEach(r=>{r.type==="inbound-rtp"?(n=r.codecId,i={type:"video",streamId:r.id,framesDecoded:r.framesDecoded,framesDropped:r.framesDropped,framesReceived:r.framesReceived,packetsReceived:r.packetsReceived,packetsLost:r.packetsLost,frameWidth:r.frameWidth,frameHeight:r.frameHeight,pliCount:r.pliCount,firCount:r.firCount,nackCount:r.nackCount,jitter:r.jitter,timestamp:r.timestamp,bytesReceived:r.bytesReceived,decoderImplementation:r.decoderImplementation}):r.type==="codec"&&s.set(r.id,r)}),i&&n!==""&&s.get(n)&&(i.mimeType=s.get(n).mimeType),i})}stopObservingElement(e){const i=this.elementInfos.filter(n=>n.element===e);for(const n of i)this.stopObservingElementInfo(n)}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return m(this,void 0,void 0,function*(){yield e.handleAppVisibilityChanged.call(this),this.isAdaptiveStream&&this.updateVisibility()})}updateVisibility(){var e,i;const n=this.elementInfos.reduce((a,c)=>Math.max(a,c.visibilityChangedAt||0),0),s=!((i=(e=this.adaptiveStreamSettings)===null||e===void 0?void 0:e.pauseVideoInBackground)!==null&&i!==void 0)||i?this.isInBackground:!1,r=this.elementInfos.some(a=>a.pictureInPicture),o=this.elementInfos.some(a=>a.visible)&&!s||r;if(this.lastVisible!==o){if(!o&&Date.now()-n<$n){Pe.setTimeout(()=>{this.updateVisibility()},$n);return}this.lastVisible=o,this.emit(D.VisibilityChanged,o,this)}}updateDimensions(){var e,i;let n=0,s=0;const r=this.getPixelDensity();for(const o of this.elementInfos){const a=o.width()*r,c=o.height()*r;a+c>n+s&&(n=a,s=c)}((e=this.lastDimensions)===null||e===void 0?void 0:e.width)===n&&((i=this.lastDimensions)===null||i===void 0?void 0:i.height)===s||(this.lastDimensions={width:n,height:s},this.emit(D.VideoDimensionsChanged,this.lastDimensions,this))}getPixelDensity(){var e;const i=(e=this.adaptiveStreamSettings)===null||e===void 0?void 0:e.pixelDensity;return i==="screen"?go():i||(go()>2?2:1)}}class _p{get visible(){return this.isPiP||this.isIntersecting}get pictureInPicture(){return this.isPiP}constructor(e,i){this.onVisibilityChanged=n=>{var s;const{target:r,isIntersecting:o}=n;r===this.element&&(this.isIntersecting=o,this.isPiP=mi(this.element),this.visibilityChangedAt=Date.now(),(s=this.handleVisibilityChanged)===null||s===void 0||s.call(this))},this.onEnterPiP=()=>{var n,s,r;(s=(n=window.documentPictureInPicture)===null||n===void 0?void 0:n.window)===null||s===void 0||s.addEventListener("pagehide",this.onLeavePiP),this.isPiP=mi(this.element),(r=this.handleVisibilityChanged)===null||r===void 0||r.call(this)},this.onLeavePiP=()=>{var n;this.isPiP=mi(this.element),(n=this.handleVisibilityChanged)===null||n===void 0||n.call(this)},this.element=e,this.isIntersecting=i??qs(e),this.isPiP=De()&&mi(e),this.visibilityChangedAt=0}width(){return this.element.clientWidth}height(){return this.element.clientHeight}observe(){var e,i,n;this.isIntersecting=qs(this.element),this.isPiP=mi(this.element),this.element.handleResize=()=>{var s;(s=this.handleResize)===null||s===void 0||s.call(this)},this.element.handleVisibilityChanged=this.onVisibilityChanged,bo().observe(this.element),vo().observe(this.element),this.element.addEventListener("enterpictureinpicture",this.onEnterPiP),this.element.addEventListener("leavepictureinpicture",this.onLeavePiP),(e=window.documentPictureInPicture)===null||e===void 0||e.addEventListener("enter",this.onEnterPiP),(n=(i=window.documentPictureInPicture)===null||i===void 0?void 0:i.window)===null||n===void 0||n.addEventListener("pagehide",this.onLeavePiP)}stopObserving(){var e,i,n,s,r;(e=bo())===null||e===void 0||e.unobserve(this.element),(i=vo())===null||i===void 0||i.unobserve(this.element),this.element.removeEventListener("enterpictureinpicture",this.onEnterPiP),this.element.removeEventListener("leavepictureinpicture",this.onLeavePiP),(n=window.documentPictureInPicture)===null||n===void 0||n.removeEventListener("enter",this.onEnterPiP),(r=(s=window.documentPictureInPicture)===null||s===void 0?void 0:s.window)===null||r===void 0||r.removeEventListener("pagehide",this.onLeavePiP)}}function mi(t){var e,i;return document.pictureInPictureElement===t?!0:!((e=window.documentPictureInPicture)===null||e===void 0)&&e.window?qs(t,(i=window.documentPictureInPicture)===null||i===void 0?void 0:i.window):!1}function qs(t,e){const i=e||window;let n=t.offsetTop,s=t.offsetLeft;const r=t.offsetWidth,o=t.offsetHeight,{hidden:a}=t,{display:c}=getComputedStyle(t);for(;t.offsetParent;)t=t.offsetParent,n+=t.offsetTop,s+=t.offsetLeft;return n<i.pageYOffset+i.innerHeight&&s<i.pageXOffset+i.innerWidth&&n+o>i.pageYOffset&&s+r>i.pageXOffset&&!a&&c!=="none"}class Ze extends Ye.EventEmitter{constructor(e,i,n,s){var r;super(),this.metadataMuted=!1,this.encryption=xe.NONE,this.log=Q,this.handleMuted=()=>{this.emit(D.Muted)},this.handleUnmuted=()=>{this.emit(D.Unmuted)},this.log=nt((r=s==null?void 0:s.loggerName)!==null&&r!==void 0?r:We.Publication),this.loggerContextCb=this.loggerContextCb,this.setMaxListeners(100),this.kind=e,this.trackSid=i,this.trackName=n,this.source=P.Source.Unknown}setTrack(e){this.track&&(this.track.off(D.Muted,this.handleMuted),this.track.off(D.Unmuted,this.handleUnmuted)),this.track=e,e&&(e.on(D.Muted,this.handleMuted),e.on(D.Unmuted,this.handleUnmuted))}get logContext(){var e;return Object.assign(Object.assign({},(e=this.loggerContextCb)===null||e===void 0?void 0:e.call(this)),J(this))}get isMuted(){return this.metadataMuted}get isEnabled(){return!0}get isSubscribed(){return this.track!==void 0}get isEncrypted(){return this.encryption!==xe.NONE}get audioTrack(){if(Je(this.track))return this.track}get videoTrack(){if(ii(this.track))return this.track}updateInfo(e){this.trackSid=e.sid,this.trackName=e.name,this.source=P.sourceFromProto(e.source),this.mimeType=e.mimeType,this.kind===P.Kind.Video&&e.width>0&&(this.dimensions={width:e.width,height:e.height},this.simulcasted=e.simulcast),this.encryption=e.encryption,this.trackInfo=e,this.log.debug("update publication info",Object.assign(Object.assign({},this.logContext),{info:e}))}}(function(t){(function(e){e.Desired="desired",e.Subscribed="subscribed",e.Unsubscribed="unsubscribed"})(t.SubscriptionStatus||(t.SubscriptionStatus={})),function(e){e.Allowed="allowed",e.NotAllowed="not_allowed"}(t.PermissionStatus||(t.PermissionStatus={}))})(Ze||(Ze={}));class Gs extends Ze{get isUpstreamPaused(){var e;return(e=this.track)===null||e===void 0?void 0:e.isUpstreamPaused}constructor(e,i,n,s){super(e,i.sid,i.name,s),this.track=void 0,this.handleTrackEnded=()=>{this.emit(D.Ended)},this.updateInfo(i),this.setTrack(n)}setTrack(e){this.track&&this.track.off(D.Ended,this.handleTrackEnded),super.setTrack(e),e&&e.on(D.Ended,this.handleTrackEnded)}get isMuted(){return this.track?this.track.isMuted:super.isMuted}get audioTrack(){return super.audioTrack}get videoTrack(){return super.videoTrack}get isLocal(){return!0}mute(){return m(this,void 0,void 0,function*(){var e;return(e=this.track)===null||e===void 0?void 0:e.mute()})}unmute(){return m(this,void 0,void 0,function*(){var e;return(e=this.track)===null||e===void 0?void 0:e.unmute()})}pauseUpstream(){return m(this,void 0,void 0,function*(){var e;yield(e=this.track)===null||e===void 0?void 0:e.pauseUpstream()})}resumeUpstream(){return m(this,void 0,void 0,function*(){var e;yield(e=this.track)===null||e===void 0?void 0:e.resumeUpstream()})}getTrackFeatures(){var e;if(Je(this.track)){const i=this.track.getSourceTrackSettings(),n=new Set;return i.autoGainControl&&n.add(Se.TF_AUTO_GAIN_CONTROL),i.echoCancellation&&n.add(Se.TF_ECHO_CANCELLATION),i.noiseSuppression&&n.add(Se.TF_NOISE_SUPPRESSION),i.channelCount&&i.channelCount>1&&n.add(Se.TF_STEREO),!((e=this.options)===null||e===void 0)&&e.dtx||n.add(Se.TF_NO_DTX),this.track.enhancedNoiseCancellation&&n.add(Se.TF_ENHANCED_NOISE_CANCELLATION),Array.from(n.values())}else return[]}}function Oi(t,e){return m(this,void 0,void 0,function*(){t??(t={});let i=!1;const{audioProcessor:n,videoProcessor:s,optionsWithoutProcessor:r}=Dc(t);let o=r.audio,a=r.video;if(n&&typeof r.audio=="object"&&(r.audio.processor=n),s&&typeof r.video=="object"&&(r.video.processor=s),t.audio&&typeof r.audio=="object"&&typeof r.audio.deviceId=="string"){const u=r.audio.deviceId;r.audio.deviceId={exact:u},i=!0,o=Object.assign(Object.assign({},r.audio),{deviceId:{ideal:u}})}if(r.video&&typeof r.video=="object"&&typeof r.video.deviceId=="string"){const u=r.video.deviceId;r.video.deviceId={exact:u},i=!0,a=Object.assign(Object.assign({},r.video),{deviceId:{ideal:u}})}(r.audio===!0||typeof r.audio=="object"&&!r.audio.deviceId)&&(r.audio={deviceId:"default"}),r.video===!0?r.video={deviceId:"default"}:typeof r.video=="object"&&!r.video.deviceId&&(r.video.deviceId="default");const c=Rc(r,Ac,xc),d=tr(c),l=navigator.mediaDevices.getUserMedia(d);r.audio&&(ke.userMediaPromiseMap.set("audioinput",l),l.catch(()=>ke.userMediaPromiseMap.delete("audioinput"))),r.video&&(ke.userMediaPromiseMap.set("videoinput",l),l.catch(()=>ke.userMediaPromiseMap.delete("videoinput")));try{const u=yield l;return yield Promise.all(u.getTracks().map(h=>m(this,void 0,void 0,function*(){const p=h.kind==="audio";let g=p?c.audio:c.video;(typeof g=="boolean"||!g)&&(g={});let f;const y=p?d.audio:d.video;typeof y!="boolean"&&(f=y);const S=h.getSettings().deviceId;f!=null&&f.deviceId&&lt(f.deviceId)!==S?f.deviceId=S:f||(f={deviceId:S});const x=op(h,f,e);return x.kind===P.Kind.Video?x.source=P.Source.Camera:x.kind===P.Kind.Audio&&(x.source=P.Source.Microphone),x.mediaStream=u,Je(x)&&n?yield x.setProcessor(n):ii(x)&&s&&(yield x.setProcessor(s)),x})))}catch(u){if(!i)throw u;return Oi(Object.assign(Object.assign({},t),{audio:o,video:a}),e)}})}function Mp(t){return m(this,void 0,void 0,function*(){return(yield Oi({audio:!1,video:!0}))[0]})}function Dp(t){return m(this,void 0,void 0,function*(){return(yield Oi({audio:!0,video:!1}))[0]})}function Op(t){return m(this,void 0,void 0,function*(){if(t===void 0&&(t={}),t.resolution===void 0&&!Tc()&&(t.resolution=yn.h1080fps30.resolution),navigator.mediaDevices.getDisplayMedia===void 0)throw new bn("getDisplayMedia not supported");const e=Mc(t),i=yield navigator.mediaDevices.getDisplayMedia(e),n=i.getVideoTracks();if(n.length===0)throw new $e("no video track found");const s=new Mi(n[0],void 0,!1);s.source=P.Source.ScreenShare;const r=[s];if(i.getAudioTracks().length>0){const o=new _i(i.getAudioTracks()[0],void 0,!1);o.source=P.Source.ScreenShareAudio,r.push(o)}return r})}var at;(function(t){t.Excellent="excellent",t.Good="good",t.Poor="poor",t.Lost="lost",t.Unknown="unknown"})(at||(at={}));function Ap(t){switch(t){case gi.EXCELLENT:return at.Excellent;case gi.GOOD:return at.Good;case gi.POOR:return at.Poor;case gi.LOST:return at.Lost;default:return at.Unknown}}class jc extends Ye.EventEmitter{get logContext(){var e,i;return Object.assign({},(i=(e=this.loggerOptions)===null||e===void 0?void 0:e.loggerContextCb)===null||i===void 0?void 0:i.call(e))}get isEncrypted(){return this.trackPublications.size>0&&Array.from(this.trackPublications.values()).every(e=>e.isEncrypted)}get isAgent(){var e;return((e=this.permissions)===null||e===void 0?void 0:e.agent)||this.kind===wi.AGENT}get isActive(){var e;return((e=this.participantInfo)===null||e===void 0?void 0:e.state)===Ht.ACTIVE}get kind(){return this._kind}get attributes(){return Object.freeze(Object.assign({},this._attributes))}constructor(e,i,n,s,r,o){let a=arguments.length>6&&arguments[6]!==void 0?arguments[6]:wi.STANDARD;var c;super(),this.audioLevel=0,this.isSpeaking=!1,this._connectionQuality=at.Unknown,this.log=Q,this.log=nt((c=o==null?void 0:o.loggerName)!==null&&c!==void 0?c:We.Participant),this.loggerOptions=o,this.setMaxListeners(100),this.sid=e,this.identity=i,this.name=n,this.metadata=s,this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.trackPublications=new Map,this._kind=a,this._attributes=r??{}}getTrackPublications(){return Array.from(this.trackPublications.values())}getTrackPublication(e){for(const[,i]of this.trackPublications)if(i.source===e)return i}getTrackPublicationByName(e){for(const[,i]of this.trackPublications)if(i.trackName===e)return i}waitUntilActive(){return this.isActive?Promise.resolve():this.activeFuture?this.activeFuture.promise:(this.activeFuture=new qt,this.once(A.Active,()=>{var e,i;(i=(e=this.activeFuture)===null||e===void 0?void 0:e.resolve)===null||i===void 0||i.call(e),this.activeFuture=void 0}),this.activeFuture.promise)}get connectionQuality(){return this._connectionQuality}get isCameraEnabled(){var e;const i=this.getTrackPublication(P.Source.Camera);return!(!((e=i==null?void 0:i.isMuted)!==null&&e!==void 0)||e)}get isMicrophoneEnabled(){var e;const i=this.getTrackPublication(P.Source.Microphone);return!(!((e=i==null?void 0:i.isMuted)!==null&&e!==void 0)||e)}get isScreenShareEnabled(){return!!this.getTrackPublication(P.Source.ScreenShare)}get isLocal(){return!1}get joinedAt(){return this.participantInfo?new Date(Number.parseInt(this.participantInfo.joinedAt.toString())*1e3):new Date}updateInfo(e){var i;return this.participantInfo&&this.participantInfo.sid===e.sid&&this.participantInfo.version>e.version?!1:(this.identity=e.identity,this.sid=e.sid,this._setName(e.name),this._setMetadata(e.metadata),this._setAttributes(e.attributes),e.state===Ht.ACTIVE&&((i=this.participantInfo)===null||i===void 0?void 0:i.state)!==Ht.ACTIVE&&this.emit(A.Active),e.permission&&this.setPermissions(e.permission),this.participantInfo=e,!0)}_setMetadata(e){const i=this.metadata!==e,n=this.metadata;this.metadata=e,i&&this.emit(A.ParticipantMetadataChanged,n)}_setName(e){const i=this.name!==e;this.name=e,i&&this.emit(A.ParticipantNameChanged,e)}_setAttributes(e){const i=Lm(this.attributes,e);this._attributes=e,Object.keys(i).length>0&&this.emit(A.AttributesChanged,i)}setPermissions(e){var i,n,s,r,o,a;const c=this.permissions,d=e.canPublish!==((i=this.permissions)===null||i===void 0?void 0:i.canPublish)||e.canSubscribe!==((n=this.permissions)===null||n===void 0?void 0:n.canSubscribe)||e.canPublishData!==((s=this.permissions)===null||s===void 0?void 0:s.canPublishData)||e.hidden!==((r=this.permissions)===null||r===void 0?void 0:r.hidden)||e.recorder!==((o=this.permissions)===null||o===void 0?void 0:o.recorder)||e.canPublishSources.length!==this.permissions.canPublishSources.length||e.canPublishSources.some((l,u)=>{var h;return l!==((h=this.permissions)===null||h===void 0?void 0:h.canPublishSources[u])})||e.canSubscribeMetrics!==((a=this.permissions)===null||a===void 0?void 0:a.canSubscribeMetrics);return this.permissions=e,d&&this.emit(A.ParticipantPermissionsChanged,c),d}setIsSpeaking(e){e!==this.isSpeaking&&(this.isSpeaking=e,e&&(this.lastSpokeAt=new Date),this.emit(A.IsSpeakingChanged,e))}setConnectionQuality(e){const i=this._connectionQuality;this._connectionQuality=Ap(e),i!==this._connectionQuality&&this.emit(A.ConnectionQualityChanged,this._connectionQuality)}setDisconnected(){var e,i;this.activeFuture&&((i=(e=this.activeFuture).reject)===null||i===void 0||i.call(e,new Error("Participant disconnected")),this.activeFuture=void 0)}setAudioContext(e){this.audioContext=e,this.audioTrackPublications.forEach(i=>Je(i.track)&&i.track.setAudioContext(e))}addTrackPublication(e){e.on(D.Muted,()=>{this.emit(A.TrackMuted,e)}),e.on(D.Unmuted,()=>{this.emit(A.TrackUnmuted,e)});const i=e;switch(i.track&&(i.track.sid=e.trackSid),this.trackPublications.set(e.trackSid,e),e.kind){case P.Kind.Audio:this.audioTrackPublications.set(e.trackSid,e);break;case P.Kind.Video:this.videoTrackPublications.set(e.trackSid,e);break}}}function xp(t){var e,i,n;if(!t.participantSid&&!t.participantIdentity)throw new Error("Invalid track permission, must provide at least one of participantIdentity and participantSid");return new Ba({participantIdentity:(e=t.participantIdentity)!==null&&e!==void 0?e:"",participantSid:(i=t.participantSid)!==null&&i!==void 0?i:"",allTracks:(n=t.allowAll)!==null&&n!==void 0?n:!1,trackSids:t.allowedTrackSids||[]})}const No=15e3;class Lp extends jc{constructor(e,i,n,s,r){super(e,i,void 0,void 0,void 0,{loggerName:s.loggerName,loggerContextCb:()=>this.engine.logContext}),this.pendingPublishing=new Set,this.pendingPublishPromises=new Map,this.participantTrackPermissions=[],this.allParticipantsAllowedToSubscribe=!0,this.encryptionType=xe.NONE,this.enabledPublishVideoCodecs=[],this.pendingAcks=new Map,this.pendingResponses=new Map,this.handleReconnecting=()=>{this.reconnectFuture||(this.reconnectFuture=new qt)},this.handleReconnected=()=>{var o,a;(a=(o=this.reconnectFuture)===null||o===void 0?void 0:o.resolve)===null||a===void 0||a.call(o),this.reconnectFuture=void 0,this.updateTrackSubscriptionPermissions()},this.handleDisconnected=()=>{var o,a,c,d,l,u;this.reconnectFuture&&(this.reconnectFuture.promise.catch(h=>this.log.warn(h.message,this.logContext)),(a=(o=this.reconnectFuture)===null||o===void 0?void 0:o.reject)===null||a===void 0||a.call(o,"Got disconnected during reconnection attempt"),this.reconnectFuture=void 0),this.signalConnectedFuture&&((d=(c=this.signalConnectedFuture).reject)===null||d===void 0||d.call(c,"Got disconnected without signal connected"),this.signalConnectedFuture=void 0),(u=(l=this.activeAgentFuture)===null||l===void 0?void 0:l.reject)===null||u===void 0||u.call(l,"Got disconnected without active agent present"),this.activeAgentFuture=void 0,this.firstActiveAgent=void 0},this.handleSignalConnected=o=>{var a,c;o.participant&&this.updateInfo(o.participant),this.signalConnectedFuture||(this.signalConnectedFuture=new qt),(c=(a=this.signalConnectedFuture).resolve)===null||c===void 0||c.call(a)},this.handleSignalRequestResponse=o=>{const{requestId:a,reason:c,message:d}=o,l=this.pendingSignalRequests.get(a);l&&(c!==Zs.OK&&l.reject(new uo(d,c)),this.pendingSignalRequests.delete(a))},this.handleDataPacket=o=>{switch(o.value.case){case"rpcResponse":let a=o.value.value,c=null,d=null;a.value.case==="payload"?c=a.value.value:a.value.case==="error"&&(d=he.fromProto(a.value.value)),this.handleIncomingRpcResponse(a.requestId,c,d);break;case"rpcAck":let l=o.value.value;this.handleIncomingRpcAck(l.requestId);break}},this.updateTrackSubscriptionPermissions=()=>{this.log.debug("updating track subscription permissions",Object.assign(Object.assign({},this.logContext),{allParticipantsAllowed:this.allParticipantsAllowedToSubscribe,participantTrackPermissions:this.participantTrackPermissions})),this.engine.client.sendUpdateSubscriptionPermissions(this.allParticipantsAllowedToSubscribe,this.participantTrackPermissions.map(o=>xp(o)))},this.onTrackUnmuted=o=>{this.onTrackMuted(o,o.isUpstreamPaused)},this.onTrackMuted=(o,a)=>{if(a===void 0&&(a=!0),!o.sid){this.log.error("could not update mute status for unpublished track",Object.assign(Object.assign({},this.logContext),J(o)));return}this.engine.updateMuteStatus(o.sid,a)},this.onTrackUpstreamPaused=o=>{this.log.debug("upstream paused",Object.assign(Object.assign({},this.logContext),J(o))),this.onTrackMuted(o,!0)},this.onTrackUpstreamResumed=o=>{this.log.debug("upstream resumed",Object.assign(Object.assign({},this.logContext),J(o))),this.onTrackMuted(o,o.isMuted)},this.onTrackFeatureUpdate=o=>{const a=this.audioTrackPublications.get(o.sid);if(!a){this.log.warn("Could not update local audio track settings, missing publication for track ".concat(o.sid),this.logContext);return}this.engine.client.sendUpdateLocalAudioTrack(a.trackSid,a.getTrackFeatures())},this.handleSubscribedQualityUpdate=o=>m(this,void 0,void 0,function*(){var a,c,d,l,u;if(!(!((u=this.roomOptions)===null||u===void 0)&&u.dynacast))return;const h=this.videoTrackPublications.get(o.trackSid);if(!h){this.log.warn("received subscribed quality update for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:o.trackSid}));return}if(!h.videoTrack)return;const p=yield h.videoTrack.setPublishingCodecs(o.subscribedCodecs);try{for(var g=!0,f=tt(p),y;y=yield f.next(),a=y.done,!a;g=!0){l=y.value,g=!1;const S=l;pm(S)&&(this.log.debug("publish ".concat(S," for ").concat(h.videoTrack.sid),Object.assign(Object.assign({},this.logContext),J(h))),yield this.publishAdditionalCodecForTrack(h.videoTrack,S,h.options))}}catch(S){c={error:S}}finally{try{!g&&!a&&(d=f.return)&&(yield d.call(f))}finally{if(c)throw c.error}}}),this.handleLocalTrackUnpublished=o=>{const a=this.trackPublications.get(o.trackSid);if(!a){this.log.warn("received unpublished event for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:o.trackSid}));return}this.unpublishTrack(a.track)},this.handleTrackEnded=o=>m(this,void 0,void 0,function*(){if(o.source===P.Source.ScreenShare||o.source===P.Source.ScreenShareAudio)this.log.debug("unpublishing local track due to TrackEnded",Object.assign(Object.assign({},this.logContext),J(o))),this.unpublishTrack(o);else if(o.isUserProvided)yield o.mute();else if(Xe(o)||rt(o))try{if(De())try{const a=yield navigator==null?void 0:navigator.permissions.query({name:o.source===P.Source.Camera?"camera":"microphone"});if(a&&a.state==="denied")throw this.log.warn("user has revoked access to ".concat(o.source),Object.assign(Object.assign({},this.logContext),J(o))),a.onchange=()=>{a.state!=="denied"&&(o.isMuted||o.restartTrack(),a.onchange=null)},new Error("GetUserMedia Permission denied")}catch{}o.isMuted||(this.log.debug("track ended, attempting to use a different device",Object.assign(Object.assign({},this.logContext),J(o))),Xe(o)?yield o.restartTrack({deviceId:"default"}):yield o.restartTrack())}catch{this.log.warn("could not restart track, muting instead",Object.assign(Object.assign({},this.logContext),J(o))),yield o.mute()}}),this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.trackPublications=new Map,this.engine=n,this.roomOptions=s,this.setupEngine(n),this.activeDeviceMap=new Map([["audioinput","default"],["videoinput","default"],["audiooutput","default"]]),this.pendingSignalRequests=new Map,this.rpcHandlers=r}get lastCameraError(){return this.cameraError}get lastMicrophoneError(){return this.microphoneError}get isE2EEEnabled(){return this.encryptionType!==xe.NONE}getTrackPublication(e){const i=super.getTrackPublication(e);if(i)return i}getTrackPublicationByName(e){const i=super.getTrackPublicationByName(e);if(i)return i}setupEngine(e){this.engine=e,this.engine.on(O.RemoteMute,(i,n)=>{const s=this.trackPublications.get(i);!s||!s.track||(n?s.mute():s.unmute())}),this.engine.on(O.Connected,this.handleReconnected).on(O.SignalConnected,this.handleSignalConnected).on(O.SignalRestarted,this.handleReconnected).on(O.SignalResumed,this.handleReconnected).on(O.Restarting,this.handleReconnecting).on(O.Resuming,this.handleReconnecting).on(O.LocalTrackUnpublished,this.handleLocalTrackUnpublished).on(O.SubscribedQualityUpdate,this.handleSubscribedQualityUpdate).on(O.Disconnected,this.handleDisconnected).on(O.SignalRequestResponse,this.handleSignalRequestResponse).on(O.DataPacketReceived,this.handleDataPacket)}setMetadata(e){return m(this,void 0,void 0,function*(){yield this.requestMetadataUpdate({metadata:e})})}setName(e){return m(this,void 0,void 0,function*(){yield this.requestMetadataUpdate({name:e})})}setAttributes(e){return m(this,void 0,void 0,function*(){yield this.requestMetadataUpdate({attributes:e})})}requestMetadataUpdate(e){return m(this,arguments,void 0,function(i){var n=this;let{metadata:s,name:r,attributes:o}=i;return function*(){return new Promise((a,c)=>m(n,void 0,void 0,function*(){var d,l;try{let u=!1;const h=yield this.engine.client.sendUpdateLocalMetadata((d=s??this.metadata)!==null&&d!==void 0?d:"",(l=r??this.name)!==null&&l!==void 0?l:"",o),p=performance.now();for(this.pendingSignalRequests.set(h,{resolve:a,reject:g=>{c(g),u=!0},values:{name:r,metadata:s,attributes:o}});performance.now()-p<5e3&&!u;){if((!r||this.name===r)&&(!s||this.metadata===s)&&(!o||Object.entries(o).every(g=>{let[f,y]=g;return this.attributes[f]===y||y===""&&!this.attributes[f]}))){this.pendingSignalRequests.delete(h),a();return}yield Ae(50)}c(new uo("Request to update local metadata timed out","TimeoutError"))}catch(u){u instanceof Error&&c(u)}}))}()})}setCameraEnabled(e,i,n){return this.setTrackEnabled(P.Source.Camera,e,i,n)}setMicrophoneEnabled(e,i,n){return this.setTrackEnabled(P.Source.Microphone,e,i,n)}setScreenShareEnabled(e,i,n){return this.setTrackEnabled(P.Source.ScreenShare,e,i,n)}setPermissions(e){const i=this.permissions,n=super.setPermissions(e);return n&&i&&this.emit(A.ParticipantPermissionsChanged,i),n}setE2EEEnabled(e){return m(this,void 0,void 0,function*(){this.encryptionType=e?xe.GCM:xe.NONE,yield this.republishAllTracks(void 0,!1)})}setTrackEnabled(e,i,n,s){return m(this,void 0,void 0,function*(){var r,o;this.log.debug("setTrackEnabled",Object.assign(Object.assign({},this.logContext),{source:e,enabled:i})),this.republishPromise&&(yield this.republishPromise);let a=this.getTrackPublication(e);if(i)if(a)yield a.unmute();else{let c;if(this.pendingPublishing.has(e)){const d=yield this.waitForPendingPublicationOfSource(e);return d||this.log.info("waiting for pending publication promise timed out",Object.assign(Object.assign({},this.logContext),{source:e})),yield d==null?void 0:d.unmute(),d}this.pendingPublishing.add(e);try{switch(e){case P.Source.Camera:c=yield this.createTracks({video:(r=n)!==null&&r!==void 0?r:!0});break;case P.Source.Microphone:c=yield this.createTracks({audio:(o=n)!==null&&o!==void 0?o:!0});break;case P.Source.ScreenShare:c=yield this.createScreenTracks(Object.assign({},n));break;default:throw new $e(e)}}catch(d){throw c==null||c.forEach(l=>{l.stop()}),d instanceof Error&&this.emit(A.MediaDevicesError,d,Fs(e)),this.pendingPublishing.delete(e),d}for(const d of c)e===P.Source.Microphone&&Je(d)&&(s!=null&&s.preConnectBuffer)&&(this.log.info("starting preconnect buffer for microphone",Object.assign({},this.logContext)),d.startPreConnectBuffer());try{const d=[];for(const u of c)this.log.info("publishing track",Object.assign(Object.assign({},this.logContext),J(u))),d.push(this.publishTrack(u,s));[a]=yield Promise.all(d)}catch(d){throw c==null||c.forEach(l=>{l.stop()}),d}finally{this.pendingPublishing.delete(e)}}else if(!(a!=null&&a.track)&&this.pendingPublishing.has(e)&&(a=yield this.waitForPendingPublicationOfSource(e),a||this.log.info("waiting for pending publication promise timed out",Object.assign(Object.assign({},this.logContext),{source:e}))),a&&a.track)if(e===P.Source.ScreenShare){a=yield this.unpublishTrack(a.track);const c=this.getTrackPublication(P.Source.ScreenShareAudio);c&&c.track&&this.unpublishTrack(c.track)}else yield a.mute();return a})}enableCameraAndMicrophone(){return m(this,void 0,void 0,function*(){if(!(this.pendingPublishing.has(P.Source.Camera)||this.pendingPublishing.has(P.Source.Microphone))){this.pendingPublishing.add(P.Source.Camera),this.pendingPublishing.add(P.Source.Microphone);try{const e=yield this.createTracks({audio:!0,video:!0});yield Promise.all(e.map(i=>this.publishTrack(i)))}finally{this.pendingPublishing.delete(P.Source.Camera),this.pendingPublishing.delete(P.Source.Microphone)}}})}createTracks(e){return m(this,void 0,void 0,function*(){var i,n;e??(e={});const s=Rc(e,(i=this.roomOptions)===null||i===void 0?void 0:i.audioCaptureDefaults,(n=this.roomOptions)===null||n===void 0?void 0:n.videoCaptureDefaults);try{return(yield Oi(s,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext})).map(a=>(Je(a)&&(this.microphoneError=void 0,a.setAudioContext(this.audioContext),a.source=P.Source.Microphone,this.emit(A.AudioStreamAcquired)),ii(a)&&(this.cameraError=void 0,a.source=P.Source.Camera),a))}catch(r){throw r instanceof Error&&(e.audio&&(this.microphoneError=r),e.video&&(this.cameraError=r)),r}})}createScreenTracks(e){return m(this,void 0,void 0,function*(){if(e===void 0&&(e={}),navigator.mediaDevices.getDisplayMedia===void 0)throw new bn("getDisplayMedia not supported");e.resolution===void 0&&!Tc()&&(e.resolution=yn.h1080fps30.resolution);const i=Mc(e),n=yield navigator.mediaDevices.getDisplayMedia(i),s=n.getVideoTracks();if(s.length===0)throw new $e("no video track found");const r=new Mi(s[0],void 0,!1,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});r.source=P.Source.ScreenShare,e.contentHint&&(r.mediaStreamTrack.contentHint=e.contentHint);const o=[r];if(n.getAudioTracks().length>0){this.emit(A.AudioStreamAcquired);const a=new _i(n.getAudioTracks()[0],void 0,!1,this.audioContext,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});a.source=P.Source.ScreenShareAudio,o.push(a)}return o})}publishTrack(e,i){return m(this,void 0,void 0,function*(){return this.publishOrRepublishTrack(e,i)})}publishOrRepublishTrack(e,i){return m(this,arguments,void 0,function(n,s){var r=this;let o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return function*(){var a,c,d,l;Xe(n)&&n.setAudioContext(r.audioContext),yield(a=r.reconnectFuture)===null||a===void 0?void 0:a.promise,r.republishPromise&&!o&&(yield r.republishPromise),Qt(n)&&r.pendingPublishPromises.has(n)&&(yield r.pendingPublishPromises.get(n));let u;if(n instanceof MediaStreamTrack)u=n.getConstraints();else{u=n.constraints;let S;switch(n.source){case P.Source.Microphone:S="audioinput";break;case P.Source.Camera:S="videoinput"}S&&r.activeDeviceMap.has(S)&&(u=Object.assign(Object.assign({},u),{deviceId:r.activeDeviceMap.get(S)}))}if(n instanceof MediaStreamTrack)switch(n.kind){case"audio":n=new _i(n,u,!0,r.audioContext,{loggerName:r.roomOptions.loggerName,loggerContextCb:()=>r.logContext});break;case"video":n=new Mi(n,u,!0,{loggerName:r.roomOptions.loggerName,loggerContextCb:()=>r.logContext});break;default:throw new $e("unsupported MediaStreamTrack kind ".concat(n.kind))}else n.updateLoggerOptions({loggerName:r.roomOptions.loggerName,loggerContextCb:()=>r.logContext});let h;if(r.trackPublications.forEach(S=>{S.track&&S.track===n&&(h=S)}),h)return r.log.warn("track has already been published, skipping",Object.assign(Object.assign({},r.logContext),J(h))),h;const p="channelCount"in n.mediaStreamTrack.getSettings()&&n.mediaStreamTrack.getSettings().channelCount===2||n.mediaStreamTrack.getConstraints().channelCount===2,g=(c=s==null?void 0:s.forceStereo)!==null&&c!==void 0?c:p;g&&(s||(s={}),s.dtx===void 0&&r.log.info("Opus DTX will be disabled for stereo tracks by default. Enable them explicitly to make it work.",Object.assign(Object.assign({},r.logContext),J(n))),s.red===void 0&&r.log.info("Opus RED will be disabled for stereo tracks by default. Enable them explicitly to make it work."),(d=s.dtx)!==null&&d!==void 0||(s.dtx=!1),(l=s.red)!==null&&l!==void 0||(s.red=!1));const f=Object.assign(Object.assign({},r.roomOptions.publishDefaults),s);!km()&&r.roomOptions.e2ee&&(r.log.info("End-to-end encryption is set up, simulcast publishing will be disabled on Safari versions and iOS browsers running iOS < v17.2",Object.assign({},r.logContext)),f.simulcast=!1),f.source&&(n.source=f.source);const y=new Promise((S,x)=>m(r,void 0,void 0,function*(){try{if(this.engine.client.currentState!==oe.CONNECTED){this.log.debug("deferring track publication until signal is connected",Object.assign(Object.assign({},this.logContext),{track:J(n)}));const M=setTimeout(()=>{x(new lo("publishing rejected as engine not connected within timeout",408))},15e3);yield this.waitUntilEngineConnected(),clearTimeout(M);const b=yield this.publish(n,f,g);S(b)}else try{const M=yield this.publish(n,f,g);S(M)}catch(M){x(M)}}catch(M){x(M)}}));r.pendingPublishPromises.set(n,y);try{return yield y}catch(S){throw S}finally{r.pendingPublishPromises.delete(n)}}()})}waitUntilEngineConnected(){return this.signalConnectedFuture||(this.signalConnectedFuture=new qt),this.signalConnectedFuture.promise}hasPermissionsToPublish(e){if(!this.permissions)return this.log.warn("no permissions present for publishing track",Object.assign(Object.assign({},this.logContext),J(e))),!1;const{canPublish:i,canPublishSources:n}=this.permissions;return i&&(n.length===0||n.map(s=>Nm(s)).includes(e.source))?!0:(this.log.warn("insufficient permissions to publish",Object.assign(Object.assign({},this.logContext),J(e))),!1)}publish(e,i,n){return m(this,void 0,void 0,function*(){var s,r,o,a,c,d,l,u,h,p;if(!this.hasPermissionsToPublish(e))throw new lo("failed to publish track, insufficient permissions",403);Array.from(this.trackPublications.values()).find(V=>Qt(e)&&V.source===e.source)&&e.source!==P.Source.Unknown&&this.log.info("publishing a second track with the same source: ".concat(e.source),Object.assign(Object.assign({},this.logContext),J(e))),i.stopMicTrackOnMute&&Je(e)&&(e.stopOnMute=!0),e.source===P.Source.ScreenShare&&ei()&&(i.simulcast=!1),i.videoCodec==="av1"&&!vm()&&(i.videoCodec=void 0),i.videoCodec==="vp9"&&!bm()&&(i.videoCodec=void 0),i.videoCodec===void 0&&(i.videoCodec=Vs),this.enabledPublishVideoCodecs.length>0&&(this.enabledPublishVideoCodecs.some(V=>i.videoCodec===Zi(V.mime))||(i.videoCodec=Zi(this.enabledPublishVideoCodecs[0].mime)));const f=i.videoCodec;e.on(D.Muted,this.onTrackMuted),e.on(D.Unmuted,this.onTrackUnmuted),e.on(D.Ended,this.handleTrackEnded),e.on(D.UpstreamPaused,this.onTrackUpstreamPaused),e.on(D.UpstreamResumed,this.onTrackUpstreamResumed),e.on(D.AudioTrackFeatureUpdate,this.onTrackFeatureUpdate);const y=[],S=!(!((s=i.dtx)!==null&&s!==void 0)||s),x=e.getSourceTrackSettings();x.autoGainControl&&y.push(Se.TF_AUTO_GAIN_CONTROL),x.echoCancellation&&y.push(Se.TF_ECHO_CANCELLATION),x.noiseSuppression&&y.push(Se.TF_NOISE_SUPPRESSION),x.channelCount&&x.channelCount>1&&y.push(Se.TF_STEREO),S&&y.push(Se.TF_NO_DTX),Xe(e)&&e.hasPreConnectBuffer&&y.push(Se.TF_PRECONNECT_BUFFER);const M=new ys({cid:e.mediaStreamTrack.id,name:i.name,type:P.kindToProto(e.kind),muted:e.isMuted,source:P.sourceToProto(e.source),disableDtx:S,encryption:this.encryptionType,stereo:n,disableRed:this.isE2EEEnabled||!(!((r=i.red)!==null&&r!==void 0)||r),stream:i==null?void 0:i.stream,backupCodecPolicy:i==null?void 0:i.backupCodecPolicy,audioFeatures:y});let b;if(e.kind===P.Kind.Video){let V={width:0,height:0};try{V=yield e.waitForDimensions()}catch{const q=(a=(o=this.roomOptions.videoCaptureDefaults)===null||o===void 0?void 0:o.resolution)!==null&&a!==void 0?a:Dt.h720.resolution;V={width:q.width,height:q.height},this.log.error("could not determine track dimensions, using defaults",Object.assign(Object.assign(Object.assign({},this.logContext),J(e)),{dims:V}))}M.width=V.width,M.height=V.height,rt(e)&&(it(f)&&(e.source===P.Source.ScreenShare&&(i.scalabilityMode="L1T3","contentHint"in e.mediaStreamTrack&&(e.mediaStreamTrack.contentHint="motion",this.log.info("forcing contentHint to motion for screenshare with SVC codecs",Object.assign(Object.assign({},this.logContext),J(e))))),i.scalabilityMode=(c=i.scalabilityMode)!==null&&c!==void 0?c:"L3T3_KEY"),M.simulcastCodecs=[new bs({codec:f,cid:e.mediaStreamTrack.id})],i.backupCodec===!0&&(i.backupCodec={codec:Vs}),i.backupCodec&&f!==i.backupCodec.codec&&M.encryption===xe.NONE&&(this.roomOptions.dynacast||(this.roomOptions.dynacast=!0),M.simulcastCodecs.push(new bs({codec:i.backupCodec.codec,cid:""})))),b=js(e.source===P.Source.ScreenShare,M.width,M.height,i),M.layers=Ao(M.width,M.height,b,it(i.videoCodec))}else e.kind===P.Kind.Audio&&(b=[{maxBitrate:(d=i.audioPreset)===null||d===void 0?void 0:d.maxBitrate,priority:(u=(l=i.audioPreset)===null||l===void 0?void 0:l.priority)!==null&&u!==void 0?u:"high",networkPriority:(p=(h=i.audioPreset)===null||h===void 0?void 0:h.priority)!==null&&p!==void 0?p:"high"}]);if(!this.engine||this.engine.isClosed)throw new fe("cannot publish track when not connected");const k=()=>m(this,void 0,void 0,function*(){var V,B,q;if(!this.engine.pcManager)throw new fe("pcManager is not ready");if(e.sender=yield this.engine.createSender(e,i,b),rt(e)&&((V=i.degradationPreference)!==null&&V!==void 0||(i.degradationPreference=gp(e)),e.setDegradationPreference(i.degradationPreference)),b)if(ei()&&e.kind===P.Kind.Audio){let X;for(const ye of this.engine.pcManager.publisher.getTransceivers())if(ye.sender===e.sender){X=ye;break}X&&this.engine.pcManager.publisher.setTrackCodecBitrate({transceiver:X,codec:"opus",maxbr:!((B=b[0])===null||B===void 0)&&B.maxBitrate?b[0].maxBitrate/1e3:0})}else e.codec&&it(e.codec)&&(!((q=b[0])===null||q===void 0)&&q.maxBitrate)&&this.engine.pcManager.publisher.setTrackCodecBitrate({cid:M.cid,codec:e.codec,maxbr:b[0].maxBitrate/1e3});yield this.engine.negotiate()});let _;const j=new Promise((V,B)=>m(this,void 0,void 0,function*(){var q;try{_=yield this.engine.addTrack(M),V(_)}catch(X){e.sender&&(!((q=this.engine.pcManager)===null||q===void 0)&&q.publisher)&&(this.engine.pcManager.publisher.removeTrack(e.sender),yield this.engine.negotiate().catch(ye=>{this.log.error("failed to negotiate after removing track due to failed add track request",Object.assign(Object.assign(Object.assign({},this.logContext),J(e)),{error:ye}))})),B(X)}}));if(this.enabledPublishVideoCodecs.length>0)_=(yield Promise.all([j,k()]))[0];else{_=yield j;let V;if(_.codecs.forEach(B=>{V===void 0&&(V=B.mimeType)}),V&&e.kind===P.Kind.Video){const B=Zi(V);B!==f&&(this.log.debug("falling back to server selected codec",Object.assign(Object.assign(Object.assign({},this.logContext),J(e)),{codec:B})),i.videoCodec=B,b=js(e.source===P.Source.ScreenShare,M.width,M.height,i))}yield k()}const z=new Gs(e.kind,_,e,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});if(z.options=i,e.sid=_.sid,this.log.debug("publishing ".concat(e.kind," with encodings"),Object.assign(Object.assign({},this.logContext),{encodings:b,trackInfo:_})),rt(e)?e.startMonitor(this.engine.client):Xe(e)&&e.startMonitor(),this.addTrackPublication(z),this.emit(A.LocalTrackPublished,z),Xe(e)&&_.audioFeatures.includes(Se.TF_PRECONNECT_BUFFER)){const V=e.getPreConnectBuffer();this.on(A.LocalTrackSubscribed,B=>{if(B.trackSid===_.sid){if(!e.hasPreConnectBuffer){this.log.warn("subscribe event came to late, buffer already closed",this.logContext);return}this.log.debug("finished recording preconnect buffer",Object.assign(Object.assign({},this.logContext),J(e))),e.stopPreConnectBuffer()}}),V&&new Promise((q,X)=>m(this,void 0,void 0,function*(){var ye,Ne,xt,bt,ni,si;try{this.log.debug("waiting for agent",Object.assign(Object.assign({},this.logContext),J(e)));const ai=setTimeout(()=>{X(new Error("agent not active within 10 seconds"))},1e4),kn=yield this.waitUntilActiveAgentPresent();clearTimeout(ai),this.log.debug("sending preconnect buffer",Object.assign(Object.assign({},this.logContext),J(e)));const Lt=yield this.streamBytes({name:"preconnect-buffer",mimeType:"audio/opus",topic:"lk.agent.pre-connect-audio-buffer",destinationIdentities:[kn.identity],attributes:{trackId:z.trackSid,sampleRate:String((ni=x.sampleRate)!==null&&ni!==void 0?ni:"48000"),channels:String((si=x.channelCount)!==null&&si!==void 0?si:"1")}});try{for(var ri=!0,yt=tt(V),oi;oi=yield yt.next(),ye=oi.done,!ye;ri=!0){bt=oi.value,ri=!1;const ci=bt;yield Lt.write(ci)}}catch(ci){Ne={error:ci}}finally{try{!ri&&!ye&&(xt=yt.return)&&(yield xt.call(yt))}finally{if(Ne)throw Ne.error}}yield Lt.close(),q()}catch(ai){X(ai)}})).then(()=>{this.log.debug("preconnect buffer sent successfully",Object.assign(Object.assign({},this.logContext),J(e)))}).catch(q=>{this.log.error("error sending preconnect buffer",Object.assign(Object.assign(Object.assign({},this.logContext),J(e)),{error:q}))})}return z})}get isLocal(){return!0}publishAdditionalCodecForTrack(e,i,n){return m(this,void 0,void 0,function*(){var s;if(this.encryptionType!==xe.NONE)return;let r;if(this.trackPublications.forEach(p=>{p.track&&p.track===e&&(r=p)}),!r)throw new $e("track is not published");if(!rt(e))throw new $e("track is not a video track");const o=Object.assign(Object.assign({},(s=this.roomOptions)===null||s===void 0?void 0:s.publishDefaults),n),a=mp(e,i,o);if(!a){this.log.info("backup codec has been disabled, ignoring request to add additional codec for track",Object.assign(Object.assign({},this.logContext),J(e)));return}const c=e.addSimulcastTrack(i,a);if(!c)return;const d=new ys({cid:c.mediaStreamTrack.id,type:P.kindToProto(e.kind),muted:e.isMuted,source:P.sourceToProto(e.source),sid:e.sid,simulcastCodecs:[{codec:o.videoCodec,cid:c.mediaStreamTrack.id}]});if(d.layers=Ao(d.width,d.height,a),!this.engine||this.engine.isClosed)throw new fe("cannot publish track when not connected");const l=()=>m(this,void 0,void 0,function*(){yield this.engine.createSimulcastSender(e,c,o,a),yield this.engine.negotiate()}),h=(yield Promise.all([this.engine.addTrack(d),l()]))[0];this.log.debug("published ".concat(i," for track ").concat(e.sid),Object.assign(Object.assign({},this.logContext),{encodings:a,trackInfo:h}))})}unpublishTrack(e,i){return m(this,void 0,void 0,function*(){var n,s;if(Qt(e)){const d=this.pendingPublishPromises.get(e);d&&(this.log.info("awaiting publish promise before attempting to unpublish",Object.assign(Object.assign({},this.logContext),J(e))),yield d)}const r=this.getPublicationForTrack(e),o=r?J(r):void 0;if(this.log.debug("unpublishing track",Object.assign(Object.assign({},this.logContext),o)),!r||!r.track){this.log.warn("track was not unpublished because no publication was found",Object.assign(Object.assign({},this.logContext),o));return}e=r.track,e.off(D.Muted,this.onTrackMuted),e.off(D.Unmuted,this.onTrackUnmuted),e.off(D.Ended,this.handleTrackEnded),e.off(D.UpstreamPaused,this.onTrackUpstreamPaused),e.off(D.UpstreamResumed,this.onTrackUpstreamResumed),e.off(D.AudioTrackFeatureUpdate,this.onTrackFeatureUpdate),i===void 0&&(i=(s=(n=this.roomOptions)===null||n===void 0?void 0:n.stopLocalTrackOnUnpublish)!==null&&s!==void 0?s:!0),i?e.stop():e.stopMonitor();let a=!1;const c=e.sender;if(e.sender=void 0,this.engine.pcManager&&this.engine.pcManager.currentState<le.FAILED&&c)try{for(const d of this.engine.pcManager.publisher.getTransceivers())d.sender===c&&(d.direction="inactive",a=!0);if(this.engine.removeTrack(c)&&(a=!0),rt(e)){for(const[,d]of e.simulcastCodecs)d.sender&&(this.engine.removeTrack(d.sender)&&(a=!0),d.sender=void 0);e.simulcastCodecs.clear()}}catch(d){this.log.warn("failed to unpublish track",Object.assign(Object.assign(Object.assign({},this.logContext),o),{error:d}))}switch(this.trackPublications.delete(r.trackSid),r.kind){case P.Kind.Audio:this.audioTrackPublications.delete(r.trackSid);break;case P.Kind.Video:this.videoTrackPublications.delete(r.trackSid);break}return this.emit(A.LocalTrackUnpublished,r),r.setTrack(void 0),a&&(yield this.engine.negotiate()),r})}unpublishTracks(e){return m(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>this.unpublishTrack(n)))).filter(n=>!!n)})}republishAllTracks(e){return m(this,arguments,void 0,function(i){var n=this;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return function*(){n.republishPromise&&(yield n.republishPromise),n.republishPromise=new Promise((r,o)=>m(n,void 0,void 0,function*(){try{const a=[];this.trackPublications.forEach(c=>{c.track&&(i&&(c.options=Object.assign(Object.assign({},c.options),i)),a.push(c))}),yield Promise.all(a.map(c=>m(this,void 0,void 0,function*(){const d=c.track;yield this.unpublishTrack(d,!1),s&&!d.isMuted&&d.source!==P.Source.ScreenShare&&d.source!==P.Source.ScreenShareAudio&&(Xe(d)||rt(d))&&!d.isUserProvided&&(this.log.debug("restarting existing track",Object.assign(Object.assign({},this.logContext),{track:c.trackSid})),yield d.restartTrack()),yield this.publishOrRepublishTrack(d,c.options,!0)}))),r()}catch(a){o(a)}finally{this.republishPromise=void 0}})),yield n.republishPromise}()})}publishData(e){return m(this,arguments,void 0,function(i){var n=this;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return function*(){const r=s.reliable?ee.RELIABLE:ee.LOSSY,o=s.destinationIdentities,a=s.topic,c=new Ie({kind:r,value:{case:"user",value:new Ca({participantIdentity:n.identity,payload:i,destinationIdentities:o,topic:a})}});yield n.engine.sendDataPacket(c,r)}()})}publishDtmf(e,i){return m(this,void 0,void 0,function*(){const n=new Ie({kind:ee.RELIABLE,value:{case:"sipDtmf",value:new Ta({code:e,digit:i})}});yield this.engine.sendDataPacket(n,ee.RELIABLE)})}sendChatMessage(e,i){return m(this,void 0,void 0,function*(){const n={id:crypto.randomUUID(),message:e,timestamp:Date.now(),attachedFiles:i==null?void 0:i.attachments},s=new Ie({value:{case:"chatMessage",value:new hs(Object.assign(Object.assign({},n),{timestamp:de.parse(n.timestamp)}))}});return yield this.engine.sendDataPacket(s,ee.RELIABLE),this.emit(A.ChatMessage,n),n})}editChatMessage(e,i){return m(this,void 0,void 0,function*(){const n=Object.assign(Object.assign({},i),{message:e,editTimestamp:Date.now()}),s=new Ie({value:{case:"chatMessage",value:new hs(Object.assign(Object.assign({},n),{timestamp:de.parse(n.timestamp),editTimestamp:de.parse(n.editTimestamp)}))}});return yield this.engine.sendDataPacket(s,ee.RELIABLE),this.emit(A.ChatMessage,n),n})}sendText(e,i){return m(this,void 0,void 0,function*(){var n;const s=crypto.randomUUID(),o=new TextEncoder().encode(e).byteLength,a=(n=i==null?void 0:i.attachments)===null||n===void 0?void 0:n.map(()=>crypto.randomUUID()),c=new Array(a?a.length+1:1).fill(0),d=(u,h)=>{var p;c[h]=u;const g=c.reduce((f,y)=>f+y,0);(p=i==null?void 0:i.onProgress)===null||p===void 0||p.call(i,g)},l=yield this.streamText({streamId:s,totalSize:o,destinationIdentities:i==null?void 0:i.destinationIdentities,topic:i==null?void 0:i.topic,attachedStreamIds:a,attributes:i==null?void 0:i.attributes});return yield l.write(e),d(1,0),yield l.close(),i!=null&&i.attachments&&a&&(yield Promise.all(i.attachments.map((u,h)=>m(this,void 0,void 0,function*(){return this._sendFile(a[h],u,{topic:i.topic,mimeType:u.type,onProgress:p=>{d(p,h+1)}})})))),l.info})}streamText(e){return m(this,void 0,void 0,function*(){var i,n;const s=(i=e==null?void 0:e.streamId)!==null&&i!==void 0?i:crypto.randomUUID(),r={id:s,mimeType:"text/plain",timestamp:Date.now(),topic:(n=e==null?void 0:e.topic)!==null&&n!==void 0?n:"",size:e==null?void 0:e.totalSize,attributes:e==null?void 0:e.attributes},o=new ps({streamId:s,mimeType:r.mimeType,topic:r.topic,timestamp:Ct(r.timestamp),totalLength:Ct(e==null?void 0:e.totalSize),attributes:r.attributes,contentHeader:{case:"textHeader",value:new Aa({version:e==null?void 0:e.version,attachedStreamIds:e==null?void 0:e.attachedStreamIds,replyToStreamId:e==null?void 0:e.replyToStreamId,operationType:(e==null?void 0:e.type)==="update"?ms.UPDATE:ms.CREATE})}}),a=e==null?void 0:e.destinationIdentities,c=new Ie({destinationIdentities:a,value:{case:"streamHeader",value:o}});yield this.engine.sendDataPacket(c,ee.RELIABLE);let d=0;const l=this,u=new WritableStream({write(g){return m(this,void 0,void 0,function*(){for(const f of Dm(g,No)){yield l.engine.waitForBufferStatusLow(ee.RELIABLE);const y=new fs({content:f,streamId:s,chunkIndex:Ct(d)}),S=new Ie({destinationIdentities:a,value:{case:"streamChunk",value:y}});yield l.engine.sendDataPacket(S,ee.RELIABLE),d+=1}})},close(){return m(this,void 0,void 0,function*(){const g=new gs({streamId:s}),f=new Ie({destinationIdentities:a,value:{case:"streamTrailer",value:g}});yield l.engine.sendDataPacket(f,ee.RELIABLE)})},abort(g){console.log("Sink error:",g)}});let h=()=>m(this,void 0,void 0,function*(){yield p.close()});l.engine.once(O.Closing,h);const p=new Ep(u,r,()=>this.engine.off(O.Closing,h));return p})}sendFile(e,i){return m(this,void 0,void 0,function*(){const n=crypto.randomUUID();return yield this._sendFile(n,e,i),{id:n}})}_sendFile(e,i,n){return m(this,void 0,void 0,function*(){var s;const r=yield this.streamBytes({streamId:e,totalSize:i.size,name:i.name,mimeType:(s=n==null?void 0:n.mimeType)!==null&&s!==void 0?s:i.type,topic:n==null?void 0:n.topic,destinationIdentities:n==null?void 0:n.destinationIdentities}),o=i.stream().getReader();for(;;){const{done:a,value:c}=yield o.read();if(a)break;yield r.write(c)}return yield r.close(),r.info})}streamBytes(e){return m(this,void 0,void 0,function*(){var i,n,s,r,o;const a=(i=e==null?void 0:e.streamId)!==null&&i!==void 0?i:crypto.randomUUID(),c=e==null?void 0:e.destinationIdentities,d={id:a,mimeType:(n=e==null?void 0:e.mimeType)!==null&&n!==void 0?n:"application/octet-stream",topic:(s=e==null?void 0:e.topic)!==null&&s!==void 0?s:"",timestamp:Date.now(),attributes:e==null?void 0:e.attributes,size:e==null?void 0:e.totalSize,name:(r=e==null?void 0:e.name)!==null&&r!==void 0?r:"unknown"},l=new ps({totalLength:Ct((o=d.size)!==null&&o!==void 0?o:0),mimeType:d.mimeType,streamId:a,topic:d.topic,timestamp:Ct(Date.now()),attributes:d.attributes,contentHeader:{case:"byteHeader",value:new xa({name:d.name})}}),u=new Ie({destinationIdentities:c,value:{case:"streamHeader",value:l}});yield this.engine.sendDataPacket(u,ee.RELIABLE);let h=0;const p=new Me,g=this.engine,f=this.log,y=new WritableStream({write(x){return m(this,void 0,void 0,function*(){const M=yield p.lock();let b=0;try{for(;b<x.byteLength;){const k=x.slice(b,b+No);yield g.waitForBufferStatusLow(ee.RELIABLE);const _=new Ie({destinationIdentities:c,value:{case:"streamChunk",value:new fs({content:k,streamId:a,chunkIndex:Ct(h)})}});yield g.sendDataPacket(_,ee.RELIABLE),h+=1,b+=k.byteLength}}finally{M()}})},close(){return m(this,void 0,void 0,function*(){const x=new gs({streamId:a}),M=new Ie({destinationIdentities:c,value:{case:"streamTrailer",value:x}});yield g.sendDataPacket(M,ee.RELIABLE)})},abort(x){f.error("Sink error:",x)}});return new Ip(y,d)})}performRpc(e){return m(this,arguments,void 0,function(i){var n=this;let{destinationIdentity:s,method:r,payload:o,responseTimeout:a=1e4}=i;return function*(){return new Promise((d,l)=>m(n,void 0,void 0,function*(){var u,h,p,g;if(or(o)>Lc){l(he.builtIn("REQUEST_PAYLOAD_TOO_LARGE"));return}if(!((h=(u=this.engine.latestJoinResponse)===null||u===void 0?void 0:u.serverInfo)===null||h===void 0)&&h.version&&ht((g=(p=this.engine.latestJoinResponse)===null||p===void 0?void 0:p.serverInfo)===null||g===void 0?void 0:g.version,"1.8.0")<0){l(he.builtIn("UNSUPPORTED_SERVER"));return}const f=crypto.randomUUID();yield this.publishRpcRequest(s,f,r,o,a-2e3);const y=setTimeout(()=>{this.pendingAcks.delete(f),l(he.builtIn("CONNECTION_TIMEOUT")),this.pendingResponses.delete(f),clearTimeout(S)},2e3);this.pendingAcks.set(f,{resolve:()=>{clearTimeout(y)},participantIdentity:s});const S=setTimeout(()=>{this.pendingResponses.delete(f),l(he.builtIn("RESPONSE_TIMEOUT"))},a);this.pendingResponses.set(f,{resolve:(x,M)=>{clearTimeout(S),this.pendingAcks.has(f)&&(console.warn("RPC response received before ack",f),this.pendingAcks.delete(f),clearTimeout(y)),M?l(M):d(x??"")},participantIdentity:s})}))}()})}registerRpcMethod(e,i){this.rpcHandlers.has(e)&&this.log.warn("you're overriding the RPC handler for method ".concat(e,", in the future this will throw an error")),this.rpcHandlers.set(e,i)}unregisterRpcMethod(e){this.rpcHandlers.delete(e)}setTrackSubscriptionPermissions(e){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];this.participantTrackPermissions=i,this.allParticipantsAllowedToSubscribe=e,this.engine.client.isDisconnected||this.updateTrackSubscriptionPermissions()}handleIncomingRpcAck(e){const i=this.pendingAcks.get(e);i?(i.resolve(),this.pendingAcks.delete(e)):console.error("Ack received for unexpected RPC request",e)}handleIncomingRpcResponse(e,i,n){const s=this.pendingResponses.get(e);s?(s.resolve(i,n),this.pendingResponses.delete(e)):console.error("Response received for unexpected RPC request",e)}publishRpcRequest(e,i,n,s,r){return m(this,void 0,void 0,function*(){const o=new Ie({destinationIdentities:[e],kind:ee.RELIABLE,value:{case:"rpcRequest",value:new Pa({id:i,method:n,payload:s,responseTimeoutMs:r,version:1})}});yield this.engine.sendDataPacket(o,ee.RELIABLE)})}handleParticipantDisconnected(e){for(const[i,{participantIdentity:n}]of this.pendingAcks)n===e&&this.pendingAcks.delete(i);for(const[i,{participantIdentity:n,resolve:s}]of this.pendingResponses)n===e&&(s(null,he.builtIn("RECIPIENT_DISCONNECTED")),this.pendingResponses.delete(i))}setEnabledPublishCodecs(e){this.enabledPublishVideoCodecs=e.filter(i=>i.mime.split("/")[0].toLowerCase()==="video")}updateInfo(e){return super.updateInfo(e)?(e.tracks.forEach(i=>{var n,s;const r=this.trackPublications.get(i.sid);if(r){const o=r.isMuted||((s=(n=r.track)===null||n===void 0?void 0:n.isUpstreamPaused)!==null&&s!==void 0?s:!1);o!==i.muted&&(this.log.debug("updating server mute state after reconcile",Object.assign(Object.assign(Object.assign({},this.logContext),J(r)),{mutedOnServer:o})),this.engine.client.sendMuteTrack(i.sid,o))}}),!0):!1}setActiveAgent(e){var i,n,s,r;this.firstActiveAgent=e,e&&!this.firstActiveAgent&&(this.firstActiveAgent=e),e?(n=(i=this.activeAgentFuture)===null||i===void 0?void 0:i.resolve)===null||n===void 0||n.call(i,e):(r=(s=this.activeAgentFuture)===null||s===void 0?void 0:s.reject)===null||r===void 0||r.call(s,"Agent disconnected"),this.activeAgentFuture=void 0}waitUntilActiveAgentPresent(){return this.firstActiveAgent?Promise.resolve(this.firstActiveAgent):(this.activeAgentFuture||(this.activeAgentFuture=new qt),this.activeAgentFuture.promise)}getPublicationForTrack(e){let i;return this.trackPublications.forEach(n=>{const s=n.track;s&&(e instanceof MediaStreamTrack?(Xe(s)||rt(s))&&s.mediaStreamTrack===e&&(i=n):e===s&&(i=n))}),i}waitForPendingPublicationOfSource(e){return m(this,void 0,void 0,function*(){const n=Date.now();for(;Date.now()<n+1e4;){const s=Array.from(this.pendingPublishPromises.entries()).find(r=>{let[o]=r;return o.source===e});if(s)return s[1];yield Ae(20)}})}}class Np extends Ze{constructor(e,i,n,s){super(e,i.sid,i.name,s),this.track=void 0,this.allowed=!0,this.disabled=!1,this.currentVideoQuality=Le.HIGH,this.handleEnded=r=>{this.setTrack(void 0),this.emit(D.Ended,r)},this.handleVisibilityChange=r=>{this.log.debug("adaptivestream video visibility ".concat(this.trackSid,", visible=").concat(r),this.logContext),this.disabled=!r,this.emitTrackUpdate()},this.handleVideoDimensionsChange=r=>{this.log.debug("adaptivestream video dimensions ".concat(r.width,"x").concat(r.height),this.logContext),this.videoDimensions=r,this.emitTrackUpdate()},this.subscribed=n,this.updateInfo(i)}setSubscribed(e){const i=this.subscriptionStatus,n=this.permissionStatus;this.subscribed=e,e&&(this.allowed=!0);const s=new fn({trackSids:[this.trackSid],subscribe:this.subscribed,participantTracks:[new wa({participantSid:"",trackSids:[this.trackSid]})]});this.emit(D.UpdateSubscription,s),this.emitSubscriptionUpdateIfChanged(i),this.emitPermissionUpdateIfChanged(n)}get subscriptionStatus(){return this.subscribed===!1?Ze.SubscriptionStatus.Unsubscribed:super.isSubscribed?Ze.SubscriptionStatus.Subscribed:Ze.SubscriptionStatus.Desired}get permissionStatus(){return this.allowed?Ze.PermissionStatus.Allowed:Ze.PermissionStatus.NotAllowed}get isSubscribed(){return this.subscribed===!1?!1:super.isSubscribed}get isDesired(){return this.subscribed!==!1}get isEnabled(){return!this.disabled}get isLocal(){return!1}setEnabled(e){!this.isManualOperationAllowed()||this.disabled===!e||(this.disabled=!e,this.emitTrackUpdate())}setVideoQuality(e){!this.isManualOperationAllowed()||this.currentVideoQuality===e||(this.currentVideoQuality=e,this.videoDimensions=void 0,this.emitTrackUpdate())}setVideoDimensions(e){var i,n;this.isManualOperationAllowed()&&(((i=this.videoDimensions)===null||i===void 0?void 0:i.width)===e.width&&((n=this.videoDimensions)===null||n===void 0?void 0:n.height)===e.height||(jn(this.track)&&(this.videoDimensions=e),this.currentVideoQuality=void 0,this.emitTrackUpdate()))}setVideoFPS(e){this.isManualOperationAllowed()&&jn(this.track)&&this.fps!==e&&(this.fps=e,this.emitTrackUpdate())}get videoQuality(){return this.currentVideoQuality}setTrack(e){const i=this.subscriptionStatus,n=this.permissionStatus,s=this.track;s!==e&&(s&&(s.off(D.VideoDimensionsChanged,this.handleVideoDimensionsChange),s.off(D.VisibilityChanged,this.handleVisibilityChange),s.off(D.Ended,this.handleEnded),s.detach(),s.stopMonitor(),this.emit(D.Unsubscribed,s)),super.setTrack(e),e&&(e.sid=this.trackSid,e.on(D.VideoDimensionsChanged,this.handleVideoDimensionsChange),e.on(D.VisibilityChanged,this.handleVisibilityChange),e.on(D.Ended,this.handleEnded),this.emit(D.Subscribed,e)),this.emitPermissionUpdateIfChanged(n),this.emitSubscriptionUpdateIfChanged(i))}setAllowed(e){const i=this.subscriptionStatus,n=this.permissionStatus;this.allowed=e,this.emitPermissionUpdateIfChanged(n),this.emitSubscriptionUpdateIfChanged(i)}setSubscriptionError(e){this.emit(D.SubscriptionFailed,e)}updateInfo(e){super.updateInfo(e);const i=this.metadataMuted;this.metadataMuted=e.muted,this.track?this.track.setMuted(e.muted):i!==e.muted&&this.emit(e.muted?D.Muted:D.Unmuted)}emitSubscriptionUpdateIfChanged(e){const i=this.subscriptionStatus;e!==i&&this.emit(D.SubscriptionStatusChanged,i,e)}emitPermissionUpdateIfChanged(e){this.permissionStatus!==e&&this.emit(D.SubscriptionPermissionChanged,this.permissionStatus,e)}isManualOperationAllowed(){return this.kind===P.Kind.Video&&this.isAdaptiveStream?(this.log.warn("adaptive stream is enabled, cannot change video track settings",this.logContext),!1):this.isDesired?!0:(this.log.warn("cannot update track settings when not subscribed",this.logContext),!1)}get isAdaptiveStream(){return jn(this.track)&&this.track.isAdaptiveStream}emitTrackUpdate(){const e=new La({trackSids:[this.trackSid],disabled:this.disabled,fps:this.fps});this.videoDimensions?(e.width=Math.ceil(this.videoDimensions.width),e.height=Math.ceil(this.videoDimensions.height)):this.currentVideoQuality!==void 0?e.quality=this.currentVideoQuality:e.quality=Le.HIGH,this.emit(D.UpdateSettings,e)}}class un extends jc{static fromParticipantInfo(e,i,n){return new un(e,i.sid,i.identity,i.name,i.metadata,i.attributes,n,i.kind)}get logContext(){return Object.assign(Object.assign({},super.logContext),{rpID:this.sid,remoteParticipant:this.identity})}constructor(e,i,n,s,r,o,a){let c=arguments.length>7&&arguments[7]!==void 0?arguments[7]:wi.STANDARD;super(i,n||"",s,r,o,a,c),this.signalClient=e,this.trackPublications=new Map,this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.volumeMap=new Map}addTrackPublication(e){super.addTrackPublication(e),e.on(D.UpdateSettings,i=>{this.log.debug("send update settings",Object.assign(Object.assign({},this.logContext),J(e))),this.signalClient.sendUpdateTrackSettings(i)}),e.on(D.UpdateSubscription,i=>{i.participantTracks.forEach(n=>{n.participantSid=this.sid}),this.signalClient.sendUpdateSubscription(i)}),e.on(D.SubscriptionPermissionChanged,i=>{this.emit(A.TrackSubscriptionPermissionChanged,e,i)}),e.on(D.SubscriptionStatusChanged,i=>{this.emit(A.TrackSubscriptionStatusChanged,e,i)}),e.on(D.Subscribed,i=>{this.emit(A.TrackSubscribed,i,e)}),e.on(D.Unsubscribed,i=>{this.emit(A.TrackUnsubscribed,i,e)}),e.on(D.SubscriptionFailed,i=>{this.emit(A.TrackSubscriptionFailed,e.trackSid,i)})}getTrackPublication(e){const i=super.getTrackPublication(e);if(i)return i}getTrackPublicationByName(e){const i=super.getTrackPublicationByName(e);if(i)return i}setVolume(e){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:P.Source.Microphone;this.volumeMap.set(i,e);const n=this.getTrackPublication(i);n&&n.track&&n.track.setVolume(e)}getVolume(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:P.Source.Microphone;const i=this.getTrackPublication(e);return i&&i.track?i.track.getVolume():this.volumeMap.get(e)}addSubscribedMediaTrack(e,i,n,s,r,o){let a=this.getTrackPublicationBySid(i);if(a||i.startsWith("TR")||this.trackPublications.forEach(l=>{!a&&e.kind===l.kind.toString()&&(a=l)}),!a){if(o===0){this.log.error("could not find published track",Object.assign(Object.assign({},this.logContext),{trackSid:i})),this.emit(A.TrackSubscriptionFailed,i);return}o===void 0&&(o=20),setTimeout(()=>{this.addSubscribedMediaTrack(e,i,n,s,r,o-1)},150);return}if(e.readyState==="ended"){this.log.error("unable to subscribe because MediaStreamTrack is ended. Do not call MediaStreamTrack.stop()",Object.assign(Object.assign({},this.logContext),J(a))),this.emit(A.TrackSubscriptionFailed,i);return}const c=e.kind==="video";let d;return c?d=new wp(e,i,s,r):d=new Rp(e,i,s,this.audioContext,this.audioOutput),d.source=a.source,d.isMuted=a.isMuted,d.setMediaStream(n),d.start(),a.setTrack(d),this.volumeMap.has(a.source)&&Ns(d)&&Je(d)&&d.setVolume(this.volumeMap.get(a.source)),a}get hasMetadata(){return!!this.participantInfo}getTrackPublicationBySid(e){return this.trackPublications.get(e)}updateInfo(e){if(!super.updateInfo(e))return!1;const i=new Map,n=new Map;return e.tracks.forEach(s=>{var r,o;let a=this.getTrackPublicationBySid(s.sid);if(a)a.updateInfo(s);else{const c=P.kindFromProto(s.type);if(!c)return;a=new Np(c,s,(r=this.signalClient.connectOptions)===null||r===void 0?void 0:r.autoSubscribe,{loggerContextCb:()=>this.logContext,loggerName:(o=this.loggerOptions)===null||o===void 0?void 0:o.loggerName}),a.updateInfo(s),n.set(s.sid,a);const d=Array.from(this.trackPublications.values()).find(l=>l.source===(a==null?void 0:a.source));d&&a.source!==P.Source.Unknown&&this.log.debug("received a second track publication for ".concat(this.identity," with the same source: ").concat(a.source),Object.assign(Object.assign({},this.logContext),{oldTrack:J(d),newTrack:J(a)})),this.addTrackPublication(a)}i.set(s.sid,a)}),this.trackPublications.forEach(s=>{i.has(s.trackSid)||(this.log.trace("detected removed track on remote participant, unpublishing",Object.assign(Object.assign({},this.logContext),J(s))),this.unpublishTrack(s.trackSid,!0))}),n.forEach(s=>{this.emit(A.TrackPublished,s)}),!0}unpublishTrack(e,i){const n=this.trackPublications.get(e);if(!n)return;const{track:s}=n;switch(s&&(s.stop(),n.setTrack(void 0)),this.trackPublications.delete(e),n.kind){case P.Kind.Audio:this.audioTrackPublications.delete(e);break;case P.Kind.Video:this.videoTrackPublications.delete(e);break}i&&this.emit(A.TrackUnpublished,n)}setAudioOutput(e){return m(this,void 0,void 0,function*(){this.audioOutput=e;const i=[];this.audioTrackPublications.forEach(n=>{var s;Je(n.track)&&Ns(n.track)&&i.push(n.track.setSinkId((s=e.deviceId)!==null&&s!==void 0?s:"default"))}),yield Promise.all(i)})}emit(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];return this.log.trace("participant event",Object.assign(Object.assign({},this.logContext),{event:e,args:n})),super.emit(e,...n)}}var te;(function(t){t.Disconnected="disconnected",t.Connecting="connecting",t.Connected="connected",t.Reconnecting="reconnecting",t.SignalReconnecting="signalReconnecting"})(te||(te={}));const Up=4*1e3;class ti extends Ye.EventEmitter{constructor(e){var i,n,s,r;if(super(),i=this,this.state=te.Disconnected,this.activeSpeakers=[],this.isE2EEEnabled=!1,this.audioEnabled=!0,this.isVideoPlaybackBlocked=!1,this.log=Q,this.bufferedEvents=[],this.isResuming=!1,this.byteStreamControllers=new Map,this.textStreamControllers=new Map,this.byteStreamHandlers=new Map,this.textStreamHandlers=new Map,this.rpcHandlers=new Map,this.connect=(o,a,c)=>m(this,void 0,void 0,function*(){var d;if(!ym())throw Qe()?Error("WebRTC isn't detected, have you called registerGlobals?"):Error("LiveKit doesn't seem to be supported on this browser. Try to update your browser and make sure no browser extensions are disabling webRTC.");const l=yield this.disconnectLock.lock();if(this.state===te.Connected)return this.log.info("already connected to room ".concat(this.name),this.logContext),l(),Promise.resolve();if(this.connectFuture)return l(),this.connectFuture.promise;this.setAndEmitConnectionState(te.Connecting),((d=this.regionUrlProvider)===null||d===void 0?void 0:d.getServerUrl().toString())!==o&&(this.regionUrl=void 0,this.regionUrlProvider=void 0),xs(new URL(o))&&(this.regionUrlProvider===void 0?this.regionUrlProvider=new zs(o,a):this.regionUrlProvider.updateToken(a),this.regionUrlProvider.fetchRegionSettings().then(p=>{var g;(g=this.regionUrlProvider)===null||g===void 0||g.setServerReportedRegions(p)}).catch(p=>{this.log.warn("could not fetch region settings",Object.assign(Object.assign({},this.logContext),{error:p}))}));const u=(p,g,f)=>m(this,void 0,void 0,function*(){var y,S;this.abortController&&this.abortController.abort();const x=new AbortController;this.abortController=x,l==null||l();try{yield this.attemptConnection(f??o,a,c,x),this.abortController=void 0,p()}catch(M){if(this.regionUrlProvider&&M instanceof re&&M.reason!==Y.Cancelled&&M.reason!==Y.NotAllowed){let b=null;try{b=yield this.regionUrlProvider.getNextBestRegionUrl((y=this.abortController)===null||y===void 0?void 0:y.signal)}catch(k){if(k instanceof re&&(k.status===401||k.reason===Y.Cancelled)){this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),g(k);return}}b&&!(!((S=this.abortController)===null||S===void 0)&&S.signal.aborted)?(this.log.info("Initial connection failed with ConnectionError: ".concat(M.message,". Retrying with another region: ").concat(b),this.logContext),this.recreateEngine(),yield u(p,g,b)):(this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,So(M)),g(M))}else{let b=Ge.UNKNOWN_REASON;M instanceof re&&(b=So(M)),this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,b),g(M)}}}),h=this.regionUrl;return this.regionUrl=void 0,this.connectFuture=new qt((p,g)=>{u(p,g,h)},()=>{this.clearConnectionFutures()}),this.connectFuture.promise}),this.connectSignal=(o,a,c,d,l,u)=>m(this,void 0,void 0,function*(){var h,p,g;const f=yield c.join(o,a,{autoSubscribe:d.autoSubscribe,adaptiveStream:typeof l.adaptiveStream=="object"?!0:l.adaptiveStream,maxRetries:d.maxRetries,e2eeEnabled:!!this.e2eeManager,websocketTimeout:d.websocketTimeout},u.signal);let y=f.serverInfo;if(y||(y={version:f.serverVersion,region:f.serverRegion}),this.serverInfo=y,this.log.debug("connected to Livekit Server ".concat(Object.entries(y).map(S=>{let[x,M]=S;return"".concat(x,": ").concat(M)}).join(", ")),{room:(h=f.room)===null||h===void 0?void 0:h.name,roomSid:(p=f.room)===null||p===void 0?void 0:p.sid,identity:(g=f.participant)===null||g===void 0?void 0:g.identity}),!y.version)throw new sm("unknown server version");return y.version==="0.15.1"&&this.options.dynacast&&(this.log.debug("disabling dynacast due to server version",this.logContext),l.dynacast=!1),f}),this.applyJoinResponse=o=>{const a=o.participant;if(this.localParticipant.sid=a.sid,this.localParticipant.identity=a.identity,this.localParticipant.setEnabledPublishCodecs(o.enabledPublishCodecs),this.options.e2ee&&this.e2eeManager)try{this.e2eeManager.setSifTrailer(o.sifTrailer)}catch(c){this.log.error(c instanceof Error?c.message:"Could not set SifTrailer",Object.assign(Object.assign({},this.logContext),{error:c}))}this.handleParticipantUpdates([a,...o.otherParticipants]),o.room&&this.handleRoomUpdate(o.room)},this.attemptConnection=(o,a,c,d)=>m(this,void 0,void 0,function*(){var l,u;this.state===te.Reconnecting||this.isResuming||!((l=this.engine)===null||l===void 0)&&l.pendingReconnect?(this.log.info("Reconnection attempt replaced by new connection attempt",this.logContext),this.recreateEngine()):this.maybeCreateEngine(),!((u=this.regionUrlProvider)===null||u===void 0)&&u.isCloud()&&this.engine.setRegionUrlProvider(this.regionUrlProvider),this.acquireAudioContext(),this.connOptions=Object.assign(Object.assign({},rr),c),this.connOptions.rtcConfig&&(this.engine.rtcConfig=this.connOptions.rtcConfig),this.connOptions.peerConnectionTimeout&&(this.engine.peerConnectionTimeout=this.connOptions.peerConnectionTimeout);try{const h=yield this.connectSignal(o,a,this.engine,this.connOptions,this.options,d);this.applyJoinResponse(h),this.setupLocalParticipantEvents(),this.emit(I.SignalConnected)}catch(h){yield this.engine.close(),this.recreateEngine();const p=new re("could not establish signal connection",Y.ServerUnreachable);throw h instanceof Error&&(p.message="".concat(p.message,": ").concat(h.message)),h instanceof re&&(p.reason=h.reason,p.status=h.status),this.log.debug("error trying to establish signal connection",Object.assign(Object.assign({},this.logContext),{error:h})),p}if(d.signal.aborted)throw yield this.engine.close(),this.recreateEngine(),new re("Connection attempt aborted",Y.Cancelled);try{yield this.engine.waitForPCInitialConnection(this.connOptions.peerConnectionTimeout,d)}catch(h){throw yield this.engine.close(),this.recreateEngine(),h}De()&&this.options.disconnectOnPageLeave&&(window.addEventListener("pagehide",this.onPageLeave),window.addEventListener("beforeunload",this.onPageLeave)),De()&&document.addEventListener("freeze",this.onPageLeave),this.setAndEmitConnectionState(te.Connected),this.emit(I.Connected),this.registerConnectionReconcile()}),this.disconnect=function(){for(var o=arguments.length,a=new Array(o),c=0;c<o;c++)a[c]=arguments[c];return m(i,[...a],void 0,function(){var d=this;let l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return function*(){var u,h,p,g;const f=yield d.disconnectLock.lock();try{if(d.state===te.Disconnected){d.log.debug("already disconnected",d.logContext);return}d.log.info("disconnect from room",Object.assign({},d.logContext)),(d.state===te.Connecting||d.state===te.Reconnecting||d.isResuming)&&(d.log.warn("abort connection attempt",d.logContext),(u=d.abortController)===null||u===void 0||u.abort(),(p=(h=d.connectFuture)===null||h===void 0?void 0:h.reject)===null||p===void 0||p.call(h,new re("Client initiated disconnect",Y.Cancelled)),d.connectFuture=void 0),!((g=d.engine)===null||g===void 0)&&g.client.isDisconnected||(yield d.engine.client.sendLeave()),d.engine&&(yield d.engine.close()),d.handleDisconnect(l,Ge.CLIENT_INITIATED),d.engine=void 0}finally{f()}}()})},this.onPageLeave=()=>m(this,void 0,void 0,function*(){this.log.info("Page leave detected, disconnecting",this.logContext),yield this.disconnect()}),this.startAudio=()=>m(this,void 0,void 0,function*(){const o=[],a=Oe();if(a&&a.os==="iOS"){const c="livekit-dummy-audio-el";let d=document.getElementById(c);if(!d){d=document.createElement("audio"),d.id=c,d.autoplay=!0,d.hidden=!0;const l=Bn();l.enabled=!0;const u=new MediaStream([l]);d.srcObject=u,document.addEventListener("visibilitychange",()=>{d&&(d.srcObject=document.hidden?null:u,document.hidden||(this.log.debug("page visible again, triggering startAudio to resume playback and update playback status",this.logContext),this.startAudio()))}),document.body.append(d),this.once(I.Disconnected,()=>{d==null||d.remove(),d=null})}o.push(d)}this.remoteParticipants.forEach(c=>{c.audioTrackPublications.forEach(d=>{d.track&&d.track.attachedElements.forEach(l=>{o.push(l)})})});try{yield Promise.all([this.acquireAudioContext(),...o.map(c=>(c.muted=!1,c.play()))]),this.handleAudioPlaybackStarted()}catch(c){throw this.handleAudioPlaybackFailed(c),c}}),this.startVideo=()=>m(this,void 0,void 0,function*(){const o=[];for(const a of this.remoteParticipants.values())a.videoTrackPublications.forEach(c=>{var d;(d=c.track)===null||d===void 0||d.attachedElements.forEach(l=>{o.includes(l)||o.push(l)})});yield Promise.all(o.map(a=>a.play())).then(()=>{this.handleVideoPlaybackStarted()}).catch(a=>{a.name==="NotAllowedError"?this.handleVideoPlaybackFailed():this.log.warn("Resuming video playback failed, make sure you call `startVideo` directly in a user gesture handler",this.logContext)})}),this.handleRestarting=()=>{this.clearConnectionReconcile(),this.isResuming=!1;for(const o of this.remoteParticipants.values())this.handleParticipantDisconnected(o.identity,o);this.setAndEmitConnectionState(te.Reconnecting)&&this.emit(I.Reconnecting)},this.handleSignalRestarted=o=>m(this,void 0,void 0,function*(){this.log.debug("signal reconnected to server, region ".concat(o.serverRegion),Object.assign(Object.assign({},this.logContext),{region:o.serverRegion})),this.bufferedEvents=[],this.applyJoinResponse(o);try{yield this.localParticipant.republishAllTracks(void 0,!0)}catch(a){this.log.error("error trying to re-publish tracks after reconnection",Object.assign(Object.assign({},this.logContext),{error:a}))}try{yield this.engine.waitForRestarted(),this.log.debug("fully reconnected to server",Object.assign(Object.assign({},this.logContext),{region:o.serverRegion}))}catch{return}this.setAndEmitConnectionState(te.Connected),this.emit(I.Reconnected),this.registerConnectionReconcile(),this.emitBufferedEvents()}),this.handleParticipantUpdates=o=>{o.forEach(a=>{var c;if(a.identity===this.localParticipant.identity){this.localParticipant.updateInfo(a);return}a.identity===""&&(a.identity=(c=this.sidToIdentity.get(a.sid))!==null&&c!==void 0?c:"");let d=this.remoteParticipants.get(a.identity);a.state===Ht.DISCONNECTED?this.handleParticipantDisconnected(a.identity,d):d=this.getOrCreateParticipant(a.identity,a)})},this.handleActiveSpeakersUpdate=o=>{const a=[],c={};o.forEach(d=>{if(c[d.sid]=!0,d.sid===this.localParticipant.sid)this.localParticipant.audioLevel=d.level,this.localParticipant.setIsSpeaking(!0),a.push(this.localParticipant);else{const l=this.getRemoteParticipantBySid(d.sid);l&&(l.audioLevel=d.level,l.setIsSpeaking(!0),a.push(l))}}),c[this.localParticipant.sid]||(this.localParticipant.audioLevel=0,this.localParticipant.setIsSpeaking(!1)),this.remoteParticipants.forEach(d=>{c[d.sid]||(d.audioLevel=0,d.setIsSpeaking(!1))}),this.activeSpeakers=a,this.emitWhenConnected(I.ActiveSpeakersChanged,a)},this.handleSpeakersChanged=o=>{const a=new Map;this.activeSpeakers.forEach(d=>{const l=this.remoteParticipants.get(d.identity);l&&l.sid!==d.sid||a.set(d.sid,d)}),o.forEach(d=>{let l=this.getRemoteParticipantBySid(d.sid);d.sid===this.localParticipant.sid&&(l=this.localParticipant),l&&(l.audioLevel=d.level,l.setIsSpeaking(d.active),d.active?a.set(d.sid,l):a.delete(d.sid))});const c=Array.from(a.values());c.sort((d,l)=>l.audioLevel-d.audioLevel),this.activeSpeakers=c,this.emitWhenConnected(I.ActiveSpeakersChanged,c)},this.handleStreamStateUpdate=o=>{o.streamStates.forEach(a=>{const c=this.getRemoteParticipantBySid(a.participantSid);if(!c)return;const d=c.getTrackPublicationBySid(a.trackSid);if(!d||!d.track)return;const l=P.streamStateFromProto(a.state);l!==d.track.streamState&&(d.track.streamState=l,c.emit(A.TrackStreamStateChanged,d,d.track.streamState),this.emitWhenConnected(I.TrackStreamStateChanged,d,d.track.streamState,c))})},this.handleSubscriptionPermissionUpdate=o=>{const a=this.getRemoteParticipantBySid(o.participantSid);if(!a)return;const c=a.getTrackPublicationBySid(o.trackSid);c&&c.setAllowed(o.allowed)},this.handleSubscriptionError=o=>{const a=Array.from(this.remoteParticipants.values()).find(d=>d.trackPublications.has(o.trackSid));if(!a)return;const c=a.getTrackPublicationBySid(o.trackSid);c&&c.setSubscriptionError(o.err)},this.handleDataPacket=o=>{const a=this.remoteParticipants.get(o.participantIdentity);if(o.value.case==="user")this.handleUserPacket(a,o.value.value,o.kind);else if(o.value.case==="transcription")this.handleTranscription(a,o.value.value);else if(o.value.case==="sipDtmf")this.handleSipDtmf(a,o.value.value);else if(o.value.case==="chatMessage")this.handleChatMessage(a,o.value.value);else if(o.value.case==="metrics")this.handleMetrics(o.value.value,a);else if(o.value.case==="streamHeader")this.handleStreamHeader(o.value.value,o.participantIdentity);else if(o.value.case==="streamChunk")this.handleStreamChunk(o.value.value);else if(o.value.case==="streamTrailer")this.handleStreamTrailer(o.value.value);else if(o.value.case==="rpcRequest"){const c=o.value.value;this.handleIncomingRpcRequest(o.participantIdentity,c.id,c.method,c.payload,c.responseTimeoutMs,c.version)}},this.handleUserPacket=(o,a,c)=>{this.emit(I.DataReceived,a.payload,o,c,a.topic),o==null||o.emit(A.DataReceived,a.payload,c)},this.handleSipDtmf=(o,a)=>{this.emit(I.SipDTMFReceived,a,o),o==null||o.emit(A.SipDTMFReceived,a)},this.bufferedSegments=new Map,this.handleTranscription=(o,a)=>{const c=a.transcribedParticipantIdentity===this.localParticipant.identity?this.localParticipant:this.getParticipantByIdentity(a.transcribedParticipantIdentity),d=c==null?void 0:c.trackPublications.get(a.trackId),l=Rm(a,this.transcriptionReceivedTimes);d==null||d.emit(D.TranscriptionReceived,l),c==null||c.emit(A.TranscriptionReceived,l,d),this.emit(I.TranscriptionReceived,l,c,d)},this.handleChatMessage=(o,a)=>{const c=wm(a);this.emit(I.ChatMessage,c,o)},this.handleMetrics=(o,a)=>{this.emit(I.MetricsReceived,o,a)},this.handleAudioPlaybackStarted=()=>{this.canPlaybackAudio||(this.audioEnabled=!0,this.emit(I.AudioPlaybackStatusChanged,!0))},this.handleAudioPlaybackFailed=o=>{this.log.warn("could not playback audio",Object.assign(Object.assign({},this.logContext),{error:o})),this.canPlaybackAudio&&(this.audioEnabled=!1,this.emit(I.AudioPlaybackStatusChanged,!1))},this.handleVideoPlaybackStarted=()=>{this.isVideoPlaybackBlocked&&(this.isVideoPlaybackBlocked=!1,this.emit(I.VideoPlaybackStatusChanged,!0))},this.handleVideoPlaybackFailed=()=>{this.isVideoPlaybackBlocked||(this.isVideoPlaybackBlocked=!0,this.emit(I.VideoPlaybackStatusChanged,!1))},this.handleDeviceChange=()=>m(this,void 0,void 0,function*(){var o;((o=Oe())===null||o===void 0?void 0:o.os)!=="iOS"&&(yield this.selectDefaultDevices()),this.emit(I.MediaDevicesChanged)}),this.handleRoomUpdate=o=>{const a=this.roomInfo;this.roomInfo=o,a&&a.metadata!==o.metadata&&this.emitWhenConnected(I.RoomMetadataChanged,o.metadata),(a==null?void 0:a.activeRecording)!==o.activeRecording&&this.emitWhenConnected(I.RecordingStatusChanged,o.activeRecording)},this.handleConnectionQualityUpdate=o=>{o.updates.forEach(a=>{if(a.participantSid===this.localParticipant.sid){this.localParticipant.setConnectionQuality(a.quality);return}const c=this.getRemoteParticipantBySid(a.participantSid);c&&c.setConnectionQuality(a.quality)})},this.onLocalParticipantMetadataChanged=o=>{this.emit(I.ParticipantMetadataChanged,o,this.localParticipant)},this.onLocalParticipantNameChanged=o=>{this.emit(I.ParticipantNameChanged,o,this.localParticipant)},this.onLocalAttributesChanged=o=>{this.emit(I.ParticipantAttributesChanged,o,this.localParticipant)},this.onLocalTrackMuted=o=>{this.emit(I.TrackMuted,o,this.localParticipant)},this.onLocalTrackUnmuted=o=>{this.emit(I.TrackUnmuted,o,this.localParticipant)},this.onTrackProcessorUpdate=o=>{var a;(a=o==null?void 0:o.onPublish)===null||a===void 0||a.call(o,this)},this.onLocalTrackPublished=o=>m(this,void 0,void 0,function*(){var a,c,d,l,u,h;(a=o.track)===null||a===void 0||a.on(D.TrackProcessorUpdate,this.onTrackProcessorUpdate),(c=o.track)===null||c===void 0||c.on(D.Restarted,this.onLocalTrackRestarted),(u=(l=(d=o.track)===null||d===void 0?void 0:d.getProcessor())===null||l===void 0?void 0:l.onPublish)===null||u===void 0||u.call(l,this),this.emit(I.LocalTrackPublished,o,this.localParticipant),Xe(o.track)&&(yield o.track.checkForSilence())&&this.emit(I.LocalAudioSilenceDetected,o);const p=yield(h=o.track)===null||h===void 0?void 0:h.getDeviceId(!1),g=Fs(o.source);g&&p&&p!==this.localParticipant.activeDeviceMap.get(g)&&(this.localParticipant.activeDeviceMap.set(g,p),this.emit(I.ActiveDeviceChanged,g,p))}),this.onLocalTrackUnpublished=o=>{var a,c;(a=o.track)===null||a===void 0||a.off(D.TrackProcessorUpdate,this.onTrackProcessorUpdate),(c=o.track)===null||c===void 0||c.off(D.Restarted,this.onLocalTrackRestarted),this.emit(I.LocalTrackUnpublished,o,this.localParticipant)},this.onLocalTrackRestarted=o=>m(this,void 0,void 0,function*(){const a=yield o.getDeviceId(!1),c=Fs(o.source);c&&a&&a!==this.localParticipant.activeDeviceMap.get(c)&&(this.log.debug("local track restarted, setting ".concat(c," ").concat(a," active"),this.logContext),this.localParticipant.activeDeviceMap.set(c,a),this.emit(I.ActiveDeviceChanged,c,a))}),this.onLocalConnectionQualityChanged=o=>{this.emit(I.ConnectionQualityChanged,o,this.localParticipant)},this.onMediaDevicesError=(o,a)=>{this.emit(I.MediaDevicesError,o,a)},this.onLocalParticipantPermissionsChanged=o=>{this.emit(I.ParticipantPermissionsChanged,o,this.localParticipant)},this.onLocalChatMessageSent=o=>{this.emit(I.ChatMessage,o,this.localParticipant)},this.setMaxListeners(100),this.remoteParticipants=new Map,this.sidToIdentity=new Map,this.options=Object.assign(Object.assign({},Xm),e),this.log=nt((n=this.options.loggerName)!==null&&n!==void 0?n:We.Room),this.transcriptionReceivedTimes=new Map,this.options.audioCaptureDefaults=Object.assign(Object.assign({},Ac),e==null?void 0:e.audioCaptureDefaults),this.options.videoCaptureDefaults=Object.assign(Object.assign({},xc),e==null?void 0:e.videoCaptureDefaults),this.options.publishDefaults=Object.assign(Object.assign({},Ym),e==null?void 0:e.publishDefaults),this.maybeCreateEngine(),this.disconnectLock=new Me,this.localParticipant=new Lp("","",this.engine,this.options,this.rpcHandlers),this.options.videoCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("videoinput",lt(this.options.videoCaptureDefaults.deviceId)),this.options.audioCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("audioinput",lt(this.options.audioCaptureDefaults.deviceId)),!((s=this.options.audioOutput)===null||s===void 0)&&s.deviceId&&this.switchActiveDevice("audiooutput",lt(this.options.audioOutput.deviceId)).catch(o=>this.log.warn("Could not set audio output: ".concat(o.message),this.logContext)),this.options.e2ee&&this.setupE2EE(),De()){const o=new AbortController;(r=navigator.mediaDevices)===null||r===void 0||r.addEventListener("devicechange",this.handleDeviceChange,{signal:o.signal}),ti.cleanupRegistry&&ti.cleanupRegistry.register(this,()=>{o.abort()})}}registerTextStreamHandler(e,i){if(this.textStreamHandlers.has(e))throw new TypeError('A text stream handler for topic "'.concat(e,'" has already been set.'));this.textStreamHandlers.set(e,i)}unregisterTextStreamHandler(e){this.textStreamHandlers.delete(e)}registerByteStreamHandler(e,i){if(this.byteStreamHandlers.has(e))throw new TypeError('A byte stream handler for topic "'.concat(e,'" has already been set.'));this.byteStreamHandlers.set(e,i)}unregisterByteStreamHandler(e){this.byteStreamHandlers.delete(e)}registerRpcMethod(e,i){if(this.rpcHandlers.has(e))throw Error("RPC handler already registered for method ".concat(e,", unregisterRpcMethod before trying to register again"));this.rpcHandlers.set(e,i)}unregisterRpcMethod(e){this.rpcHandlers.delete(e)}handleIncomingRpcRequest(e,i,n,s,r,o){return m(this,void 0,void 0,function*(){if(yield this.engine.publishRpcAck(e,i),o!==1){yield this.engine.publishRpcResponse(e,i,null,he.builtIn("UNSUPPORTED_VERSION"));return}const a=this.rpcHandlers.get(n);if(!a){yield this.engine.publishRpcResponse(e,i,null,he.builtIn("UNSUPPORTED_METHOD"));return}let c=null,d=null;try{const l=yield a({requestId:i,callerIdentity:e,payload:s,responseTimeout:r});or(l)>Lc?(c=he.builtIn("RESPONSE_PAYLOAD_TOO_LARGE"),console.warn("RPC Response payload too large for ".concat(n))):d=l}catch(l){l instanceof he?c=l:(console.warn("Uncaught error returned by RPC handler for ".concat(n,". Returning APPLICATION_ERROR instead."),l),c=he.builtIn("APPLICATION_ERROR"))}yield this.engine.publishRpcResponse(e,i,d,c)})}setE2EEEnabled(e){return m(this,void 0,void 0,function*(){if(this.e2eeManager)yield Promise.all([this.localParticipant.setE2EEEnabled(e)]),this.localParticipant.identity!==""&&this.e2eeManager.setParticipantCryptorEnabled(e,this.localParticipant.identity);else throw Error("e2ee not configured, please set e2ee settings within the room options")})}setupE2EE(){var e;this.options.e2ee&&("e2eeManager"in this.options.e2ee?this.e2eeManager=this.options.e2ee.e2eeManager:this.e2eeManager=new Um(this.options.e2ee),this.e2eeManager.on(ct.ParticipantEncryptionStatusChanged,(i,n)=>{Mm(n)&&(this.isE2EEEnabled=i),this.emit(I.ParticipantEncryptionStatusChanged,i,n)}),this.e2eeManager.on(ct.EncryptionError,i=>this.emit(I.EncryptionError,i)),(e=this.e2eeManager)===null||e===void 0||e.setup(this))}get logContext(){var e;return{room:this.name,roomID:(e=this.roomInfo)===null||e===void 0?void 0:e.sid,participant:this.localParticipant.identity,pID:this.localParticipant.sid}}get isRecording(){var e,i;return(i=(e=this.roomInfo)===null||e===void 0?void 0:e.activeRecording)!==null&&i!==void 0?i:!1}getSid(){return m(this,void 0,void 0,function*(){return this.state===te.Disconnected?"":this.roomInfo&&this.roomInfo.sid!==""?this.roomInfo.sid:new Promise((e,i)=>{const n=s=>{s.sid!==""&&(this.engine.off(O.RoomUpdate,n),e(s.sid))};this.engine.on(O.RoomUpdate,n),this.once(I.Disconnected,()=>{this.engine.off(O.RoomUpdate,n),i("Room disconnected before room server id was available")})})})}get name(){var e,i;return(i=(e=this.roomInfo)===null||e===void 0?void 0:e.name)!==null&&i!==void 0?i:""}get metadata(){var e;return(e=this.roomInfo)===null||e===void 0?void 0:e.metadata}get numParticipants(){var e,i;return(i=(e=this.roomInfo)===null||e===void 0?void 0:e.numParticipants)!==null&&i!==void 0?i:0}get numPublishers(){var e,i;return(i=(e=this.roomInfo)===null||e===void 0?void 0:e.numPublishers)!==null&&i!==void 0?i:0}maybeCreateEngine(){this.engine&&!this.engine.isClosed||(this.engine=new yp(this.options),this.engine.on(O.ParticipantUpdate,this.handleParticipantUpdates).on(O.RoomUpdate,this.handleRoomUpdate).on(O.SpeakersChanged,this.handleSpeakersChanged).on(O.StreamStateChanged,this.handleStreamStateUpdate).on(O.ConnectionQualityUpdate,this.handleConnectionQualityUpdate).on(O.SubscriptionError,this.handleSubscriptionError).on(O.SubscriptionPermissionUpdate,this.handleSubscriptionPermissionUpdate).on(O.MediaTrackAdded,(e,i,n)=>{this.onTrackAdded(e,i,n)}).on(O.Disconnected,e=>{this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,e)}).on(O.ActiveSpeakersUpdate,this.handleActiveSpeakersUpdate).on(O.DataPacketReceived,this.handleDataPacket).on(O.Resuming,()=>{this.clearConnectionReconcile(),this.isResuming=!0,this.log.info("Resuming signal connection",this.logContext),this.setAndEmitConnectionState(te.SignalReconnecting)&&this.emit(I.SignalReconnecting)}).on(O.Resumed,()=>{this.registerConnectionReconcile(),this.isResuming=!1,this.log.info("Resumed signal connection",this.logContext),this.updateSubscriptions(),this.emitBufferedEvents(),this.setAndEmitConnectionState(te.Connected)&&this.emit(I.Reconnected)}).on(O.SignalResumed,()=>{this.bufferedEvents=[],(this.state===te.Reconnecting||this.isResuming)&&this.sendSyncState()}).on(O.Restarting,this.handleRestarting).on(O.SignalRestarted,this.handleSignalRestarted).on(O.Offline,()=>{this.setAndEmitConnectionState(te.Reconnecting)&&this.emit(I.Reconnecting)}).on(O.DCBufferStatusChanged,(e,i)=>{this.emit(I.DCBufferStatusChanged,e,i)}).on(O.LocalTrackSubscribed,e=>{const i=this.localParticipant.getTrackPublications().find(n=>{let{trackSid:s}=n;return s===e});if(!i){this.log.warn("could not find local track subscription for subscribed event",this.logContext);return}this.localParticipant.emit(A.LocalTrackSubscribed,i),this.emitWhenConnected(I.LocalTrackSubscribed,i,this.localParticipant)}).on(O.RoomMoved,e=>{this.log.debug("room moved",e),e.room&&this.handleRoomUpdate(e.room),this.remoteParticipants.forEach((i,n)=>{this.handleParticipantDisconnected(n,i)}),this.emit(I.Moved,e.room.name,e.token),e.participant?this.handleParticipantUpdates([e.participant,...e.otherParticipants]):this.handleParticipantUpdates(e.otherParticipants)}),this.localParticipant&&this.localParticipant.setupEngine(this.engine),this.e2eeManager&&this.e2eeManager.setupEngine(this.engine))}static getLocalDevices(e){let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return ke.getInstance().getDevices(e,i)}prepareConnection(e,i){return m(this,void 0,void 0,function*(){if(this.state===te.Disconnected){this.log.debug("prepareConnection to ".concat(e),this.logContext);try{if(xs(new URL(e))&&i){this.regionUrlProvider=new zs(e,i);const n=yield this.regionUrlProvider.getNextBestRegionUrl();n&&this.state===te.Disconnected&&(this.regionUrl=n,yield fetch(Ls(n),{method:"HEAD"}),this.log.debug("prepared connection to ".concat(n),this.logContext))}else yield fetch(Ls(e),{method:"HEAD"})}catch(n){this.log.warn("could not prepare connection",Object.assign(Object.assign({},this.logContext),{error:n}))}}})}getParticipantByIdentity(e){return this.localParticipant.identity===e?this.localParticipant:this.remoteParticipants.get(e)}clearConnectionFutures(){this.connectFuture=void 0}simulateScenario(e,i){return m(this,void 0,void 0,function*(){let n=()=>{},s;switch(e){case"signal-reconnect":yield this.engine.client.handleOnClose("simulate disconnect");break;case"speaker":s=new He({scenario:{case:"speakerUpdate",value:3}});break;case"node-failure":s=new He({scenario:{case:"nodeFailure",value:!0}});break;case"server-leave":s=new He({scenario:{case:"serverLeave",value:!0}});break;case"migration":s=new He({scenario:{case:"migration",value:!0}});break;case"resume-reconnect":this.engine.failNext(),yield this.engine.client.handleOnClose("simulate resume-disconnect");break;case"disconnect-signal-on-resume":n=()=>m(this,void 0,void 0,function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")}),s=new He({scenario:{case:"disconnectSignalOnResume",value:!0}});break;case"disconnect-signal-on-resume-no-messages":n=()=>m(this,void 0,void 0,function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")}),s=new He({scenario:{case:"disconnectSignalOnResumeNoMessages",value:!0}});break;case"full-reconnect":this.engine.fullReconnectOnNext=!0,yield this.engine.client.handleOnClose("simulate full-reconnect");break;case"force-tcp":case"force-tls":s=new He({scenario:{case:"switchCandidateProtocol",value:e==="force-tls"?2:1}}),n=()=>m(this,void 0,void 0,function*(){const r=this.engine.client.onLeave;r&&r(new gn({reason:Ge.CLIENT_INITIATED,action:Kt.RECONNECT}))});break;case"subscriber-bandwidth":if(i===void 0||typeof i!="number")throw new Error("subscriber-bandwidth requires a number as argument");s=new He({scenario:{case:"subscriberBandwidth",value:Ct(i)}});break;case"leave-full-reconnect":s=new He({scenario:{case:"leaveRequestFullReconnect",value:!0}})}s&&(yield this.engine.client.sendSimulateScenario(s),yield n())})}get canPlaybackAudio(){return this.audioEnabled}get canPlaybackVideo(){return!this.isVideoPlaybackBlocked}getActiveDevice(e){return this.localParticipant.activeDeviceMap.get(e)}switchActiveDevice(e,i){return m(this,arguments,void 0,function(n,s){var r=this;let o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return function*(){var a,c,d,l,u,h,p,g;let f=!0,y=!1;const S=o?{exact:s}:s;if(n==="audioinput"){y=r.localParticipant.audioTrackPublications.size===0;const x=(a=r.getActiveDevice(n))!==null&&a!==void 0?a:r.options.audioCaptureDefaults.deviceId;r.options.audioCaptureDefaults.deviceId=S;const M=Array.from(r.localParticipant.audioTrackPublications.values()).filter(b=>b.source===P.Source.Microphone);try{f=(yield Promise.all(M.map(b=>{var k;return(k=b.audioTrack)===null||k===void 0?void 0:k.setDeviceId(S)}))).every(b=>b===!0)}catch(b){throw r.options.audioCaptureDefaults.deviceId=x,b}}else if(n==="videoinput"){y=r.localParticipant.videoTrackPublications.size===0;const x=(c=r.getActiveDevice(n))!==null&&c!==void 0?c:r.options.videoCaptureDefaults.deviceId;r.options.videoCaptureDefaults.deviceId=S;const M=Array.from(r.localParticipant.videoTrackPublications.values()).filter(b=>b.source===P.Source.Camera);try{f=(yield Promise.all(M.map(b=>{var k;return(k=b.videoTrack)===null||k===void 0?void 0:k.setDeviceId(S)}))).every(b=>b===!0)}catch(b){throw r.options.videoCaptureDefaults.deviceId=x,b}}else if(n==="audiooutput"){if(!As()&&!r.options.webAudioMix||r.options.webAudioMix&&r.audioContext&&!("setSinkId"in r.audioContext))throw new Error("cannot switch audio output, setSinkId not supported");r.options.webAudioMix&&(s=(d=yield ke.getInstance().normalizeDeviceId("audiooutput",s))!==null&&d!==void 0?d:""),(l=(g=r.options).audioOutput)!==null&&l!==void 0||(g.audioOutput={});const x=(u=r.getActiveDevice(n))!==null&&u!==void 0?u:r.options.audioOutput.deviceId;r.options.audioOutput.deviceId=s;try{r.options.webAudioMix&&((h=r.audioContext)===null||h===void 0||h.setSinkId(s)),yield Promise.all(Array.from(r.remoteParticipants.values()).map(M=>M.setAudioOutput({deviceId:s})))}catch(M){throw r.options.audioOutput.deviceId=x,M}}return(y||n==="audiooutput")&&(r.localParticipant.activeDeviceMap.set(n,n==="audiooutput"&&((p=r.options.audioOutput)===null||p===void 0?void 0:p.deviceId)||s),r.emit(I.ActiveDeviceChanged,n,s)),f}()})}setupLocalParticipantEvents(){this.localParticipant.on(A.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).on(A.ParticipantNameChanged,this.onLocalParticipantNameChanged).on(A.AttributesChanged,this.onLocalAttributesChanged).on(A.TrackMuted,this.onLocalTrackMuted).on(A.TrackUnmuted,this.onLocalTrackUnmuted).on(A.LocalTrackPublished,this.onLocalTrackPublished).on(A.LocalTrackUnpublished,this.onLocalTrackUnpublished).on(A.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).on(A.MediaDevicesError,this.onMediaDevicesError).on(A.AudioStreamAcquired,this.startAudio).on(A.ChatMessage,this.onLocalChatMessageSent).on(A.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged)}recreateEngine(){var e;(e=this.engine)===null||e===void 0||e.close(),this.engine=void 0,this.isResuming=!1,this.remoteParticipants.clear(),this.sidToIdentity.clear(),this.bufferedEvents=[],this.maybeCreateEngine()}onTrackAdded(e,i,n){if(this.state===te.Connecting||this.state===te.Reconnecting){const l=()=>{this.onTrackAdded(e,i,n),u()},u=()=>{this.off(I.Reconnected,l),this.off(I.Connected,l),this.off(I.Disconnected,u)};this.once(I.Reconnected,l),this.once(I.Connected,l),this.once(I.Disconnected,u);return}if(this.state===te.Disconnected){this.log.warn("skipping incoming track after Room disconnected",this.logContext);return}if(e.readyState==="ended"){this.log.info("skipping incoming track as it already ended",this.logContext);return}const s=gm(i.id),r=s[0];let o=s[1],a=e.id;if(o&&o.startsWith("TR")&&(a=o),r===this.localParticipant.sid){this.log.warn("tried to create RemoteParticipant for local participant",this.logContext);return}const c=Array.from(this.remoteParticipants.values()).find(l=>l.sid===r);if(!c){this.log.error("Tried to add a track for a participant, that's not present. Sid: ".concat(r),this.logContext);return}let d;this.options.adaptiveStream&&(typeof this.options.adaptiveStream=="object"?d=this.options.adaptiveStream:d={}),c.addSubscribedMediaTrack(e,a,i,n,d)}handleDisconnect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,i=arguments.length>1?arguments[1]:void 0;var n;if(this.clearConnectionReconcile(),this.isResuming=!1,this.bufferedEvents=[],this.transcriptionReceivedTimes.clear(),this.state!==te.Disconnected){this.regionUrl=void 0;try{this.remoteParticipants.forEach(s=>{s.trackPublications.forEach(r=>{s.unpublishTrack(r.trackSid)})}),this.localParticipant.trackPublications.forEach(s=>{var r,o,a;s.track&&this.localParticipant.unpublishTrack(s.track,e),e?((r=s.track)===null||r===void 0||r.detach(),(o=s.track)===null||o===void 0||o.stop()):(a=s.track)===null||a===void 0||a.stopMonitor()}),this.localParticipant.off(A.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).off(A.ParticipantNameChanged,this.onLocalParticipantNameChanged).off(A.AttributesChanged,this.onLocalAttributesChanged).off(A.TrackMuted,this.onLocalTrackMuted).off(A.TrackUnmuted,this.onLocalTrackUnmuted).off(A.LocalTrackPublished,this.onLocalTrackPublished).off(A.LocalTrackUnpublished,this.onLocalTrackUnpublished).off(A.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).off(A.MediaDevicesError,this.onMediaDevicesError).off(A.AudioStreamAcquired,this.startAudio).off(A.ChatMessage,this.onLocalChatMessageSent).off(A.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged),this.localParticipant.trackPublications.clear(),this.localParticipant.videoTrackPublications.clear(),this.localParticipant.audioTrackPublications.clear(),this.remoteParticipants.clear(),this.sidToIdentity.clear(),this.activeSpeakers=[],this.audioContext&&typeof this.options.webAudioMix=="boolean"&&(this.audioContext.close(),this.audioContext=void 0),De()&&(window.removeEventListener("beforeunload",this.onPageLeave),window.removeEventListener("pagehide",this.onPageLeave),window.removeEventListener("freeze",this.onPageLeave),(n=navigator.mediaDevices)===null||n===void 0||n.removeEventListener("devicechange",this.handleDeviceChange))}finally{this.setAndEmitConnectionState(te.Disconnected),this.emit(I.Disconnected,i)}}}handleParticipantDisconnected(e,i){var n;this.remoteParticipants.delete(e),i&&(i.trackPublications.forEach(s=>{i.unpublishTrack(s.trackSid,!0)}),this.emit(I.ParticipantDisconnected,i),i.setDisconnected(),(n=this.localParticipant)===null||n===void 0||n.handleParticipantDisconnected(i.identity))}handleStreamHeader(e,i){return m(this,void 0,void 0,function*(){var n;if(e.contentHeader.case==="byteHeader"){const s=this.byteStreamHandlers.get(e.topic);if(!s){this.log.debug("ignoring incoming byte stream due to no handler for topic",e.topic);return}let r;const o={id:e.streamId,name:(n=e.contentHeader.value.name)!==null&&n!==void 0?n:"unknown",mimeType:e.mimeType,size:e.totalLength?Number(e.totalLength):void 0,topic:e.topic,timestamp:Xi(e.timestamp),attributes:e.attributes},a=new ReadableStream({start:c=>{r=c,this.byteStreamControllers.set(e.streamId,{info:o,controller:r,startTime:Date.now()})}});s(new Tp(o,a,Xi(e.totalLength)),{identity:i})}else if(e.contentHeader.case==="textHeader"){const s=this.textStreamHandlers.get(e.topic);if(!s){this.log.debug("ignoring incoming text stream due to no handler for topic",e.topic);return}let r;const o={id:e.streamId,mimeType:e.mimeType,size:e.totalLength?Number(e.totalLength):void 0,topic:e.topic,timestamp:Number(e.timestamp),attributes:e.attributes},a=new ReadableStream({start:c=>{r=c,this.textStreamControllers.set(e.streamId,{info:o,controller:r,startTime:Date.now()})}});s(new Pp(o,a,Xi(e.totalLength)),{identity:i})}})}handleStreamChunk(e){const i=this.byteStreamControllers.get(e.streamId);i&&e.content.length>0&&i.controller.enqueue(e);const n=this.textStreamControllers.get(e.streamId);n&&e.content.length>0&&n.controller.enqueue(e)}handleStreamTrailer(e){const i=this.textStreamControllers.get(e.streamId);i&&(i.info.attributes=Object.assign(Object.assign({},i.info.attributes),e.attributes),i.controller.close(),this.textStreamControllers.delete(e.streamId));const n=this.byteStreamControllers.get(e.streamId);n&&(n.info.attributes=Object.assign(Object.assign({},n.info.attributes),e.attributes),n.controller.close(),this.byteStreamControllers.delete(e.streamId))}selectDefaultDevices(){return m(this,void 0,void 0,function*(){var e,i,n;const s=ke.getInstance().previousDevices,r=yield ke.getInstance().getDevices(void 0,!1),o=Oe();if((o==null?void 0:o.name)==="Chrome"&&o.os!=="iOS")for(let c of r){const d=s.find(l=>l.deviceId===c.deviceId);d&&d.label!==""&&d.kind===c.kind&&d.label!==c.label&&this.getActiveDevice(c.kind)==="default"&&this.emit(I.ActiveDeviceChanged,c.kind,c.deviceId)}const a=["audiooutput","audioinput","videoinput"];for(let c of a){const d=Om(c),l=this.localParticipant.getTrackPublication(d);if(l&&(!((e=l.track)===null||e===void 0)&&e.isUserProvided))continue;const u=r.filter(p=>p.kind===c),h=this.getActiveDevice(c);if(h===((i=s.filter(p=>p.kind===c)[0])===null||i===void 0?void 0:i.deviceId)&&u.length>0&&((n=u[0])===null||n===void 0?void 0:n.deviceId)!==h){yield this.switchActiveDevice(c,u[0].deviceId);continue}c==="audioinput"&&!fo()||c==="videoinput"||u.length>0&&!u.find(p=>p.deviceId===this.getActiveDevice(c))&&(c!=="audiooutput"||!fo())&&(yield this.switchActiveDevice(c,u[0].deviceId))}})}acquireAudioContext(){return m(this,void 0,void 0,function*(){var e,i;if(typeof this.options.webAudioMix!="boolean"&&this.options.webAudioMix.audioContext?this.audioContext=this.options.webAudioMix.audioContext:(!this.audioContext||this.audioContext.state==="closed")&&(this.audioContext=(e=_c())!==null&&e!==void 0?e:void 0),this.options.webAudioMix&&this.remoteParticipants.forEach(s=>s.setAudioContext(this.audioContext)),this.localParticipant.setAudioContext(this.audioContext),this.audioContext&&this.audioContext.state==="suspended")try{yield Promise.race([this.audioContext.resume(),Ae(200)])}catch(s){this.log.warn("Could not resume audio context",Object.assign(Object.assign({},this.logContext),{error:s}))}const n=((i=this.audioContext)===null||i===void 0?void 0:i.state)==="running";n!==this.canPlaybackAudio&&(this.audioEnabled=n,this.emit(I.AudioPlaybackStatusChanged,n))})}createParticipant(e,i){var n;let s;return i?s=un.fromParticipantInfo(this.engine.client,i,{loggerContextCb:()=>this.logContext,loggerName:this.options.loggerName}):s=new un(this.engine.client,"",e,void 0,void 0,void 0,{loggerContextCb:()=>this.logContext,loggerName:this.options.loggerName}),this.options.webAudioMix&&s.setAudioContext(this.audioContext),!((n=this.options.audioOutput)===null||n===void 0)&&n.deviceId&&s.setAudioOutput(this.options.audioOutput).catch(r=>this.log.warn("Could not set audio output: ".concat(r.message),this.logContext)),s}getOrCreateParticipant(e,i){if(this.remoteParticipants.has(e)){const s=this.remoteParticipants.get(e);return i&&s.updateInfo(i)&&this.sidToIdentity.set(i.sid,i.identity),s}const n=this.createParticipant(e,i);return this.remoteParticipants.set(e,n),this.sidToIdentity.set(i.sid,i.identity),this.emitWhenConnected(I.ParticipantConnected,n),n.on(A.TrackPublished,s=>{this.emitWhenConnected(I.TrackPublished,s,n)}).on(A.TrackSubscribed,(s,r)=>{s.kind===P.Kind.Audio?(s.on(D.AudioPlaybackStarted,this.handleAudioPlaybackStarted),s.on(D.AudioPlaybackFailed,this.handleAudioPlaybackFailed)):s.kind===P.Kind.Video&&(s.on(D.VideoPlaybackFailed,this.handleVideoPlaybackFailed),s.on(D.VideoPlaybackStarted,this.handleVideoPlaybackStarted)),this.emit(I.TrackSubscribed,s,r,n)}).on(A.TrackUnpublished,s=>{this.emit(I.TrackUnpublished,s,n)}).on(A.TrackUnsubscribed,(s,r)=>{this.emit(I.TrackUnsubscribed,s,r,n)}).on(A.TrackMuted,s=>{this.emitWhenConnected(I.TrackMuted,s,n)}).on(A.TrackUnmuted,s=>{this.emitWhenConnected(I.TrackUnmuted,s,n)}).on(A.ParticipantMetadataChanged,s=>{this.emitWhenConnected(I.ParticipantMetadataChanged,s,n)}).on(A.ParticipantNameChanged,s=>{this.emitWhenConnected(I.ParticipantNameChanged,s,n)}).on(A.AttributesChanged,s=>{this.emitWhenConnected(I.ParticipantAttributesChanged,s,n)}).on(A.ConnectionQualityChanged,s=>{this.emitWhenConnected(I.ConnectionQualityChanged,s,n)}).on(A.ParticipantPermissionsChanged,s=>{this.emitWhenConnected(I.ParticipantPermissionsChanged,s,n)}).on(A.TrackSubscriptionStatusChanged,(s,r)=>{this.emitWhenConnected(I.TrackSubscriptionStatusChanged,s,r,n)}).on(A.TrackSubscriptionFailed,(s,r)=>{this.emit(I.TrackSubscriptionFailed,s,n,r)}).on(A.TrackSubscriptionPermissionChanged,(s,r)=>{this.emitWhenConnected(I.TrackSubscriptionPermissionChanged,s,r,n)}).on(A.Active,()=>{this.emitWhenConnected(I.ParticipantActive,n),n.kind===wi.AGENT&&this.localParticipant.setActiveAgent(n)}),i&&n.updateInfo(i),n}sendSyncState(){const e=Array.from(this.remoteParticipants.values()).reduce((n,s)=>(n.push(...s.getTrackPublications()),n),[]),i=this.localParticipant.getTrackPublications();this.engine.sendSyncState(e,i)}updateSubscriptions(){for(const e of this.remoteParticipants.values())for(const i of e.videoTrackPublications.values())i.isSubscribed&&_m(i)&&i.emitTrackUpdate()}getRemoteParticipantBySid(e){const i=this.sidToIdentity.get(e);if(i)return this.remoteParticipants.get(i)}registerConnectionReconcile(){this.clearConnectionReconcile();let e=0;this.connectionReconcileInterval=Pe.setInterval(()=>{!this.engine||this.engine.isClosed||!this.engine.verifyTransport()?(e++,this.log.warn("detected connection state mismatch",Object.assign(Object.assign({},this.logContext),{numFailures:e,engine:this.engine?{closed:this.engine.isClosed,transportsConnected:this.engine.verifyTransport()}:void 0})),e>=3&&(this.recreateEngine(),this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,Ge.STATE_MISMATCH))):e=0},Up)}clearConnectionReconcile(){this.connectionReconcileInterval&&Pe.clearInterval(this.connectionReconcileInterval)}setAndEmitConnectionState(e){return e===this.state?!1:(this.state=e,this.emit(I.ConnectionStateChanged,this.state),!0)}emitBufferedEvents(){this.bufferedEvents.forEach(e=>{let[i,n]=e;this.emit(i,...n)}),this.bufferedEvents=[]}emitWhenConnected(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];if(this.state===te.Reconnecting||this.isResuming||!this.engine||this.engine.pendingReconnect)this.bufferedEvents.push([e,n]);else if(this.state===te.Connected)return this.emit(e,...n);return!1}simulateParticipants(e){return m(this,void 0,void 0,function*(){var i,n;const s=Object.assign({audio:!0,video:!0,useRealTracks:!1},e.publish),r=Object.assign({count:9,audio:!1,video:!0,aspectRatios:[1.66,1.7,1.3]},e.participants);if(this.handleDisconnect(),this.roomInfo=new pn({sid:"RM_SIMULATED",name:"simulated-room",emptyTimeout:0,maxParticipants:0,creationTime:de.parse(new Date().getTime()),metadata:"",numParticipants:1,numPublishers:1,turnPassword:"",enabledCodecs:[],activeRecording:!1}),this.localParticipant.updateInfo(new _t({identity:"simulated-local",name:"local-name"})),this.setupLocalParticipantEvents(),this.emit(I.SignalConnected),this.emit(I.Connected),this.setAndEmitConnectionState(te.Connected),s.video){const o=new Gs(P.Kind.Video,new jt({source:pe.CAMERA,sid:Math.floor(Math.random()*1e4).toString(),type:ze.AUDIO,name:"video-dummy"}),new Mi(s.useRealTracks?(yield window.navigator.mediaDevices.getUserMedia({video:!0})).getVideoTracks()[0]:yo(160*((i=r.aspectRatios[0])!==null&&i!==void 0?i:1),160,!0,!0),void 0,!1,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(o),this.localParticipant.emit(A.LocalTrackPublished,o)}if(s.audio){const o=new Gs(P.Kind.Audio,new jt({source:pe.MICROPHONE,sid:Math.floor(Math.random()*1e4).toString(),type:ze.AUDIO}),new _i(s.useRealTracks?(yield navigator.mediaDevices.getUserMedia({audio:!0})).getAudioTracks()[0]:Bn(),void 0,!1,this.audioContext,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(o),this.localParticipant.emit(A.LocalTrackPublished,o)}for(let o=0;o<r.count-1;o+=1){let a=new _t({sid:Math.floor(Math.random()*1e4).toString(),identity:"simulated-".concat(o),state:Ht.ACTIVE,tracks:[],joinedAt:de.parse(Date.now())});const c=this.getOrCreateParticipant(a.identity,a);if(r.video){const d=yo(160*((n=r.aspectRatios[o%r.aspectRatios.length])!==null&&n!==void 0?n:1),160,!1,!0),l=new jt({source:pe.CAMERA,sid:Math.floor(Math.random()*1e4).toString(),type:ze.AUDIO});c.addSubscribedMediaTrack(d,l.sid,new MediaStream([d]),new RTCRtpReceiver),a.tracks=[...a.tracks,l]}if(r.audio){const d=Bn(),l=new jt({source:pe.MICROPHONE,sid:Math.floor(Math.random()*1e4).toString(),type:ze.AUDIO});c.addSubscribedMediaTrack(d,l.sid,new MediaStream([d]),new RTCRtpReceiver),a.tracks=[...a.tracks,l]}c.updateInfo(a)}})}emit(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];if(e!==I.ActiveSpeakersChanged&&e!==I.TranscriptionReceived){const r=zc(n).filter(o=>o!==void 0);this.log.debug("room event ".concat(e),Object.assign(Object.assign({},this.logContext),{event:e,args:r}))}return super.emit(e,...n)}}ti.cleanupRegistry=typeof FinalizationRegistry<"u"&&new FinalizationRegistry(t=>{t()});function zc(t){return t.map(e=>{if(e)return Array.isArray(e)?zc(e):typeof e=="object"?"logContext"in e?e.logContext:void 0:e})}var je;(function(t){t[t.IDLE=0]="IDLE",t[t.RUNNING=1]="RUNNING",t[t.SKIPPED=2]="SKIPPED",t[t.SUCCESS=3]="SUCCESS",t[t.FAILED=4]="FAILED"})(je||(je={}));class vt extends Ye.EventEmitter{constructor(e,i){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};super(),this.status=je.IDLE,this.logs=[],this.options={},this.url=e,this.token=i,this.name=this.constructor.name,this.room=new ti(n.roomOptions),this.connectOptions=n.connectOptions,this.options=n}run(e){return m(this,void 0,void 0,function*(){if(this.status!==je.IDLE)throw Error("check is running already");this.setStatus(je.RUNNING);try{yield this.perform()}catch(i){i instanceof Error&&(this.options.errorsAsWarnings?this.appendWarning(i.message):this.appendError(i.message))}return yield this.disconnect(),yield new Promise(i=>setTimeout(i,500)),this.status!==je.SKIPPED&&this.setStatus(this.isSuccess()?je.SUCCESS:je.FAILED),e&&e(),this.getInfo()})}isSuccess(){return!this.logs.some(e=>e.level==="error")}connect(e){return m(this,void 0,void 0,function*(){return this.room.state===te.Connected?this.room:(e||(e=this.url),yield this.room.connect(e,this.token,this.connectOptions),this.room)})}disconnect(){return m(this,void 0,void 0,function*(){this.room&&this.room.state!==te.Disconnected&&(yield this.room.disconnect(),yield new Promise(e=>setTimeout(e,500)))})}skip(){this.setStatus(je.SKIPPED)}switchProtocol(e){return m(this,void 0,void 0,function*(){let i=!1,n=!1;if(this.room.on(I.Reconnecting,()=>{i=!0}),this.room.once(I.Reconnected,()=>{n=!0}),this.room.simulateScenario("force-".concat(e)),yield new Promise(r=>setTimeout(r,1e3)),!i)return;const s=Date.now()+1e4;for(;Date.now()<s;){if(n)return;yield Ae(100)}throw new Error("Could not reconnect using ".concat(e," protocol after 10 seconds"))})}appendMessage(e){this.logs.push({level:"info",message:e}),this.emit("update",this.getInfo())}appendWarning(e){this.logs.push({level:"warning",message:e}),this.emit("update",this.getInfo())}appendError(e){this.logs.push({level:"error",message:e}),this.emit("update",this.getInfo())}setStatus(e){this.status=e,this.emit("update",this.getInfo())}get engine(){var e;return(e=this.room)===null||e===void 0?void 0:e.engine}getInfo(){return{logs:this.logs,name:this.name,status:this.status,description:this.description}}}class Fp extends vt{get description(){return"Cloud regions"}perform(){return m(this,void 0,void 0,function*(){const e=new zs(this.url,this.token);if(!e.isCloud()){this.skip();return}const i=[],n=new Set;for(let r=0;r<3;r++){const o=yield e.getNextBestRegionUrl();if(!o)break;if(n.has(o))continue;n.add(o);const a=yield this.checkCloudRegion(o);this.appendMessage("".concat(a.region," RTT: ").concat(a.rtt,"ms, duration: ").concat(a.duration,"ms")),i.push(a)}i.sort((r,o)=>(r.duration-o.duration)*.5+(r.rtt-o.rtt)*.5);const s=i[0];this.bestStats=s,this.appendMessage("best Cloud region: ".concat(s.region))})}getInfo(){const e=super.getInfo();return e.data=this.bestStats,e}checkCloudRegion(e){return m(this,void 0,void 0,function*(){var i,n;yield this.connect(e),this.options.protocol==="tcp"&&(yield this.switchProtocol("tcp"));const s=(i=this.room.serverInfo)===null||i===void 0?void 0:i.region;if(!s)throw new Error("Region not found");const r=yield this.room.localParticipant.streamText({topic:"test"}),o=1e3,c=1e6/o,d="A".repeat(o),l=Date.now();for(let g=0;g<c;g++)yield r.write(d);yield r.close();const u=Date.now(),h=yield(n=this.room.engine.pcManager)===null||n===void 0?void 0:n.publisher.getStats(),p={region:s,rtt:1e4,duration:u-l};return h==null||h.forEach(g=>{g.type==="candidate-pair"&&g.nominated&&(p.rtt=g.currentRoundTripTime*1e3)}),yield this.disconnect(),p})}}const Jn=1e4;class Vp extends vt{get description(){return"Connection via UDP vs TCP"}perform(){return m(this,void 0,void 0,function*(){const e=yield this.checkConnectionProtocol("udp"),i=yield this.checkConnectionProtocol("tcp");this.bestStats=e,e.qualityLimitationDurations.bandwidth-i.qualityLimitationDurations.bandwidth>.5||(e.packetsLost-i.packetsLost)/e.packetsSent>.01?(this.appendMessage("best connection quality via tcp"),this.bestStats=i):this.appendMessage("best connection quality via udp");const n=this.bestStats;this.appendMessage("upstream bitrate: ".concat((n.bitrateTotal/n.count/1e3/1e3).toFixed(2)," mbps")),this.appendMessage("RTT: ".concat((n.rttTotal/n.count*1e3).toFixed(2)," ms")),this.appendMessage("jitter: ".concat((n.jitterTotal/n.count*1e3).toFixed(2)," ms")),n.packetsLost>0&&this.appendWarning("packets lost: ".concat((n.packetsLost/n.packetsSent*100).toFixed(2),"%")),n.qualityLimitationDurations.bandwidth>1&&this.appendWarning("bandwidth limited ".concat((n.qualityLimitationDurations.bandwidth/(Jn/1e3)*100).toFixed(2),"%")),n.qualityLimitationDurations.cpu>0&&this.appendWarning("cpu limited ".concat((n.qualityLimitationDurations.cpu/(Jn/1e3)*100).toFixed(2),"%"))})}getInfo(){const e=super.getInfo();return e.data=this.bestStats,e}checkConnectionProtocol(e){return m(this,void 0,void 0,function*(){yield this.connect(),e==="tcp"?yield this.switchProtocol("tcp"):yield this.switchProtocol("udp");const i=document.createElement("canvas");i.width=1280,i.height=720;const n=i.getContext("2d");if(!n)throw new Error("Could not get canvas context");let s=0;const r=()=>{s=(s+1)%360,n.fillStyle="hsl(".concat(s,", 100%, 50%)"),n.fillRect(0,0,i.width,i.height),requestAnimationFrame(r)};r();const a=i.captureStream(30).getVideoTracks()[0],d=(yield this.room.localParticipant.publishTrack(a,{simulcast:!1,degradationPreference:"maintain-resolution",videoEncoding:{maxBitrate:2e6}})).track,l={protocol:e,packetsLost:0,packetsSent:0,qualityLimitationDurations:{},rttTotal:0,jitterTotal:0,bitrateTotal:0,count:0},u=setInterval(()=>m(this,void 0,void 0,function*(){const h=yield d.getRTCStatsReport();h==null||h.forEach(p=>{p.type==="outbound-rtp"?(l.packetsSent=p.packetsSent,l.qualityLimitationDurations=p.qualityLimitationDurations,l.bitrateTotal+=p.targetBitrate,l.count++):p.type==="remote-inbound-rtp"&&(l.packetsLost=p.packetsLost,l.rttTotal+=p.roundTripTime,l.jitterTotal+=p.jitter)})}),1e3);return yield new Promise(h=>setTimeout(h,Jn)),clearInterval(u),a.stop(),i.remove(),yield this.disconnect(),l})}}class Bp extends vt{get description(){return"Can publish audio"}perform(){return m(this,void 0,void 0,function*(){var e;const i=yield this.connect(),n=yield Dp();if(yield wc(n,1e3))throw new Error("unable to detect audio from microphone");this.appendMessage("detected audio from microphone"),i.localParticipant.publishTrack(n),yield new Promise(a=>setTimeout(a,3e3));const r=yield(e=n.sender)===null||e===void 0?void 0:e.getStats();if(!r)throw new Error("Could not get RTCStats");let o=0;if(r.forEach(a=>{a.type==="outbound-rtp"&&(a.kind==="audio"||!a.kind&&a.mediaType==="audio")&&(o=a.packetsSent)}),o===0)throw new Error("Could not determine packets are sent");this.appendMessage("published ".concat(o," audio packets"))})}}class jp extends vt{get description(){return"Can publish video"}perform(){return m(this,void 0,void 0,function*(){var e;const i=yield this.connect(),n=yield Mp();yield this.checkForVideo(n.mediaStreamTrack),i.localParticipant.publishTrack(n),yield new Promise(o=>setTimeout(o,5e3));const s=yield(e=n.sender)===null||e===void 0?void 0:e.getStats();if(!s)throw new Error("Could not get RTCStats");let r=0;if(s.forEach(o=>{o.type==="outbound-rtp"&&(o.kind==="video"||!o.kind&&o.mediaType==="video")&&(r+=o.packetsSent)}),r===0)throw new Error("Could not determine packets are sent");this.appendMessage("published ".concat(r," video packets"))})}checkForVideo(e){return m(this,void 0,void 0,function*(){const i=new MediaStream;i.addTrack(e.clone());const n=document.createElement("video");n.srcObject=i,n.muted=!0,yield new Promise(s=>{n.onplay=()=>{setTimeout(()=>{var r,o,a,c;const d=document.createElement("canvas"),l=e.getSettings(),u=(o=(r=l.width)!==null&&r!==void 0?r:n.videoWidth)!==null&&o!==void 0?o:1280,h=(c=(a=l.height)!==null&&a!==void 0?a:n.videoHeight)!==null&&c!==void 0?c:720;d.width=u,d.height=h;const p=d.getContext("2d");p.drawImage(n,0,0);const f=p.getImageData(0,0,d.width,d.height).data;let y=!0;for(let S=0;S<f.length;S+=4)if(f[S]!==0||f[S+1]!==0||f[S+2]!==0){y=!1;break}y?this.appendError("camera appears to be producing only black frames"):this.appendMessage("received video frames"),s()},1e3)},n.play()}),n.remove()})}}class zp extends vt{get description(){return"Resuming connection after interruption"}perform(){return m(this,void 0,void 0,function*(){var e;const i=yield this.connect();let n=!1,s=!1,r;const o=new Promise(d=>{setTimeout(d,5e3),r=d}),a=()=>{n=!0};i.on(I.SignalReconnecting,a).on(I.Reconnecting,a).on(I.Reconnected,()=>{s=!0,r(!0)}),(e=i.engine.client.ws)===null||e===void 0||e.close();const c=i.engine.client.onClose;if(c&&c(""),yield o,n){if(!s||i.state!==te.Connected)throw this.appendWarning("reconnection is only possible in Redis-based configurations"),new Error("Not able to reconnect")}else throw new Error("Did not attempt to reconnect")})}}class qp extends vt{get description(){return"Can connect via TURN"}perform(){return m(this,void 0,void 0,function*(){var e,i;const n=new ir,s=yield n.join(this.url,this.token,{autoSubscribe:!0,maxRetries:0,e2eeEnabled:!1,websocketTimeout:15e3});let r=!1,o=!1,a=!1;for(let c of s.iceServers)for(let d of c.urls)d.startsWith("turn:")?(o=!0,a=!0):d.startsWith("turns:")&&(o=!0,a=!0,r=!0),d.startsWith("stun:")&&(a=!0);a?o&&!r&&this.appendWarning("TURN is configured server side, but TURN/TLS is unavailable."):this.appendWarning("No STUN servers configured on server side."),yield n.close(),!((i=(e=this.connectOptions)===null||e===void 0?void 0:e.rtcConfig)===null||i===void 0)&&i.iceServers||o?yield this.room.connect(this.url,this.token,{rtcConfig:{iceTransportPolicy:"relay"}}):(this.appendWarning("No TURN servers configured."),this.skip(),yield new Promise(c=>setTimeout(c,0)))})}}class Gp extends vt{get description(){return"Establishing WebRTC connection"}perform(){return m(this,void 0,void 0,function*(){let e=!1,i=!1;this.room.on(I.SignalConnected,()=>{const n=this.room.engine.client.onTrickle;this.room.engine.client.onTrickle=(s,r)=>{if(s.candidate){const o=new RTCIceCandidate(s);let a="".concat(o.protocol," ").concat(o.address,":").concat(o.port," ").concat(o.type);o.address&&(Wp(o.address)?a+=" (private)":o.protocol==="tcp"&&o.tcpType==="passive"?(e=!0,a+=" (passive)"):o.protocol==="udp"&&(i=!0)),this.appendMessage(a)}n&&n(s,r)},this.room.engine.pcManager&&(this.room.engine.pcManager.subscriber.onIceCandidateError=s=>{s instanceof RTCPeerConnectionIceErrorEvent&&this.appendWarning("error with ICE candidate: ".concat(s.errorCode," ").concat(s.errorText," ").concat(s.url))})});try{yield this.connect(),Q.info("now the room is connected")}catch(n){throw this.appendWarning("ports need to be open on firewall in order to connect."),n}e||this.appendWarning("Server is not configured for ICE/TCP"),i||this.appendWarning("No public IPv4 UDP candidates were found. Your server is likely not configured correctly")})}}function Wp(t){const e=t.split(".");if(e.length===4){if(e[0]==="10")return!0;if(e[0]==="192"&&e[1]==="168")return!0;if(e[0]==="172"){const i=parseInt(e[1],10);if(i>=16&&i<=31)return!0}}return!1}class Hp extends vt{get description(){return"Connecting to signal connection via WebSocket"}perform(){return m(this,void 0,void 0,function*(){var e,i,n;(this.url.startsWith("ws:")||this.url.startsWith("http:"))&&this.appendWarning("Server is insecure, clients may block connections to it");let s=new ir;const r=yield s.join(this.url,this.token,{autoSubscribe:!0,maxRetries:0,e2eeEnabled:!1,websocketTimeout:15e3});this.appendMessage("Connected to server, version ".concat(r.serverVersion,".")),((e=r.serverInfo)===null||e===void 0?void 0:e.edition)===_a.Cloud&&(!((i=r.serverInfo)===null||i===void 0)&&i.region)&&this.appendMessage("LiveKit Cloud: ".concat((n=r.serverInfo)===null||n===void 0?void 0:n.region)),yield s.close()})}}class Df extends Ye.EventEmitter{constructor(e,i){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};super(),this.options={},this.checkResults=new Map,this.url=e,this.token=i,this.options=n}getNextCheckId(){const e=this.checkResults.size;return this.checkResults.set(e,{logs:[],status:je.IDLE,name:"",description:""}),e}updateCheck(e,i){this.checkResults.set(e,i),this.emit("checkUpdate",e,i)}isSuccess(){return Array.from(this.checkResults.values()).every(e=>e.status!==je.FAILED)}getResults(){return Array.from(this.checkResults.values())}createAndRunCheck(e){return m(this,void 0,void 0,function*(){const i=this.getNextCheckId(),n=new e(this.url,this.token,this.options),s=o=>{this.updateCheck(i,o)};n.on("update",s);const r=yield n.run();return n.off("update",s),r})}checkWebsocket(){return m(this,void 0,void 0,function*(){return this.createAndRunCheck(Hp)})}checkWebRTC(){return m(this,void 0,void 0,function*(){return this.createAndRunCheck(Gp)})}checkTURN(){return m(this,void 0,void 0,function*(){return this.createAndRunCheck(qp)})}checkReconnect(){return m(this,void 0,void 0,function*(){return this.createAndRunCheck(zp)})}checkPublishAudio(){return m(this,void 0,void 0,function*(){return this.createAndRunCheck(Bp)})}checkPublishVideo(){return m(this,void 0,void 0,function*(){return this.createAndRunCheck(jp)})}checkConnectionProtocol(){return m(this,void 0,void 0,function*(){const e=yield this.createAndRunCheck(Vp);if(e.data&&"protocol"in e.data){const i=e.data;this.options.protocol=i.protocol}return e})}checkCloudRegion(){return m(this,void 0,void 0,function*(){return this.createAndRunCheck(Fp)})}}const qc=()=>({data(){return{speaking:!1,speakingTimer:null}},methods:{setSpeaking(t){this.speaking=t},setSpeakingInternal(t){this.speaking=!0,this.speakingTimer=setTimeout(()=>{this.speaking=!1,this.speakingTimer=null},t)},setSpeakingWithTimeout(t){this.speakingTimer?(clearTimeout(this.speakingTimer),this.setSpeakingInternal(t)):this.setSpeakingInternal(t)},setSpeakingWithDefaultTimeout(){this.setSpeakingWithTimeout(1e3)},resetSpeaking(){this.speaking=!1,this.speakingTimer&&(clearTimeout(this.speakingTimer),this.speakingTimer=null)}}}),Kp={mixins:[$o(),Di()],computed:{...pt(ft)},props:["isLocal","shouldShowMuteAudio","shouldShowMuteVideo","shouldShowClose","shouldShowVideoKick","shouldShowAudioMute","audioMute","videoMute","userName"],methods:{className(){return"user-video-item-context-menu"},onShowContextMenu(t,e){this.onShowContextMenuBase(t,e)},onCloseContextMenu(){this.onCloseContextMenuBase()},getContextMenuItems(){const t=[];return this.menuableItem&&(t.push({title:this.userName,icon:"mdi-close",action:()=>{this.onCloseContextMenu()},enabled:!0}),this.shouldShowMuteAudio&&t.push({title:this.audioMute?this.$vuetify.locale.t("$vuetify.unmute_audio"):this.$vuetify.locale.t("$vuetify.mute_audio"),icon:this.audioMute?"mdi-microphone-off":"mdi-microphone",action:()=>{this.audioMute?this.menuableItem.doMuteAudio(!1):this.menuableItem.doMuteAudio(!0)},enabled:!0}),this.shouldShowMuteVideo&&t.push({title:this.videoMute?this.$vuetify.locale.t("$vuetify.unmute_video"):this.$vuetify.locale.t("$vuetify.mute_video"),icon:this.videoMute?"mdi-video-off":"mdi-video",action:()=>{this.videoMute?this.menuableItem.doMuteVideo(!1):this.menuableItem.doMuteVideo(!0)},enabled:!0}),this.shouldShowClose&&t.push({title:this.$vuetify.locale.t("$vuetify.close_video"),icon:"mdi-close",iconColor:"error",action:()=>{this.menuableItem.onLocalClose()},enabled:!0}),this.shouldShowVideoKick&&t.push({title:this.$vuetify.locale.t("$vuetify.kick"),icon:"mdi-block-helper",iconColor:"error",action:()=>{this.menuableItem.kickRemote()},enabled:!0}),this.shouldShowAudioMute&&t.push({title:this.$vuetify.locale.t("$vuetify.force_mute"),icon:"mdi-microphone-off",iconColor:"error",action:()=>{this.menuableItem.forceMuteRemote()},enabled:!0}),this.pinningIsAvailable()&&(t.push({title:this.$vuetify.locale.t("$vuetify.pin_video"),icon:"mdi-pin",action:()=>{T.emit(Ii,this.menuableItem.getVideoStreamId())},enabled:!0}),this.chatStore.pinnedTrackSid&&t.push({title:this.$vuetify.locale.t("$vuetify.unpin_video"),icon:"mdi-pin-off-outline",action:()=>{T.emit(Si)},enabled:!0}))),t}}};function $p(t,e,i,n,s,r){return F(),H(Qo,{attach:"#video-splitpanes",class:be(r.className()),"model-value":t.showContextMenu,transition:!1,"open-on-click":!1,"open-on-focus":!1,"open-on-hover":!1,"open-delay":0,"close-delay":0,"close-on-back":!1,"onUpdate:modelValue":t.onUpdate},{default:U(()=>[K(Jo,null,{default:U(()=>[(F(!0),ae(Xt,null,hn(r.getContextMenuItems(),(o,a)=>(F(),H(Yo,{key:a,disabled:!o.enabled,onClick:o.action},{prepend:U(()=>[o.icon?(F(),H(ce,{key:0,color:o.iconColor},{default:U(()=>[ie(ve(o.icon),1)]),_:2},1032,["color"])):W("",!0)]),title:U(()=>[ie(ve(o.title),1)]),_:2},1032,["disabled","onClick"]))),128))]),_:1})]),_:1},8,["class","model-value","onUpdate:modelValue"])}const Gc=mt(Kp,[["render",$p]]),Jp={name:"UserVideo",mixins:[Di(),qc()],components:{UserVideoContextMenu:Gc},data(){return{userName:Xo,audioMute:!0,errorDescription:null,avatar:"",videoMute:!0,userId:null,audioPublication:null,videoPublication:null}},props:{id:{type:String},localVideoProperties:{type:Object}},methods:{setAudioStream(t,e){var i;console.info("Setting source audio for videoRef=",this.$refs.videoRef," track=",t," audio tag id=",this.id,", enabled=",e),this.setDisplayAudioMute(!e),this.audioPublication=t,this.localVideoProperties||(i=t==null?void 0:t.audioTrack)==null||i.attach(this.$refs.videoRef)},setVideoStream(t,e){var i;console.info("Setting source video for videoRef=",this.$refs.videoRef," track=",t," video tag id=",this.id,", enabled=",e),this.setDisplayVideoMute(!e),this.videoPublication=t,(i=t==null?void 0:t.videoTrack)==null||i.attach(this.$refs.videoRef)},getVideoStream(){return this.videoPublication},getAudioStream(){return this.audioPublication},getVideoStreamId(){var t;return(t=this.videoPublication)==null?void 0:t.trackSid},getAudioStreamId(){var t;return(t=this.audioPublication)==null?void 0:t.trackSid},getId(){return this.$props.id},getVideoElement(){var t;return(t=this==null?void 0:this.$refs)==null?void 0:t.videoRef},setUserName(t){this.userName=t},getUserName(){return this.userName},setDisplayAudioMute(t,e){this.audioMute=t,this.isLocal&&!e&&(this.chatStore.localMicrophoneEnabled=!t)},setAvatar(t){this.avatar=t},getAvatar(){return this.avatar},setDisplayVideoMute(t,e){this.videoMute=t,this.isLocal&&!e&&(this.chatStore.localVideoEnabled=!t)},getUserId(){return this.userId},setUserId(t){this.userId=t},doMuteAudio(t,e){var i,n;t?(i=this.audioPublication)==null||i.mute():(n=this.audioPublication)==null||n.unmute(),this.setDisplayAudioMute(t,e)},doMuteVideo(t,e){var i,n;t?(i=this.videoPublication)==null||i.mute():(n=this.videoPublication)==null||n.unmute(),this.setDisplayVideoMute(t,e)},onLocalClose(){var t,e;this.localVideoProperties.localParticipant.unpublishTrack((t=this.videoPublication)==null?void 0:t.videoTrack),this.localVideoProperties.localParticipant.unpublishTrack((e=this.audioPublication)==null?void 0:e.audioTrack)},isComponentLocal(){return this.isLocal},getVideoSource(){var t;return(t=this.videoPublication)==null?void 0:t.source},kickRemote(){se.put(`/api/video/${this.chatStore.chatDto.id}/kick?userId=${this.userId}`)},forceMuteRemote(){se.put(`/api/video/${this.chatStore.chatDto.id}/mute?userId=${this.userId}`)},shouldShowMuteAudio(){return this.isLocal&&this.audioPublication!=null},shouldShowMuteVideo(){return this.isLocal&&this.videoPublication!=null},shouldShowClose(){return this.isLocal},shouldShowVideoKick(){return!this.isLocal&&this.canVideoKick},shouldShowAudioMute(){return!this.isLocal&&this.canAudioMute},onShowContextMenu(t,e){this.$refs.contextMenuRef.onShowContextMenu(t,e)},shouldShowCaption(){return!(this.isMobile()&&this.chatStore.presenterEnabled)||this.videoIsGallery()},pinCurrentVideo(){this.pinningIsAvailable()&&T.emit(Ii,this.getVideoStreamId())}},computed:{...pt(ft),avatarIsSet(){return Be(this.avatar)},isLocal(){return!!this.localVideoProperties},canVideoKick(){return!this.isLocal&&this.chatStore.canVideoKickParticipant(this.userId)},canAudioMute(){return!this.isLocal&&this.chatStore.canAudioMuteParticipant(this.userId)},videoContainerElementClass(){const t=["video-container-element"];return this.videoIsHorizontal()?t.push("video-container-element-position-horizontal"):this.videoIsGallery()?t.push("video-container-element-position-gallery"):t.push("video-container-element-position-vertical"),t},videoElementClass(){const t=["video-element"];return this.videoIsHorizontal()?t.push("video-element-horizontal"):this.videoIsGallery()?t.push("video-element-gallery"):t.push("video-element-vertical"),this.pinningIsAvailable()&&t.push("can-pin-video-element"),t}}},Qp=["src"],Yp=["id"];function Xp(t,e,i,n,s,r){const o=Re("UserVideoContextMenu");return F(),ae("div",{class:be(r.videoContainerElementClass),ref:"containerRef",onContextmenu:e[1]||(e[1]=Te(a=>r.onShowContextMenu(a,this),["stop"]))},[Pt(ge("img",{class:be(r.videoElementClass),src:s.avatar},null,10,Qp),[[Et,r.avatarIsSet&&s.videoMute]]),Pt(ge("video",{class:be(r.videoElementClass),id:i.id,autoPlay:"",playsInline:"",ref:"videoRef",onClick:e[0]||(e[0]=Te(a=>r.pinCurrentVideo(),["prevent"]))},null,10,Yp),[[Et,!s.videoMute||!r.avatarIsSet]]),r.shouldShowCaption()?(F(),ae("p",{key:0,class:be([t.speaking?"video-container-element-caption-speaking":"","video-container-element-caption","inline-caption-base"])},[ie(ve(s.userName)+" ",1),s.audioMute?(F(),H(ce,{key:0},{default:U(()=>e[2]||(e[2]=[ie("mdi-microphone-off")])),_:1})):W("",!0)],2)):W("",!0),K(o,{ref:"contextMenuRef",isLocal:"isLocal",shouldShowMuteAudio:r.shouldShowMuteAudio(),shouldShowMuteVideo:r.shouldShowMuteVideo(),shouldShowClose:r.shouldShowClose(),shouldShowVideoKick:r.shouldShowVideoKick(),shouldShowAudioMute:r.shouldShowAudioMute(),audioMute:s.audioMute,videoMute:s.videoMute,userName:r.getUserName()},null,8,["shouldShowMuteAudio","shouldShowMuteVideo","shouldShowClose","shouldShowVideoKick","shouldShowAudioMute","audioMute","videoMute","userName"])],34)}const Zp=mt(Jp,[["render",Xp],["__scopeId","data-v-d8c40161"]]);var kt={},Uo;function ef(){if(Uo)return kt;Uo=1,Object.defineProperty(kt,"__esModule",{value:!0}),kt.largestRect=kt.largestSquare=void 0;const t=(n,s,r,o={})=>{if(n<0||s<0)throw new Error("Container must have a non-negative area");if(r<1||!Number.isInteger(r))throw new Error("Number of shapes to place must be a positive integer");const a=o.aspectRatio||1;if(isNaN(a))throw new Error("Aspect ratio must be a number");let c={area:0,cols:0,rows:0,width:0,height:0};const d=r,l=-1;for(let u=d;u>0;u+=l){const h=Math.ceil(r/u),p=n/(u*a),g=s/h;let f,y;p<=g?(f=n/u,y=f/a):(y=s/h,f=y*a);const S=f*y;S>c.area&&(c={area:S,width:f,height:y,rows:h,cols:u})}return c},e=(n,s,r)=>t(n,s,r);kt.largestSquare=e;const i=(n,s,r,o,a)=>t(n,s,r,{aspectRatio:o/a});return kt.largestRect=i,kt}var tf=ef();const nf={mixins:[Di()],data(){return{}},computed:{...pt(ft),videoButtonsControlClass(){const t=[];return this.videoIsHorizontal()||this.videoIsGallery()||this.isMobile()?t.push("video-buttons-control","video-buttons-control-horizontal"):this.videoIsVertical()&&(this.chatStore.presenterEnabled?t.push("video-buttons-control","video-buttons-control-horizontal"):t.push("video-buttons-control","video-buttons-control-vertical")),this.isMobile()&&t.push("video-buttons-control-mobile"),t},positionItems(){return Ad()},chatId(){return this.$route.params.id},enableToggleMiniatures(){return this.chatStore.presenterEnabled&&this.isPresenterEnabled()}},methods:{doMuteAudio(t){this.chatStore.localMicrophoneEnabled=t},doMuteVideo(t){this.chatStore.localVideoEnabled=t},onEnterFullscreen(t){this.$emit("requestFullScreen")},stopCall(){Od(this.chatStore,this.$route,this.$router)},addScreenSource(){T.emit(ls)},changeVideoPosition(t){this.chatStore.videoPosition=t,Dd(t)},presenterValue(){return this.chatStore.presenterEnabled},miniaturesValue(){return this.chatStore.videoMiniaturesEnabled},messagesValue(){return this.chatStore.videoMessagesEnabled},presenterClick(){const t=!this.chatStore.presenterEnabled;this.chatStore.presenterEnabled=t,Md(t)},addVideoSource(){T.emit(_d)},startRecord(){se.put(`/api/video/${this.chatId}/record/start`),this.chatStore.initializingStaringVideoRecord=!0},stopRecord(){se.put(`/api/video/${this.chatId}/record/stop`),this.chatStore.initializingStoppingVideoRecord=!0},openSettings(){T.emit(wd,"a_video_settings")},toggleMiniatures(){const t=!this.chatStore.videoMiniaturesEnabled;this.chatStore.videoMiniaturesEnabled=t,Rd(t)},toggleMessages(){const t=!this.chatStore.videoMessagesEnabled;this.chatStore.videoMessagesEnabled=t,Id(t)}}};function sf(t,e,i,n,s,r){return F(),ae("div",{class:be(r.videoButtonsControlClass)},[K(Ld,{"show-arrows":""},{default:U(()=>[t.chatStore.showCallManagement&&t.chatStore.isInCall()?(F(),H(Ce,{key:0,variant:"plain",tile:"",icon:"",loading:t.chatStore.initializingVideoCall,onClick:e[0]||(e[0]=Te(o=>r.stopCall(),["stop","prevent"])),title:t.$vuetify.locale.t("$vuetify.leave_call")},{default:U(()=>[K(ce,{size:"x-large",class:be(t.chatStore.shouldPhoneBlink?"call-blink":"text-red")},{default:U(()=>e[11]||(e[11]=[ie("mdi-phone")])),_:1},8,["class"])]),_:1},8,["loading","title"])):W("",!0),t.chatStore.canShowMicrophoneButton?(F(),H(Ce,{key:1,variant:"plain",tile:"",icon:"",onClick:e[1]||(e[1]=Te(o=>r.doMuteAudio(!t.chatStore.localMicrophoneEnabled),["stop","prevent"])),title:t.chatStore.localMicrophoneEnabled?t.$vuetify.locale.t("$vuetify.mute_audio"):t.$vuetify.locale.t("$vuetify.unmute_audio")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>[ie(ve(t.chatStore.localMicrophoneEnabled?"mdi-microphone":"mdi-microphone-off"),1)]),_:1})]),_:1},8,["title"])):W("",!0),t.chatStore.canShowVideoButton?(F(),H(Ce,{key:2,variant:"plain",tile:"",icon:"",onClick:e[2]||(e[2]=Te(o=>r.doMuteVideo(!t.chatStore.localVideoEnabled),["stop","prevent"])),title:t.chatStore.localVideoEnabled?t.$vuetify.locale.t("$vuetify.mute_video"):t.$vuetify.locale.t("$vuetify.unmute_video")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>[ie(ve(t.chatStore.localVideoEnabled?"mdi-video":"mdi-video-off"),1)]),_:1})]),_:1},8,["title"])):W("",!0),t.isMobile()?W("",!0):(F(),H(Ce,{key:3,variant:"plain",tile:"",icon:"",onClick:e[3]||(e[3]=Te(o=>r.addScreenSource(),["stop","prevent"])),title:t.$vuetify.locale.t("$vuetify.screen_share")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[12]||(e[12]=[ie("mdi-monitor-share")])),_:1})]),_:1},8,["title"])),K(Ce,{variant:"plain",tile:"",icon:"",onClick:Te(r.onEnterFullscreen,["stop","prevent"]),title:t.$vuetify.locale.t("$vuetify.fullscreen")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[13]||(e[13]=[ie("mdi-arrow-expand-all")])),_:1})]),_:1},8,["onClick","title"]),K(Ce,{tile:"",icon:"",onClick:e[4]||(e[4]=Te(o=>r.toggleMessages(),["stop","prevent"])),variant:r.messagesValue()?"tonal":"plain",title:r.messagesValue()?t.$vuetify.locale.t("$vuetify.video_messages_disable"):t.$vuetify.locale.t("$vuetify.video_messages_enable")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[14]||(e[14]=[ie("mdi-message-text-outline")])),_:1})]),_:1},8,["variant","title"]),K(Ce,{disabled:!r.enableToggleMiniatures,tile:"",icon:"",onClick:e[5]||(e[5]=Te(o=>r.toggleMiniatures(),["stop","prevent"])),variant:r.miniaturesValue()?"tonal":"plain",title:r.miniaturesValue()?t.$vuetify.locale.t("$vuetify.video_miniatures_disable"):t.$vuetify.locale.t("$vuetify.video_miniatures_enable")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[15]||(e[15]=[ie("mdi-table-row")])),_:1})]),_:1},8,["disabled","variant","title"]),K(Ce,{disabled:t.videoIsGallery(),tile:"",icon:"","input-value":r.presenterValue(),onClick:r.presenterClick,variant:r.presenterValue()?"tonal":"plain",title:r.presenterValue()?t.$vuetify.locale.t("$vuetify.video_presenter_disable"):t.$vuetify.locale.t("$vuetify.video_presenter_enable")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[16]||(e[16]=[ie("mdi-presentation")])),_:1})]),_:1},8,["disabled","input-value","onClick","variant","title"]),K(xd,{class:"video-position-select",items:r.positionItems,density:"compact","hide-details":"","onUpdate:modelValue":[r.changeVideoPosition,e[6]||(e[6]=o=>t.chatStore.videoPosition=o)],modelValue:t.chatStore.videoPosition,variant:"plain"},null,8,["items","onUpdate:modelValue","modelValue"]),K(Ce,{variant:"plain",tile:"",icon:"",onClick:e[7]||(e[7]=o=>r.addVideoSource()),title:t.$vuetify.locale.t("$vuetify.source_add")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[17]||(e[17]=[ie("mdi-video-plus")])),_:1})]),_:1},8,["title"]),t.chatStore.showRecordStartButton?(F(),H(Ce,{key:4,variant:"plain",tile:"",icon:"",onClick:e[8]||(e[8]=o=>r.startRecord()),loading:t.chatStore.initializingStaringVideoRecord,title:t.$vuetify.locale.t("$vuetify.start_record")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[18]||(e[18]=[ie("mdi-record-rec")])),_:1})]),_:1},8,["loading","title"])):W("",!0),t.chatStore.showRecordStopButton?(F(),H(Ce,{key:5,variant:"plain",tile:"",icon:"",onClick:e[9]||(e[9]=o=>r.stopRecord()),loading:t.chatStore.initializingStoppingVideoRecord,title:t.$vuetify.locale.t("$vuetify.stop_record")},{default:U(()=>[K(ce,{size:"x-large",color:"red"},{default:U(()=>e[19]||(e[19]=[ie("mdi-stop")])),_:1})]),_:1},8,["loading","title"])):W("",!0),K(Ce,{variant:"plain",tile:"",icon:"",onClick:e[10]||(e[10]=o=>r.openSettings()),title:t.$vuetify.locale.t("$vuetify.video_settings")},{default:U(()=>[K(ce,{size:"x-large"},{default:U(()=>e[20]||(e[20]=[ie("mdi-cog")])),_:1})]),_:1},8,["title"])]),_:1})],2)}const rf=mt(nf,[["render",sf],["__scopeId","data-v-ff1c8a4e"]]),of={mixins:[$o()],computed:{...pt(ft)},props:["userName"],methods:{className(){return"presenter-context-menu"},onShowContextMenu(t,e){this.onShowContextMenuBase(t,e)},onCloseContextMenu(){this.onCloseContextMenuBase()},getContextMenuItems(){const t=[];return this.menuableItem&&(t.push({title:this.userName,icon:"mdi-close",action:()=>{this.onCloseContextMenu()},enabled:!0}),this.chatStore.pinnedTrackSid?t.push({title:this.$vuetify.locale.t("$vuetify.unpin_video"),icon:"mdi-pin-off-outline",action:()=>{T.emit(Si)},enabled:!0}):t.push({title:this.$vuetify.locale.t("$vuetify.pin_video"),icon:"mdi-pin",action:()=>{T.emit(Ii,this.menuableItem.getPresenterVideoStreamId())},enabled:!0})),t}}};function af(t,e,i,n,s,r){return F(),H(Qo,{attach:"#video-splitpanes",class:be(r.className()),"model-value":t.showContextMenu,transition:!1,"open-on-click":!1,"open-on-focus":!1,"open-on-hover":!1,"open-delay":0,"close-delay":0,"close-on-back":!1,"onUpdate:modelValue":t.onUpdate},{default:U(()=>[K(Jo,null,{default:U(()=>[(F(!0),ae(Xt,null,hn(r.getContextMenuItems(),(o,a)=>(F(),H(Yo,{key:a,onClick:o.action,disabled:!o.enabled},{prepend:U(()=>[o.icon?(F(),H(ce,{key:0,color:o.iconColor},{default:U(()=>[ie(ve(o.icon),1)]),_:2},1032,["color"])):W("",!0)]),title:U(()=>[ie(ve(o.title),1)]),_:2},1032,["onClick","disabled"]))),128))]),_:1})]),_:1},8,["class","model-value","onUpdate:modelValue"])}const cf=mt(of,[["render",af]]),Qn="first",Fo="second",df="last",lf="video-component-wrapper-position-horizontal",uf="video-component-wrapper-position-vertical",hf="video-component-wrapper-position-gallery",Vo="videoPanelSizes",mf=()=>({presenterPane:80}),Yn="#video-splitpanes",Xn="--splitter-h-display",pf={mixins:[Nd(),Di(),qc()],props:["chatId"],data(){return{room:null,videoContainerDiv:null,userVideoComponents:new Map,inRestarting:!1,presenterData:null,presenterVideoMute:!1,presenterAudioMute:!0,showControls:!0,localTrackDrawn:!1,localTrackCreatedAndPublished:!1,finishedConnectingToRoom:!1}},methods:{getNewId(){return Xd()},setUserVideoWrapperClass(t,e,i){e?t.className=lf:i?t.className=hf:t.className=uf},createComponent(t,e,i,n){const s=Jd(Zp,{id:i,localVideoProperties:n});s.config.globalProperties.isMobile=()=>Zo(),s.use(Qd),s.use(Yd);const r=document.createElement("div");this.setUserVideoWrapperClass(r,this.videoIsHorizontal(),this.videoIsGallery()),e==Qn?this.insertChildAtIndex(this.videoContainerDiv,r,0):e==df?this.videoContainerDiv.append(r):e==Fo&&this.insertChildAtIndex(this.videoContainerDiv,r,1);const o=s.mount(r);return this.addComponentForUser(t,{component:o,app:s,containerEl:r}),o},insertChildAtIndex(t,e,i){i||(i=0),i>=t.children.length?t.appendChild(e):t.insertBefore(e,t.children[i])},videoPublicationIsPresent(t,e){return!!e.filter(i=>i.getVideoStreamId()==t.trackSid).length},audioPublicationIsPresent(t,e){return!!e.filter(i=>i.getAudioStreamId()==t.trackSid).length},drawNewComponentOrInsertIntoExisting(t,e,i,n){try{const s=JSON.parse(t.metadata),o=(n?"local-":"remote-")+this.getNewId(),a=t.identity,c=this.getByUser(a).map(u=>u.component),d=c.filter(u=>!u.getVideoStreamId()),l=c.filter(u=>!u.getAudioStreamId());for(const u of e)if(u.kind=="video"){if(console.debug("Processing video track",u),this.videoPublicationIsPresent(u,c)){console.debug("Skipping video",u);continue}let h=d.length?d[0]:null;console.debug("candidatesWithoutVideo",d,"candidateToAppendVideo",h),h||(h=this.createComponent(a,i,o,n));const p=u&&!u.isMuted;u.isSubscribed||console.warn("Video track is not subscribed"),h.setVideoStream(u,p),console.log("Video track was set",u.trackSid,"to",h.getId()),h.setUserName(s.login),h.setAvatar(s.avatar),h.setUserId(s.userId);const g=this.getDataForPresenter(h);this.updatePresenterIfNeed(g,!1),this.recalculateLayout();return}else if(u.kind=="audio"){if(console.debug("Processing audio track",u),this.audioPublicationIsPresent(u,c)){console.debug("Skipping audio",u);continue}let h=l.length?l[0]:null;console.debug("candidatesWithoutAudio",l,"candidateToAppendAudio",h),h||(h=this.createComponent(a,i,o,n));const p=u&&!u.isMuted;u.isSubscribed||console.warn("Audio track is not subscribed"),h.setAudioStream(u,p),console.log("Audio track was set",u.trackSid,"to",h.getId()),h.setUserName(s.login),h.setAvatar(s.avatar),h.setUserId(s.userId);const g=this.getDataForPresenter(h);this.updatePresenterIfNeed(g,!1),this.recalculateLayout();return}this.setError(e,"Unable to draw track");return}finally{n&&(this.localTrackDrawn=!0,this.updateInitializingVideoCall())}},updateInitializingVideoCall(){this.chatStore.initializingVideoCall=!(this.localTrackDrawn&&this.localTrackCreatedAndPublished&&this.finishedConnectingToRoom)},getPresenterPriority(t,e){if(!t)return-1;if(t.trackSid===this.chatStore.pinnedTrackSid)return 5;for(const i of this.room.localParticipant.getTrackPublications().values())if(i.trackSid===t.trackSid)return 0;switch(t.source){case"camera":return e?3:2;case"screen_share":return 4;default:return 1}},onPinVideo(t){console.log("pinning",t),this.chatStore.pinnedTrackSid=t,this.electNewPresenter()},onUnpinVideo(){this.chatStore.pinnedTrackSid=null,this.electNewPresenter()},doUnpinVideo(){T.emit(Si)},detachPresenter(){var t,e;this.presenterData&&((e=(t=this.presenterData.videoStream)==null?void 0:t.videoTrack)==null||e.detach(this.$refs.presenterRef),this.presenterData=null)},updatePresenter(t){var e;t!=null&&t.videoStream&&(this.detachPresenter(),(e=t.videoStream.videoTrack)==null||e.attach(this.$refs.presenterRef),this.presenterData=t,this.updatePresenterVideoMute()),t!=null&&t.audioStream&&this.updatePresenterAudioMute()},updatePresenterIfNeed(t,e){var i,n,s,r,o,a,c,d,l,u,h,p,g,f,y,S;this.chatStore.presenterEnabled&&this.canUsePresenter()&&(((i=t.videoStream)==null?void 0:i.trackSid)!=null&&((s=(n=this.presenterData)==null?void 0:n.videoStream)==null?void 0:s.trackSid)!==t.videoStream.trackSid&&this.getPresenterPriority(t.videoStream,e)>this.getPresenterPriority((r=this.presenterData)==null?void 0:r.videoStream)&&(this.detachPresenter(),this.updatePresenter(t)),((o=this.presenterData)==null?void 0:o.videoStream)!=null&&this.presenterData.audioStream==null&&t.audioStream&&((c=(a=this.presenterData)==null?void 0:a.videoStream)==null?void 0:c.trackSid)===((d=t.videoStream)==null?void 0:d.trackSid)&&(console.log("Appending an audio stream to the presenter"),this.presenterData.audioStream=t.audioStream),((l=this.presenterData)==null?void 0:l.audioStream)!=null&&this.presenterData.videoStream==null&&t.videoStream&&((h=(u=this.presenterData)==null?void 0:u.audioStream)==null?void 0:h.trackSid)===((p=t.audioStream)==null?void 0:p.trackSid)&&(console.log("Appending a video stream to the presenter"),this.presenterData.videoStream=t.videoStream),((f=(g=this.presenterData)==null?void 0:g.videoStream)==null?void 0:f.trackSid)!=null&&t.videoStream!=null&&((S=(y=this.presenterData)==null?void 0:y.videoStream)==null?void 0:S.trackSid)===t.videoStream.trackSid&&e&&this.setSpeakingWithDefaultTimeout())},updatePresenterVideoMute(){this.presenterVideoMute=this.getPresenterVideoMute()},getPresenterVideoMute(){var e;const t=(e=this.presenterData)==null?void 0:e.videoStream;if(t){const i=t.videoTrack;if(i)return i.isMuted}return!0},updatePresenterAudioMute(){this.presenterAudioMute=this.getPresenterAudioMute()},getPresenterAudioMute(){var e;const t=(e=this.presenterData)==null?void 0:e.audioStream;if(t){const i=t.audioTrack;if(i)return i.isMuted}return!0},canUsePresenterPlain(t){return!this.videoIsGalleryPlain(t)},canUsePresenter(){const t=this.chatStore.videoPosition;return this.canUsePresenterPlain(t)},handleTrackUnsubscribed(t,e,i){console.log("handleTrackUnsubscribed",t),this.removeComponentIfNeed(i.identity,t)},handleLocalTrackUnpublished(t,e){const i=t.track;console.log("handleLocalTrackUnpublished sid=",i.sid,"kind=",i.kind),console.debug("handleLocalTrackUnpublished",t,"track",i),this.removeComponentIfNeed(e.identity,i),this.refreshLocalMuteAppBarButtons()},electNewPresenter(){const t=this.getAnyPrioritizedVideoData();t&&this.updatePresenter(t)},removeComponentIfNeed(t,e){var s;e.detach();let i=!1;e.sid===this.chatStore.pinnedTrackSid&&(this.chatStore.pinnedTrackSid=null,this.detachPresenter(),i=!0);let n=!1;for(const r of this.getByUser(t)){const o=r.component,a=r.app,c=r.containerEl;if(console.debug("For removal checking component=",o,"against",e),o.getVideoStreamId()==e.sid||o.getAudioStreamId()==e.sid){console.log("Setting null video for component=",o.getId()),this.chatStore.presenterEnabled&&((s=this.presenterData)!=null&&s.videoStream)&&this.presenterData.videoStream.trackSid==o.getVideoStreamId()&&(this.detachPresenter(),n=!0);try{console.log("Removing component=",o.getId()),a.unmount(),this.videoContainerDiv.removeChild(c),this.removeComponentForUser(t,r),console.log("Successfully removed component=",o.getId()),this.recalculateLayout();break}catch(d){console.debug("Something wrong on removing child",d,o.$el,this.videoContainerDiv)}}}(n||i)&&this.electNewPresenter()},handleActiveSpeakerChange(t){console.debug("handleActiveSpeakerChange",t);for(const e of t){const i=e.identity,n=[...e.audioTrackPublications.keys()],s=this.getByUser(i).map(r=>r.component);for(const r of s){const o=r.getAudioStreamId();if(n.includes(o))if(e.isSpeaking){r.setSpeakingWithDefaultTimeout();const a=this.getDataForPresenter(r);this.updatePresenterIfNeed(a,!0)}else r.resetSpeaking()}}},getDataForPresenter(t){const e=t.getUserId(),i=t.getUserName(),n=t.getVideoStream(),s=t.getAudioStream(),r=t.getAvatar();return{videoStream:n,audioStream:s,avatar:r,userId:e,userName:i}},handleDisconnect(){if(console.log("disconnected from room"),this.$route.name==Ei&&!this.inRestarting){console.log("Handling kick"),this.chatStore.leavingVideoAcceptableParam=!0;const t={name:Pi};yi(this.$route,this.$router,t)}},async setConfig(){await this.initServerData()},handleTrackMuted(t,e){var o,a;const i=e.identity,n=this.getByUser(i).map(c=>c.component),s=n.filter(c=>t.trackSid==c.getVideoStreamId()),r=n.filter(c=>t.trackSid==c.getAudioStreamId());for(const c of s)c.setDisplayVideoMute(t.isMuted),c.getVideoStreamId()&&((o=this.presenterData)!=null&&o.videoStream)&&c.getVideoStreamId()==this.presenterData.videoStream.trackSid&&(this.presenterVideoMute=t.isMuted);for(const c of r)c.setDisplayAudioMute(t.isMuted),c.getAudioStreamId()&&((a=this.presenterData)!=null&&a.audioStream)&&c.getAudioStreamId()==this.presenterData.audioStream.trackSid&&(this.presenterAudioMute=t.isMuted)},async stopLocalTracks(){for(const t of this.room.localParticipant.getTrackPublications().values())await this.room.localParticipant.unpublishTrack(t.track,!0)},async tryRestartVideoDevice(){this.inRestarting=!0,await this.stopLocalTracks(),await this.createLocalMediaTracks(Tr(),Pr()),T.emit(Cr),this.inRestarting=!1},async startRoom(t){try{await this.setConfig()}catch(n){this.setError(n,"Error during fetching config")}console.log("Creating room with dynacast",this.roomDynacast,"adaptiveStream",this.roomAdaptiveStream),this.room=new ti({adaptiveStream:this.roomAdaptiveStream,dynacast:this.roomDynacast}),this.room.on(I.TrackSubscribed,(n,s,r)=>{try{console.log("TrackPublished to room.name",this.room.name),console.debug("TrackPublished to room",this.room),this.drawNewComponentOrInsertIntoExisting(r,[s],this.getOnScreenPosition(s),null)}catch(o){this.setError(o,"Error during reacting on remote track published")}}).on(I.TrackUnsubscribed,this.handleTrackUnsubscribed).on(I.ActiveSpeakersChanged,this.handleActiveSpeakerChange).on(I.LocalTrackUnpublished,this.handleLocalTrackUnpublished).on(I.LocalTrackPublished,()=>{try{console.log("LocalTrackPublished to room.name",this.room.name),console.debug("LocalTrackPublished to room",this.room);const n={localParticipant:this.room.localParticipant},s=this.room.localParticipant.getTrackPublications();this.drawNewComponentOrInsertIntoExisting(this.room.localParticipant,s,Qn,n),this.refreshLocalMuteAppBarButtons()}catch(n){this.setError(n,"Error during reacting on local track published")}}).on(I.TrackMuted,this.handleTrackMuted).on(I.TrackUnmuted,this.handleTrackMuted).on(I.Reconnecting,()=>{this.setWarning("Reconnecting to video server")}).on(I.Reconnected,()=>{this.setOk(this.$vuetify.locale.t("$vuetify.video_successfully_reconnected"))}).on(I.Disconnected,this.handleDisconnect).on(I.SignalConnected,()=>{this.createLocalMediaTracks(Tr(),Pr())});const e=10,i={delay:200,maxAttempts:e};try{this.inRestarting=!0,await Kd(async n=>{const s=n.attemptNum+1;s>1&&this.setWarning("Connecting to the room, attempt "+s+" / "+e),this.room?(await this.room.connect($d()+"/api/livekit",t,{autoSubscribe:!0}),console.log("Connected to room",this.room.name),this.finishedConnectingToRoom=!0,this.updateInitializingVideoCall(),this.closeError()):console.warn("Didn't connect to room because it's null. It is ok when user leaves very fast.")},i),this.inRestarting=!1}catch(n){this.setError(n,"Error during connecting to room"),this.finishedConnectingToRoom=!0,this.updateInitializingVideoCall()}},getOnScreenPosition(t){return t.source=="screen_share"?Qn:Fo},refreshLocalMuteAppBarButtons(){this.onlyOneLocalTrackWith(this.room.localParticipant.identity)?this.chatStore.canShowMicrophoneButton=!0:this.chatStore.canShowMicrophoneButton=!1,this.onlyOneLocalTrackWith(this.room.localParticipant.identity,!0)?this.chatStore.canShowVideoButton=!0:this.chatStore.canShowVideoButton=!1},onlyOneLocalTrackWith(t,e){const n=this.getByUser(t).map(s=>s.component).filter(s=>s.isComponentLocal()?e?s.getVideoSource()==="screen_share"?!1:s.getVideoStreamId()!=null:s.getAudioStreamId()!=null:!1);return n.length==1?n[0]:null},async stopRoom(){console.log("Stopping room"),await this.room.disconnect(),this.room=null},onAddVideoSource({videoId:t,audioId:e,isScreen:i}){this.createLocalMediaTracks(t,e,i)},async createLocalMediaTracks(t,e,i){let n=[];try{const s=Dt[this.videoResolution].resolution,r=this.screenResolution===zd?void 0:Dt[this.screenResolution].resolution,o=qd(),a=Gd();if(!o&&!a&&!i)return console.warn("No media devices configured, joining without local tracks"),T.emit(Cr,{info:"Joined without media devices"}),this.localTrackCreatedAndPublished=!0,this.updateInitializingVideoCall(),Promise.resolve([]);console.info("Creating media tracks","isScreen",i,"audioId",e,"videoid",t,"videoResolution",s,"screenResolution",r),i?n=await Op({audio:!1,resolution:r}):n=await Oi({audio:o?{deviceId:e,echoCancellation:!0,noiseSuppression:!0}:!1,video:a?{deviceId:t,resolution:s}:!1})}catch(s){return this.setError(s,"Error during creating local tracks"),this.chatStore.initializingVideoCall=!1,Promise.reject("Error during creating local tracks")}try{for(const s of n){const r=!!i,o="track_"+s.kind+"__screen_"+r+"_"+this.getNewId(),a=r?this.screenSimulcast:this.videoSimulcast,c=this.codec===Wd?void 0:this.codec;console.log(`Publishing local ${s.kind} screen=${r} track with name ${o}, simulcast ${a}, codec ${c}`);const d=await this.room.localParticipant.publishTrack(s,{name:o,simulcast:a,videoCodec:c});s.kind=="audio"&&Hd&&await d.mute(),console.info("Published track sid=",s.sid," kind=",s.kind)}return this.localTrackCreatedAndPublished=!0,this.updateInitializingVideoCall(),Promise.resolve(!0)}catch(s){return this.setError(s,"Error during publishing local tracks"),this.chatStore.initializingVideoCall=!1,Promise.reject("Error during publishing local tracks")}},onAddScreenSource(){this.createLocalMediaTracks(null,null,!0)},onChangeVideoSource({videoId:t,audioId:e,purpose:i}){i===Vd&&(Bd(t),jd(e),this.tryRestartVideoDevice())},addComponentForUser(t,e){let i=this.userVideoComponents.get(t);i||(this.userVideoComponents.set(t,[]),i=this.userVideoComponents.get(t)),i.push(e)},removeComponentForUser(t,e){let i=this.userVideoComponents.get(t);if(i)for(let n=0;n<i.length;n++)i[n].component.getId()==e.component.getId()&&i.splice(n,1);i.length==0&&this.userVideoComponents.delete(t)},getByUser(t){let e=this.userVideoComponents.get(t);return e||(this.userVideoComponents.set(t,[]),e=this.userVideoComponents.get(t)),e},getAnyPrioritizedVideoData(){const t=[];for(const[e,i]of this.userVideoComponents)for(const n of i){const s=this.getDataForPresenter(n.component);s.videoStream&&s.videoStream.kind=="video"&&t.push(s)}return t.sort((e,i)=>this.getPresenterPriority(i.videoStream)-this.getPresenterPriority(e.videoStream)),t.length?t[0]:null},recalculateLayout(){const t=document.getElementById("video-container");if(t){const e=t.getBoundingClientRect().width,i=t.getBoundingClientRect().height,n=t.getElementsByTagName("video").length;if(e&&i&&n){const o=tf.largestRect(e,i,n,16,9);t.style.setProperty("--width",o.width+"px"),t.style.setProperty("--height",o.height+"px"),t.style.setProperty("--cols",o.cols+"")}}},onButtonsFullscreen(){var e;const t=(e=this.$refs.splVideo)==null?void 0:e.$el;t&&Fd()?document.exitFullscreen():t.requestFullscreen()},onMouseEnter(){this.isMobile()||(this.showControls=!0)},onMouseLeave(){this.isMobile()||(this.showControls=!1)},getLoadingMessage(){return Xo},onClick(){this.showControls=!this.showControls},onClickFromVideos(){this.shouldShowPresenter||this.onClick()},presenterPaneSize(){return this.chatStore.videoMiniaturesEnabled?this.getStored().presenterPane:100},miniaturesPaneSize(){return this.chatStore.videoMiniaturesEnabled?this.shouldShowPresenter?100-this.presenterPaneSize():100:0},prepareForStore(){const t=this.getStored(),e=this.$refs.splVideo.panes.map(i=>i.size);return this.shouldShowPresenter?t.presenterPane=e[0]:t.presenterPane=0,t},getStored(){const t=localStorage.getItem(Vo);return t?JSON.parse(t):mf()},saveToStored(t){localStorage.setItem(Vo,JSON.stringify(t))},onPanelResized(){this.$nextTick(()=>{this.saveToStored(this.prepareForStore())})},onPanelAdd(){this.$nextTick(()=>{const t=this.getStored();this.restorePanelsSize(t)})},onPanelRemove(){this.$nextTick(()=>{const t=this.getStored();this.restorePanelsSize(t)})},restorePanelsSize(t){this.chatStore.videoMiniaturesEnabled?this.shouldShowPresenter?this.$refs.splVideo.panes[0].size=t.presenterPane:this.$refs.splVideo.panes[0].size=100:this.$refs.splVideo.panes[0].size=100},onShowContextMenu(t,e){this.$refs.contextMenuRef.onShowContextMenu(t,e)},getPresenterVideoStreamId(){var t;return(t=this.presenterData)==null?void 0:t.videoStream.trackSid}},computed:{...pt(ft),splitpanesIsHorizontal(){return this.videoIsHorizontal()||this.videoIsGallery()},videoContainerClass(){return this.videoIsHorizontal()?"video-container-position-horizontal":this.videoIsGallery()?"video-container-position-gallery":"video-container-position-vertical"},paneVideoContainerClass(){return this.videoIsHorizontal()||this.videoIsGallery()?"pane-videos-horizontal":this.videoIsVertical()?"pane-videos-vertical":null},presenterPaneClass(){return this.videoIsHorizontal()?"pane-presenter-horizontal":this.isMobile()?["pane-presenter-vertical","pane-presenter-vertical-mobile"]:"pane-presenter-vertical"},shouldShowPresenter(){return this.chatStore.presenterEnabled&&!this.videoIsGallery()},presenterAvatarIsSet(){var t;return Be((t=this.presenterData)==null?void 0:t.avatar)},presenterUserName(){var t;return(t=this.presenterData)==null?void 0:t.userName}},components:{UserVideoContextMenu:Gc,Splitpanes:ea,Pane:ta,VideoButtons:rf,PresenterContextMenu:cf},watch:{"chatStore.videoPosition":{handler:function(t,e){if(this.videoContainerDiv){const i=this.videoIsHorizontalPlain(t),n=this.videoIsGalleryPlain(t);for(const s of this.videoContainerDiv.children)this.setUserVideoWrapperClass(s,i,n);this.canUsePresenterPlain(t)&&this.chatStore.presenterEnabled&&this.$nextTick(()=>{this.electNewPresenter()}),n&&setTimeout(()=>{this.recalculateLayout()},300)}}},"chatStore.presenterEnabled":{handler:function(t,e){this.videoContainerDiv&&(It(Yn,Xn,this.chatStore.videoMiniaturesEnabled),t?this.$nextTick(()=>{this.electNewPresenter()}):this.detachPresenter())}},"chatStore.showDrawer":{handler:function(t,e){setTimeout(()=>{this.recalculateLayout()},300)}},"chatStore.localMicrophoneEnabled":{handler:function(t,e){const i=this.onlyOneLocalTrackWith(this.room.localParticipant.identity);i?i.doMuteAudio(!t,!0):this.chatStore.canShowMicrophoneButton=!1}},"chatStore.localVideoEnabled":{handler:function(t,e){const i=this.onlyOneLocalTrackWith(this.room.localParticipant.identity,!0);i?i.doMuteVideo(!t,!0):this.chatStore.canShowVideoButton=!1}},"chatStore.videoMiniaturesEnabled":{handler:function(t,e){It(Yn,Xn,t)}}},created(){this.recalculateLayout=Hs(this.recalculateLayout)},async mounted(){this.initPositionAndPresenter();const t=Ud();It(Yn,Xn,t),this.chatStore.videoMiniaturesEnabled=t,this.chatStore.setCallStateInCall(),this.chatStore.initializingVideoCall=!0,this.isMobile()||(this.chatStore.showDrawerPrevious=this.chatStore.showDrawer,this.chatStore.showDrawer=!1);const e=await se.put(`/api/video/${this.chatId}/dial/enter`,null,{params:{tokenId:this.chatStore.videoTokenId}});this.chatStore.videoTokenId=e.data.tokenId,!this.chatStore.showRecordStopButton&&this.chatStore.canMakeRecord&&(this.chatStore.showRecordStartButton=!0,this.chatStore.showRecordStopButton=!1),T.on(yr,this.onAddVideoSource),T.on(ls,this.onAddScreenSource),T.on(Sr,this.tryRestartVideoDevice),T.on(kr,this.onChangeVideoSource),T.on(Ii,this.onPinVideo),T.on(Si,this.onUnpinVideo),this.chatStore.searchType=fi,window.addEventListener("resize",this.recalculateLayout),this.videoContainerDiv=document.getElementById("video-container"),this.startRoom(e.data.token)},async beforeUnmount(){se.put(`/api/video/${this.chatId}/dial/exit`,null,{params:{tokenId:this.chatStore.videoTokenId}}),this.detachPresenter(),await this.stopLocalTracks(),await this.stopRoom(),console.log("Cleaning videoContainerDiv"),this.videoContainerDiv=null,this.inRestarting=!1,this.chatStore.canShowMicrophoneButton=!1,this.localTrackDrawn=!1,this.localTrackCreatedAndPublished=!1,this.finishedConnectingToRoom=!1,this.isMobile()||(this.chatStore.showDrawer=this.chatStore.showDrawerPrevious,this.chatStore.showDrawerPrevious=!1),this.chatStore.videoChatUsersCount=0,this.chatStore.showRecordStartButton=!1,this.chatStore.initializingStaringVideoRecord=!1,this.chatStore.initializingStoppingVideoRecord=!1,this.chatStore.pinnedTrackSid=null,this.chatStore.videoTokenId=null,this.chatStore.setCallStateReady(),window.removeEventListener("resize",this.recalculateLayout),T.off(yr,this.onAddVideoSource),T.off(ls,this.onAddScreenSource),T.off(Sr,this.tryRestartVideoDevice),T.off(kr,this.onChangeVideoSource),T.off(Ii,this.onPinVideo),T.off(Si,this.onUnpinVideo)}},ff=["src"];function gf(t,e,i,n,s,r){const o=Re("VideoButtons"),a=Re("PresenterContextMenu"),c=Re("pane"),d=Re("splitpanes");return F(),ae(Xt,null,[K(d,{ref:"splVideo",id:"video-splitpanes",class:"default-theme","dbl-click-splitter":!1,horizontal:r.splitpanesIsHorizontal,onResize:e[5]||(e[5]=l=>r.onPanelResized(l)),onPaneAdd:e[6]||(e[6]=l=>r.onPanelAdd(l)),onPaneRemove:e[7]||(e[7]=l=>r.onPanelRemove(l))},{default:U(()=>[r.shouldShowPresenter?(F(),H(c,{key:0,size:r.presenterPaneSize(),class:be(r.presenterPaneClass)},{default:U(()=>{var l,u,h;return[ge("div",{class:"video-presenter-container-element",onContextmenu:e[3]||(e[3]=Te(p=>r.onShowContextMenu(p,this),["stop"]))},[Pt(ge("video",{onClick:e[0]||(e[0]=Te(p=>r.onClick(),["self"])),class:"video-presenter-element",ref:"presenterRef"},null,512),[[Et,!s.presenterVideoMute||!r.presenterAvatarIsSet]]),Pt(ge("img",{onClick:e[1]||(e[1]=Te(p=>r.onClick(),["self"])),class:"video-presenter-element",src:(l=s.presenterData)==null?void 0:l.avatar},null,8,ff),[[Et,r.presenterAvatarIsSet&&s.presenterVideoMute]]),ge("p",{class:be([t.speaking?"presenter-element-caption-speaking":"","presenter-element-caption","inline-caption-base"])},[ie(ve((u=s.presenterData)!=null&&u.userName?(h=s.presenterData)==null?void 0:h.userName:r.getLoadingMessage())+" ",1),s.presenterAudioMute?(F(),H(ce,{key:0},{default:U(()=>e[8]||(e[8]=[ie("mdi-microphone-off")])),_:1})):W("",!0)],2),t.isMobile()?W("",!0):Pt((F(),H(o,{key:0,onRequestFullScreen:r.onButtonsFullscreen},null,8,["onRequestFullScreen"])),[[Et,s.showControls]]),K(a,{ref:"contextMenuRef",userName:r.presenterUserName},null,8,["userName"]),t.chatStore.pinnedTrackSid?(F(),H(Ce,{key:1,class:"presenter-unpin-button",onClick:e[2]||(e[2]=p=>r.doUnpinVideo()),icon:"mdi-pin-off-outline",rounded:"0",title:t.$vuetify.locale.t("$vuetify.unpin_video")},null,8,["title"])):W("",!0)],32)]}),_:1},8,["size","class"])):W("",!0),K(c,{class:be(r.paneVideoContainerClass),size:r.miniaturesPaneSize()},{default:U(()=>[K(Zd,{cols:"12",class:be(["ma-0 pa-0",r.videoContainerClass]),id:"video-container",onClick:e[4]||(e[4]=l=>r.onClickFromVideos())},null,8,["class"]),!t.isMobile()&&!r.shouldShowPresenter?Pt((F(),H(o,{key:0,onRequestFullScreen:r.onButtonsFullscreen},null,8,["onRequestFullScreen"])),[[Et,s.showControls]]):W("",!0)]),_:1},8,["class","size"])]),_:1},8,["horizontal"]),t.isMobile()?Pt((F(),H(o,{key:0,onRequestFullScreen:r.onButtonsFullscreen},null,8,["onRequestFullScreen"])),[[Et,s.showControls]]):W("",!0)],64)}const vf=mt(pf,[["render",gf],["__scopeId","data-v-86e90607"]]),bf=tl({id:String,interactive:Boolean,text:String,...al(cl({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:!1}),["absolute","persistent"])},"VTooltip"),yf=el()({name:"VTooltip",props:bf(),emits:{"update:modelValue":t=>!0},setup(t,e){let{slots:i}=e;const n=il(t,"modelValue"),{scopeId:s}=nl(),r=sl(),o=_e(()=>t.id||`v-tooltip-${r}`),a=Tt(),c=_e(()=>t.location.split(" ").length>1?t.location:t.location+" center"),d=_e(()=>t.origin==="auto"||t.origin==="overlap"||t.origin.split(" ").length>1||t.location.split(" ").length>1?t.origin:t.origin+" center"),l=_e(()=>t.transition?t.transition:n.value?"scale-transition":"fade-transition"),u=_e(()=>Er({"aria-describedby":o.value},t.activatorProps));return rl(()=>{const h=Ir.filterProps(t);return K(Ir,Er({ref:a,class:["v-tooltip",{"v-tooltip--interactive":t.interactive},t.class],style:t.style,id:o.value},h,{modelValue:n.value,"onUpdate:modelValue":p=>n.value=p,transition:l.value,absolute:!0,location:c.value,origin:d.value,persistent:!0,role:"tooltip",activatorProps:u.value,_disableGlobalStack:!0},s),{activator:i.activator,default:function(){var y;for(var p=arguments.length,g=new Array(p),f=0;f<p;f++)g[f]=arguments[f];return((y=i.default)==null?void 0:y.call(i,...g))??t.text}})}),ol({},a)}}),$=t=>{var e;return(e=t.data)==null?void 0:e.chatEvents};let Bo;const jo="panelSizes",Fi=Zo()?"#central-splitpanes":"#root-splitpanes",Vi="--splitter-v-display",Sf=()=>({topPane:60,leftPane:20,rightPane:70,bottomPane:15,bottomPaneBig:60}),kf={mixins:[ll(),Di(),Ho(Dl),Ko(),ul("userStatusInChatViewTetATet")],data(){return{pinnedPromoted:null,pinnedPromotedKey:+new Date,writingUsers:[],showTooltip:!0,broadcastMessage:null,initialLoaded:!1,chatEventsSubscription:null,canWriteMessage:!0,initialized:!1,chatEventsSubscribed:!1}},components:{ChatVideo:vf,Splitpanes:ea,Pane:ta,ChatList:xl,MessageList:au,MessageEdit:dl},computed:{...pt(ft),chatId(){return this.$route.params.id},chatDtoIsReady(){return!!this.chatStore.chatDto.id}},methods:{onProfileSet(){return this.getInfo(this.chatId).then(()=>{this.chatStore.showCallManagement=!0,this.chatEventsSubscribed||(this.chatEventsSubscribed=!0,this.chatEventsSubscription.graphQlSubscribe())})},async doInitialize(){this.initialized||(this.initialized=!0,await this.onProfileSet())},onLogout(){this.partialReset(!0),this.initialLoaded=!1,this.chatEventsSubscription.graphQlUnsubscribe()},doUninitialize(){this.initialized&&(this.onLogout(),this.initialized=!1)},fetchAndSetChat(t){return se.get(`/api/chat/${t}`,{signal:this.requestAbortController.signal}).then(e=>e.status==205?se.put(`/api/chat/${t}/join`,null,{signal:this.requestAbortController.signal}).then(i=>se.get(`/api/chat/${t}`,{signal:this.requestAbortController.signal}).then(n=>this.processNormalInfoResponse(n))):e.status==204?(this.goToChatList(),this.setWarning(this.$vuetify.locale.t("$vuetify.chat_not_found")),Promise.reject()):this.processNormalInfoResponse(e))},processNormalInfoResponse(t){const e=t.data;return console.log("Got info about chat in ChatView, chatId=",this.chatId,e),this.commonChatEdit(e),this.chatStore.tetATet=e.tetATet,this.chatStore.setChatDto(e),this.initialLoaded=!0,this.canWriteMessage=e.canWriteMessage,Promise.resolve(e)},setParticipantsFields(t){this.chatStore.title=t.name,this.chatStore.titleStrike=Go(t),Or(t.name),this.chatStore.avatar=t.avatar},commonChatEdit(t){this.setParticipantsFields(t),this.chatStore.chatUsersCount=t.participantsCount,this.chatStore.showChatEditButton=t.canEdit,this.chatStore.canBroadcastTextMessage=t.canBroadcast,t.blog?this.chatStore.showGoToBlogButton=this.chatId:this.chatStore.showGoToBlogButton=null,this.isRealTetATet(t)&&(this.chatStore.oppositeUserLastSeenDateTime=t.lastSeenDateTime)},isRealTetATet(t){return!!(t.tetATet&&t.participantsCount==2)},fetchPromotedMessage(t){se.get(`/api/chat/${t}/message/pin/promoted`,{signal:this.requestAbortController.signal}).then(e=>{e.status!=204?(this.pinnedPromoted=e.data,this.pinnedPromotedKey++):this.pinnedPromoted=null})},hasTetATetUserStatusSubscriptions(){return!!this.subscriptionElements.length},getInfo(t){return this.updateLastUpdateDateTime(),this.fetchAndSetChat(t).then(e=>(this.isRealTetATet(e)&&(this.hasTetATetUserStatusSubscriptions()||(console.info("Subscribing onto tet-a-tet online"),this.graphQlUserStatusSubscribe()),this.requestTetAtTetStatuses()),this.fetchPromotedMessage(t),se.get(`/api/video/${t}/users`,{signal:this.requestAbortController.signal}).then(i=>i.data).then(i=>{T.emit(Ml,i)}),Promise.resolve())).then(()=>(se.get(`/api/video/${t}/record/status`,{signal:this.requestAbortController.signal}).then(({data:e})=>{this.chatStore.canMakeRecord=e.canMakeRecord,e.canMakeRecord&&e.recordInProcess&&(this.chatStore.showRecordStopButton=!0)}),Promise.resolve()))},getTetAtetOppositeParticipant(){return this.chatStore.chatDto.participantIds.find(t=>t!==this.chatStore.currentUser.id)},requestTetAtTetStatuses(){this.$nextTick(()=>{if(this.chatStore.currentUser&&this.chatId){const t=this.getTetAtetOppositeParticipant();this.triggerUsesStatusesEvents(t,this.requestAbortController.signal)}})},getUserIdsSubscribeTo(){return[this.getTetAtetOppositeParticipant()]},onUserStatusChanged(t){const e=this.getTetAtetOppositeParticipant();t&&e&&t.forEach(i=>{i.online!=null&&e==i.userId&&(i.online?(this.chatStore.oppositeUserOnline=!0,this.chatStore.oppositeUserLastSeenDateTime=null):(this.chatStore.oppositeUserOnline=!1,this.chatStore.oppositeUserLastSeenDateTime=i.lastSeenDateTime)),i.isInVideo!==null&&e==i.userId&&(i.isInVideo?this.chatStore.oppositeUserInVideo=!0:this.chatStore.oppositeUserInVideo=!1)})},goToChatList(){this.$router.push({name:wn})},getGraphQlSubscriptionQuery(){return`
                                fragment DisplayMessageDtoFragment on DisplayMessageDto {
                                  id
                                  text
                                  chatId
                                  ownerId
                                  createDateTime
                                  editDateTime
                                  owner {
                                    id
                                    login
                                    avatar
                                    shortInfo
                                    loginColor
                                    additionalData {
                                      enabled,
                                      expired,
                                      locked,
                                      confirmed,
                                      roles,
                                    }
                                  }
                                  canEdit
                                  canDelete
                                  fileItemUuid
                                  embedMessage {
                                    id
                                    chatId
                                    chatName
                                    text
                                    owner {
                                      id
                                      login
                                      avatar
                                      shortInfo
                                      loginColor
                                      additionalData {
                                        enabled,
                                        expired,
                                        locked,
                                        confirmed,
                                        roles,
                                      }
                                    }
                                    embedType
                                    isParticipant
                                  }
                                  pinned
                                  blogPost
                                  pinnedPromoted
                                  reactions {
                                    count
                                    users {
                                      id
                                      login
                                      avatar
                                      shortInfo
                                      loginColor
                                      additionalData {
                                        enabled,
                                        expired,
                                        locked,
                                        confirmed,
                                        roles,
                                      }
                                    }
                                    reaction
                                  }
                                  published
                                  canPublish
                                  canPin
                                }

                                subscription{
                                  chatEvents(chatId: ${this.chatId}) {
                                    eventType
                                    messageEvent {
                                      ...DisplayMessageDtoFragment
                                    }
                                    messageDeletedEvent {
                                      id
                                      chatId
                                    }
                                    messageBroadcastEvent {
                                      login
                                      userId
                                      text
                                    }
                                    previewCreatedEvent {
                                      id
                                      url
                                      previewUrl
                                      aType
                                      correlationId
                                    }
                                    participantsEvent {
                                      id
                                      login
                                      avatar
                                      admin
                                      shortInfo
                                      loginColor
                                      additionalData {
                                        enabled,
                                        expired,
                                        locked,
                                        confirmed,
                                        roles,
                                      }
                                    }
                                    promoteMessageEvent {
                                      count
                                      message {
                                        id
                                        text
                                        chatId
                                        ownerId
                                        owner {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        pinnedPromoted
                                        createDateTime
                                        canPin
                                      }
                                    }
                                    publishedMessageEvent {
                                      count
                                      message {
                                        id
                                        text
                                        chatId
                                        ownerId
                                        owner {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        createDateTime
                                        canPublish
                                      }
                                    }
                                    fileEvent {
                                      fileInfoDto {
                                        id
                                        filename
                                        url
                                        publishedUrl
                                        previewUrl
                                        size
                                        canDelete
                                        canEdit
                                        canShare
                                        lastModified
                                        ownerId
                                        owner {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        canPlayAsVideo
                                        canShowAsImage
                                        canPlayAsAudio
                                        fileItemUuid
                                        correlationId
                                        previewable
                                        aType
                                      }
                                    }
                                    reactionChangedEvent {
                                      messageId
                                      reaction {
                                        count
                                        users {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        reaction
                                      }
                                    }
                                  }
                                }
                `},onNextSubscriptionElement(t){if($(t).eventType==="message_created"){const e=$(t).messageEvent;T.emit(es,e)}else if($(t).eventType==="message_deleted"){const e=$(t).messageDeletedEvent;T.emit(ts,e)}else if($(t).eventType==="message_edited"){const e=$(t).messageEvent;T.emit(is,e)}else if($(t).eventType==="user_broadcast"){const e=$(t).messageBroadcastEvent;T.emit(In,e)}else if($(t).eventType==="preview_created"){const e=$(t).previewCreatedEvent;T.emit(Cl,e)}else if($(t).eventType==="participant_added"){const e=$(t).participantsEvent;T.emit(Tl,e)}else if($(t).eventType==="participant_deleted"){const e=$(t).participantsEvent;T.emit(Rn,e)}else if($(t).eventType==="participant_edited"){const e=$(t).participantsEvent;T.emit(Pl,e)}else if($(t).eventType==="pinned_message_promote"){const e=$(t).promoteMessageEvent;T.emit(Tn,e)}else if($(t).eventType==="pinned_message_unpromote"){const e=$(t).promoteMessageEvent;T.emit(Pn,e)}else if($(t).eventType==="pinned_message_edit"){const e=$(t).promoteMessageEvent;T.emit(En,e)}else if($(t).eventType==="published_message_add"){const e=$(t).publishedMessageEvent;T.emit(El,e)}else if($(t).eventType==="published_message_remove"){const e=$(t).publishedMessageEvent;T.emit(Il,e)}else if($(t).eventType==="published_message_edit"){const e=$(t).publishedMessageEvent;T.emit(Rl,e)}else if($(t).eventType==="file_created"){const e=$(t).fileEvent;T.emit(as,e)}else if($(t).eventType==="file_removed"){const e=$(t).fileEvent;T.emit(wl,e)}else if($(t).eventType==="file_updated"){const e=$(t).fileEvent;T.emit(_l,e)}else if($(t).eventType==="reaction_changed"){const e=$(t).reactionChangedEvent;T.emit(ns,e)}else if($(t).eventType==="reaction_removed"){const e=$(t).reactionChangedEvent;T.emit(ss,e)}else $(t).eventType==="messages_reload"&&T.emit(os)},getPinnedPromotedRoute(t){let e="";return this.isVideoRoute()&&(e=Ol),kl+"/"+t.chatId+e+Gt+t.id},onClickPinnedPromoted(t){const i={name:this.isVideoRoute()?Ei:Pi,params:{id:t.chatId},hash:Gt+t.id};this.chatStore.canShowPinnedLink&&this.$router.push(i)},onPinnedMessagePromoted(t){this.pinnedPromoted=t.message,this.pinnedPromotedKey++},onPinnedMessageUnpromoted(t){this.pinnedPromoted&&this.pinnedPromoted.id==t.message.id&&(this.pinnedPromoted=null)},onPinnedMessageChanged(t){this.pinnedPromoted&&this.pinnedPromoted.id==t.message.id&&this.onPinnedMessagePromoted(t)},onFocus(){this.chatStore.currentUser&&this.chatId&&this.getInfo(this.chatId)},onUserBroadcast(t){console.log("onUserBroadcast",t);const e=t.text;e&&e.length>0?(this.showTooltip=!0,this.broadcastMessage=t.text):this.broadcastMessage=null},onChatChange(t){t.id==this.chatId&&(this.commonChatEdit(t),this.chatStore.setChatDto(t))},onChatDelete(t){t.id==this.chatId&&this.$router.push({name:wn})},onParticipantDeleted(t){if(t.find(e=>e.id==this.chatStore.currentUser.id)){const e={name:wn};yi(this.$route,this.$router,e)}},isAllowedVideo(){var t,e;return this.chatStore.currentUser&&this.$route.name==Ei&&((e=(t=this.chatStore.chatDto)==null?void 0:t.participantIds)==null?void 0:e.length)},isAllowedChatList(){return this.chatStore.currentUser&&this.initialLoaded},onWsRestoredRefresh(){this.getInfo(this.chatId)},partialReset(t){this.chatEventsSubscribed=!1,this.chatStore.resetChatDto(),this.chatStore.videoChatUsersCount=0,this.chatStore.canMakeRecord=!1,this.pinnedPromoted=null,this.chatStore.canBroadcastTextMessage=!1,this.chatStore.showRecordStartButton=!1,this.chatStore.showRecordStopButton=!1,this.chatStore.showChatEditButton=!1,t||(this.chatStore.titleStrike=!1,this.chatStore.title=null,Or(null)),this.chatStore.avatar=null,this.chatStore.showGoToBlogButton=null,this.chatStore.chatUsersCount=0,this.chatStore.oppositeUserLastSeenDateTime=null,this.chatStore.oppositeUserInVideo=!1,this.chatStore.oppositeUserOnline=!1,this.chatStore.showCallManagement=!1,this.hasTetATetUserStatusSubscriptions()&&(console.info("Unsubscribing from tet-a-tet online"),this.graphQlUserStatusUnsubscribe())},onChatDialStatusChange(t){var e;if((e=this.chatStore.chatDto)!=null&&e.tetATet&&t.chatId==this.chatId)for(const i of t.dials)this.chatStore.currentUser.id!=i.userId&&(this.chatStore.shouldPhoneBlink=Sl(i.status))},openNewMessageDialog(){T.emit(ds,{dto:null,actionType:yl})},messageListPaneClass(){const t=[];return t.push("message-pane"),this.isMobile()&&t.push("message-pane-mobile"),t},showRightPane(){return!this.isMobile()&&this.isAllowedVideo()},showLeftPane(){return this.shouldShowChatList()},showBottomPane(){return!this.isMobile()},leftPaneSize(){return this.getStored().leftPane},rightPaneSize(){return this.chatStore.videoMessagesEnabled?this.getStored().rightPane:100},showTopPane(){return this.isMobile()&&this.isAllowedVideo()},topPaneSize(){return this.chatStore.videoMessagesEnabled?this.getStored().topPane:100},centralPaneSize(){return this.isMobile()?100:this.showRightPane()?100-this.rightPaneSize():this.showLeftPane()?100-this.leftPaneSize():100},bottomPaneSize(){return this.chatStore.isEditingBigText?this.getStored().bottomPaneBig:this.getStored().bottomPane},messageListPaneSize(){return this.isMobile()?this.showTopPane()?100-this.topPaneSize():100:this.showBottomPane()?100-this.bottomPaneSize():100},getStored(){const t=localStorage.getItem(jo);return t?JSON.parse(t):Sf()},saveToStored(t){localStorage.setItem(jo,JSON.stringify(t))},prepareForStore(){const t=this.$refs.splOuter.panes.map(n=>n.size),e=this.$refs.splCentral.panes.map(n=>n.size),i=this.getStored();if(this.isMobile()){if(this.showTopPane()){const n=e[0];i.topPane=n}}else if(this.showLeftPane()&&(i.leftPane=t[0]),this.showRightPane()&&(i.rightPane=t[t.length-1]),this.showBottomPane()){const n=e[e.length-1];this.chatStore.isEditingBigText?i.bottomPaneBig=n:i.bottomPane=n}return i},restorePanelsSize(t){if(this.isMobile())this.showTopPane()&&(this.chatStore.videoMessagesEnabled?this.$refs.splCentral.panes[0].size=t.topPane:this.$refs.splCentral.panes[0].size=100);else if(this.showLeftPane()&&(this.$refs.splOuter.panes[0].size=t.leftPane),this.showRightPane()&&(this.chatStore.videoMessagesEnabled?this.$refs.splOuter.panes[this.$refs.splOuter.panes.length-1].size=t.rightPane:this.$refs.splOuter.panes[this.$refs.splOuter.panes.length-1].size=100),this.showBottomPane()){let e;this.chatStore.isEditingBigText?e=t.bottomPaneBig:e=t.bottomPane,this.$refs.splCentral.panes[this.$refs.splCentral.panes.length-1].size=e}},onPanelAdd(){var t;(t=this.$refs.chatVideoRef)==null||t.recalculateLayout(),this.isAllowedVideo()&&It(Fi,Vi,this.chatStore.videoMessagesEnabled),this.$nextTick(()=>{const e=this.getStored();this.restorePanelsSize(e)})},onPanelRemove(){var t;(t=this.$refs.chatVideoRef)==null||t.recalculateLayout(),this.isAllowedVideo()||It(Fi,Vi,!0),this.$nextTick(()=>{const e=this.getStored();this.restorePanelsSize(e)})},onPanelResized(){var t;(t=this.$refs.chatVideoRef)==null||t.recalculateLayout(),this.$nextTick(()=>{this.saveToStored(this.prepareForStore())})},scrollDown(){T.emit(rs)},scrollDownClass(){return this.isMobile()&&this.canWriteMessage?"new-fab-t":"new-fab-b"},onUserTyping(t){t.chatId==this.chatId&&(bl(this.writingUsers,t),this.chatStore.usersWritingSubtitleInfo=Dr(this.writingUsers,this.$vuetify))},onCoChattedParticipantChanged(t){if(this.chatStore.chatDto.tetATet&&this.chatStore.chatDto.participantIds.find(e=>e==t.id)){const e={name:t.login,additionalData:t.additionalData,avatar:t.avatar};this.setParticipantsFields(e),this.chatStore.setChatDto({...this.chatStore.chatDto,...e})}},openPinnedMessages(){T.emit(vl,{chatId:this.chatId})}},watch:{$route:{handler:function(t,e){cs(t)&&t.params.id!=e.params.id&&(console.debug("Chat id has been changed",e.params.id,"->",t.params.id),Be(t.params.id)&&(this.chatStore.incrementProgressCount(),this.partialReset(),this.onProfileSet().then(()=>{this.chatStore.decrementProgressCount()})))}},"chatStore.videoMessagesEnabled":{handler:function(t,e){It(Fi,Vi,t||!this.isAllowedVideo())}},"chatStore.chatDto.canWriteMessage":{handler:function(t,e){t!==void 0&&(this.canWriteMessage=t)}}},created(){},async mounted(){this.chatStore.titleStrike=!1,this.chatStore.title=`Chat #${this.chatId}`,this.chatStore.chatUsersCount=0,this.chatStore.isShowSearch=!0,this.chatStore.showChatEditButton=!1;const t=pl();It(Fi,Vi,t||!this.isAllowedVideo()),this.chatStore.videoMessagesEnabled=t,this.chatEventsSubscription=fl("chatEvents",this.getGraphQlSubscriptionQuery,this.setErrorSilent,this.onNextSubscriptionElement),T.on(en,this.doInitialize),T.on(tn,this.doUninitialize),T.on(Tn,this.onPinnedMessagePromoted),T.on(Pn,this.onPinnedMessageUnpromoted),T.on(En,this.onPinnedMessageChanged),T.on(Rr,this.onUserTyping),T.on(In,this.onUserBroadcast),T.on(wr,this.onChatChange),T.on(_r,this.onChatDelete),T.on(sn,this.onWsRestoredRefresh),T.on(Mr,this.onChatDialStatusChange),T.on(Rn,this.onParticipantDeleted),T.on(nn,this.onCoChattedParticipantChanged),this.chatStore.currentUser&&await this.doInitialize(),Bo=setInterval(()=>{this.writingUsers=gl(this.writingUsers),this.writingUsers.length==0?this.chatStore.usersWritingSubtitleInfo=null:this.chatStore.usersWritingSubtitleInfo=Dr(this.writingUsers,this.$vuetify)},500),this.installOnFocus()},beforeUnmount(){this.uninstallOnFocus(),this.doUninitialize(),T.off(en,this.doInitialize),T.off(tn,this.doUninitialize),T.off(Tn,this.onPinnedMessagePromoted),T.off(Pn,this.onPinnedMessageUnpromoted),T.off(En,this.onPinnedMessageChanged),T.off(Rr,this.onUserTyping),T.off(In,this.onUserBroadcast),T.off(wr,this.onChatChange),T.off(_r,this.onChatDelete),T.off(sn,this.onWsRestoredRefresh),T.off(Mr,this.onChatDialStatusChange),T.off(Rn,this.onParticipantDeleted),T.off(nn,this.onCoChattedParticipantChanged),this.chatStore.isShowSearch=!1,this.partialReset(),clearInterval(Bo),this.initialLoaded=!1,this.chatStore.isEditingBigText=!1,this.canWriteMessage=!0,this.chatEventsSubscription=null}},Cf=["innerHTML"],Tf=["title"],Pf=["href","innerHTML"],Ef={key:1};function If(t,e,i,n,s,r){const o=Re("ChatList"),a=Re("pane"),c=Re("ChatVideo"),d=Re("MessageList"),l=Re("MessageEdit"),u=Re("splitpanes");return F(),H(u,{ref:"splOuter",class:"default-theme",id:"root-splitpanes","dbl-click-splitter":!1,style:Ws(t.heightWithoutAppBar),onResize:e[7]||(e[7]=h=>r.onPanelResized(h)),onPaneAdd:e[8]||(e[8]=h=>r.onPanelAdd(h)),onPaneRemove:e[9]||(e[9]=h=>r.onPanelRemove(h))},{default:U(()=>[r.showLeftPane()?(F(),H(a,{key:0,size:r.leftPaneSize()},{default:U(()=>[r.isAllowedChatList()?(F(),H(o,{key:0,embedded:!0,ref:"chatListRef"},null,512)):W("",!0)]),_:1},8,["size"])):W("",!0),K(a,{style:{background:"white"},size:r.centralPaneSize()},{default:U(()=>[K(u,{ref:"splCentral",id:"central-splitpanes",class:"default-theme","dbl-click-splitter":!1,horizontal:"",onResize:e[4]||(e[4]=h=>r.onPanelResized(h)),onPaneAdd:e[5]||(e[5]=h=>r.onPanelAdd(h)),onPaneRemove:e[6]||(e[6]=h=>r.onPanelRemove(h))},{default:U(()=>[r.showTopPane()?(F(),H(a,{key:0,size:r.topPaneSize(),class:"video-top-pane"},{default:U(()=>[r.chatDtoIsReady?(F(),H(c,{key:0,chatId:r.chatId,ref:"chatVideoRef"},null,8,["chatId"])):W("",!0)]),_:1},8,["size"])):W("",!0),K(a,{class:be(r.messageListPaneClass()),size:r.messageListPaneSize()},{default:U(()=>[s.broadcastMessage?(F(),H(yf,{key:0,"model-value":s.showTooltip,activator:r.showBottomPane()?".message-edit-pane":".message-pane-mobile",location:"bottom center"},{default:U(()=>[ge("span",{innerHTML:s.broadcastMessage},null,8,Cf)]),_:1},8,["model-value","activator"])):W("",!0),s.pinnedPromoted?(F(),ae("div",{key:s.pinnedPromotedKey,class:"pinned-promoted",title:t.$vuetify.locale.t("$vuetify.goto_pinned_message")},[K(hl,{color:"red-lighten-4",elevation:"2",density:"compact"},{text:U(()=>[ge("a",{href:r.getPinnedPromotedRoute(s.pinnedPromoted),onClick:e[0]||(e[0]=Te(h=>r.onClickPinnedPromoted(s.pinnedPromoted),["prevent"])),class:"pinned-text",innerHTML:s.pinnedPromoted.text},null,8,Pf)]),append:U(()=>[K(Ce,{density:"compact",icon:"",rounded:"0",variant:"plain",title:t.$vuetify.locale.t("$vuetify.pinned_messages"),onClick:e[1]||(e[1]=Te(h=>r.openPinnedMessages(),["stop","prevent"]))},{default:U(()=>[K(ce,null,{default:U(()=>e[10]||(e[10]=[ie("mdi-view-list-outline")])),_:1})]),_:1},8,["title"])]),_:1})],8,Tf)):W("",!0),K(d,{isCompact:t.isVideoRoute()},null,8,["isCompact"]),t.chatStore.showScrollDown?(F(),H(Ce,{key:2,variant:"elevated",color:"primary",icon:"mdi-arrow-down-thick",class:be(r.scrollDownClass()),onClick:e[2]||(e[2]=h=>r.scrollDown())},null,8,["class"])):W("",!0),t.isMobile()&&s.canWriteMessage?(F(),H(Ce,{key:3,variant:"elevated",icon:"",color:"primary",class:"new-fab-b",onClick:e[3]||(e[3]=h=>r.openNewMessageDialog())},{default:U(()=>[K(ml,{color:"red",dot:"",location:"right top",overlap:"",bordered:"","offset-x":"-9","offset-y":"-9","model-value":t.chatStore.hasMessageEditingText()},{default:U(()=>[K(ce,null,{default:U(()=>[ie(ve(t.chatStore.isMessageEditing()?"mdi-lead-pencil":"mdi-plus-thick"),1)]),_:1})]),_:1},8,["model-value"])]),_:1})):W("",!0)]),_:1},8,["class","size"]),r.showBottomPane()?(F(),H(a,{key:1,class:"message-edit-pane d-flex flex-row justify-center align-center",size:r.bottomPaneSize(),style:{background:"white",color:"#3a3a3e"}},{default:U(()=>[s.canWriteMessage?(F(),H(l,{key:0,chatId:this.chatId},null,8,["chatId"])):(F(),ae("span",Ef,ve(t.$vuetify.locale.t("$vuetify.you_cannot_write_message")),1))]),_:1},8,["size"])):W("",!0)]),_:1},512)]),_:1},8,["size"]),r.showRightPane()?(F(),H(a,{key:1,size:r.rightPaneSize()},{default:U(()=>[r.chatDtoIsReady?(F(),H(c,{key:0,chatId:r.chatId,ref:"chatVideoRef"},null,8,["chatId"])):W("",!0)]),_:1},8,["size"])):W("",!0)]),_:1},8,["style"])}const Of=mt(kf,[["render",If],["__scopeId","data-v-6511de7e"]]);export{Of as default};
