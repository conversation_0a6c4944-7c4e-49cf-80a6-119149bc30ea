import{bJ as b,bK as S,bP as f,b as I,cT as p,cU as v,U as T,cV as u,T as w}from"./appMain-DAGYo8tP.js";const y=S({start:Boolean,end:Boolean,...v(),...p()},"VListItemAction"),F=b()({name:"VListItemAction",props:y(),setup(e,t){let{slots:s}=t;return f(()=>I(e.tag,{class:["v-list-item-action",{"v-list-item-action--start":e.start,"v-list-item-action--end":e.end},e.class],style:e.style},s)),{}}}),m="top",g="bottom",L=e=>({data(){return{items:[],observer:null,isFirstLoad:!0,scrollerDiv:null,aDirection:this.initialDirection(),scrollerProbeCurrent:0,scrollerProbePrevious:0,preservedScroll:0}},methods:{cssStr(t){return t.tagName.toLowerCase()+(t.id?"#"+t.id:"")+"."+Array.from(t.classList).join(".")},async reduceListIfNeed(){if(this.items.length>this.getMaxItemsLength())return this.$nextTick(()=>{this.isTopDirection()?this.reduceBottom():this.reduceTop(),console.log("Reduced to",this.getMaxItemsLength(),"in",e)})},reduceListAfterAdd(t){this.items.length>this.getMaxItemsLength()&&(t?this.reduceTop():this.reduceBottom(),console.log("Reduced after add to",this.getMaxItemsLength(),"in",e))},onScroll(t){this.onScrollCallback&&this.onScrollCallback(),this.scrollerProbePrevious=this.scrollerProbeCurrent,this.scrollerProbeCurrent=this.scrollerDiv.scrollTop,this.trySwitchDirection()},trySwitchDirection(){this.scrollerProbeCurrent!=0&&this.scrollerProbeCurrent>this.scrollerProbePrevious&&this.isTopDirection()?this.aDirection=g:this.scrollerProbeCurrent!=0&&this.scrollerProbePrevious>this.scrollerProbeCurrent&&!this.isTopDirection()&&(this.aDirection=m)},isTopDirection(){return this.aDirection===m},async restoreScroll(t){return this.$nextTick(()=>{const s=this.preservedScroll,r=this.scrollerSelector()+" #"+this.getItemId(s),i=document.querySelector(r);console.debug("Restored scroll to element id",s,"in",e,"selector",r,"element",i),i==null||i.scrollIntoView({behavior:"instant",block:t?"start":"end"}),this.afterScrollRestored&&this.afterScrollRestored(i)})},resetInfiniteScrollVars(){this.items=[],this.isFirstLoad=!0,this.aDirection=this.initialDirection(),this.scrollerProbePrevious=0,this.scrollerProbeCurrent=0,this.preservedScroll=null},async setNoScroll(){return this.$nextTick(()=>{u()&&this.scrollerDiv.classList.add("stop-scrolling")})},async unsetNoScroll(){return this.$nextTick(()=>{u()&&this.scrollerDiv.classList.remove("stop-scrolling")})},async initialLoad(){await this.$nextTick(()=>{this.scrollerDiv==null&&(this.scrollerDiv=document.querySelector(this.scrollerSelector()))}),await this.setNoScroll();const t=await this.load();await this.unsetNoScroll(),await this.$nextTick(),await this.onFirstLoad(t),this.isFirstLoad=!1},async loadTop(){console.log("going to load top in",e),this.saveScroll(!0),await this.setNoScroll(),await this.load(),await this.$nextTick(),await this.reduceListIfNeed(),await this.restoreScroll(!0),await this.unsetNoScroll()},async loadBottom(){console.log("going to load bottom in",e),this.saveScroll(!1),await this.setNoScroll(),await this.load(),await this.$nextTick(),await this.reduceListIfNeed(),await this.restoreScroll(!1),await this.unsetNoScroll()},isReady(){return this.scrollerDiv!=null},initScroller(){if(!this.isReady())throw"You have to invoke initialLoad() first";const t={root:this.scrollerDiv,rootMargin:"0px",threshold:0},r=T(async(i,l)=>{const a=i.map(o=>({entry:o,elementName:this.cssStr(o.target)})),n=a.filter(o=>o.entry.intersectionRatio>0&&o.elementName.includes(this.topElementSelector())),h=n.length?n[n.length-1]:null,c=a.filter(o=>o.entry.intersectionRatio>0&&o.elementName.includes(this.bottomElementSelector())),d=c.length?c[c.length-1]:null;console.log("Invoking callback in",e,a),this.items.length&&h&&h.entry.isIntersecting&&(console.debug("attempting to load top",this.isTopDirection(),"in",e),this.isTopDirection()&&await this.loadTop()),this.items.length&&d&&d.entry.isIntersecting&&(console.debug("attempting to load bottom",!this.isTopDirection(),"in",e),this.isTopDirection()||await this.loadBottom())},200,{leading:!1,trailing:!0});this.observer=new IntersectionObserver(r,t),this.observer.observe(document.querySelector(this.scrollerSelector()+" "+this.bottomElementSelector())),this.observer.observe(document.querySelector(this.scrollerSelector()+" "+this.topElementSelector()))},async destroyScroller(){return this.$nextTick(()=>{var t;(t=this.observer)==null||t.disconnect(),this.observer=null,this.scrollerDiv=null})},async installScroller(){return this.$nextTick(()=>{this.initScroller(),console.log("Scroller",e,"has been installed")})},async uninstallScroller(){await this.destroyScroller(),this.reset(),console.log("Scroller",e,"has been uninstalled")},async reloadItems(){await this.uninstallScroller(),await this.$nextTick(),this.updateLastUpdateDateTime&&this.updateLastUpdateDateTime(),await this.initialLoad(),await this.$nextTick(async()=>{await this.installScroller()})}}}),k=()=>({data(){return{startingFromItemIdTop:null,startingFromItemIdBottom:null,hasHashFromRoute:!1,loadedFromStoreHash:null}},computed:{highlightItemId(){return this.enableHashInRoute()&&this.isAppropriateHash(this.$route.hash)?this.getIdFromRouteHash(this.$route.hash):null}},methods:{getDefaultItemId(){return this.isTopDirection()?this.startingFromItemIdTop:this.startingFromItemIdBottom},initializeHashVariables(){this.enableHashInRoute()&&(this.hasHashFromRoute=w(this.highlightItemId)),this.loadedFromStoreHash=this.getPositionFromStore()},prepareHashesForRequest(){let e,t;return this.enableHashInRoute()&&this.hasHashFromRoute?(e=this.highlightItemId,t=!0):this.loadedFromStoreHash?(e=this.loadedFromStoreHash,t=!0):(e=this.getDefaultItemId(),t=!1),{startingFromItemId:e,hasHash:t}},async doScrollOnFirstLoad(){this.enableHashInRoute()&&this.highlightItemId?await this.scrollTo(this.convertLoadedFromRouteHash(this.highlightItemId)):this.loadedFromStoreHash?await this.scrollTo(this.convertLoadedFromStoreHash(this.loadedFromStoreHash)):await this.doDefaultScroll(),this.loadedFromStoreHash=null,this.hasHashFromRoute=!1},async scrollTo(e){return await this.$nextTick(()=>{const t=document.querySelector(e);return t==null||t.scrollIntoView({behavior:"instant",block:"start"}),t})},async scrollToOrLoad(e,t){let s;t&&(s=await this.scrollTo(e)),s||(console.log("Didn't scrolled or different queries, resetting"),await this.initializeHashVariablesAndReloadItems())},async clearRouteHash(){return this.$router.push({hash:null,query:this.$route.query})},async initializeHashVariablesAndReloadItems(){this.initializeHashVariables(),await this.reloadItems()},saveLastVisibleElement(e){if(console.log("saveLastVisibleElement",this.conditionToSaveLastVisible()),this.conditionToSaveLastVisible()){const s=[...document.querySelectorAll(this.scrollerSelector()+" "+this.itemSelector())].map(l=>{const a=l.getBoundingClientRect().top>10;return{item:l,visible:a}}).filter(l=>l.visible);if(s.length==0){console.warn("Unable to get desiredVisible");return}const r=this.doSaveTheFirstItem()?s[0].item:s[s.length-1].item,i=this.extractIdFromElementForStoring(r);console.log("For storing to localstore found desiredVisible",r,"itemId",i,"obj",e),this.setPositionToStore(i,e)}else console.log("Skipped saved desiredVisible because we are already scrolled")},doSaveTheFirstItem(){return this.initialDirection()==g}}});export{F as V,g as a,m as d,k as h,L as i};
