@import "constants.styl"

.message-item-root {
  // align-items: center;
  display: flex;
  letter-spacing: normal;
  outline: none;
  position: relative;
  text-decoration: none;
}

.message-item-with-buttons-wrapper {
  flex-direction row
  flex 1 1
}
.message-item-wrapper {
  border-radius 10px
  background #efefef
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: baseline;
  width: fit-content;
  word-wrap break-word
  overflow-wrap break-word

  .after-embed {
    padding-top: 0
  }
}

.my {
  background $messageSelectedBackground
}

.message-item-text {
  padding: 12px;
  line-height: $lineHeight;
  -ms-word-break: break-all;
  /* This is the dangerous one in WebKit, as it breaks things wherever */
  word-break: break-all;
  /* Instead use this non-standard one: */
  word-break: break-word;

  white-space: pre-wrap

  /* Adds a hyphen where the word breaks, if supported (No Blink) */
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
  p {
    margin-bottom unset
  }
  p:empty:after {
    content: '\200b';
  }
}

.message-item-text-mobile {
  padding: 8px;
  font-size 0.9rem
  line-height: $lineHeightMobile;
}
