import{_ as h,m as v,c_ as y,c as u,w as a,b as s,f as d,a as w,c$ as o,l as $,e as n,t as i,bW as V,j as b,k as m,d0 as g,aZ as k,s as f,d1 as I,d2 as T,a2 as S,d3 as A,d4 as C,T as P,A as q,B as p}from"./appMain-DAGYo8tP.js";const B={mixins:[y()],data:()=>({login:null,email:null,password:null,error:"",showInputablePassword:!1}),computed:{...v(q),showError(){return P(this.error)}},methods:{onSubmit(){const e={login:this.login,email:this.email,password:this.password};S.post("/api/aaa/register",e,{params:{language:this.$vuetify.locale.current,referer:this.$route.query.referer}}).then(()=>{this.$router.push({name:A})}).catch(t=>{this.error=C(t)})},hideAlert(){this.error=""},resend(){return T},onResendClick(){this.$router.push({name:I})},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.registration"),f(this.$vuetify.locale.t("$vuetify.registration"))}},watch:{"$vuetify.locale.current":{handler:function(e,t){this.setTopTitle()}}},mounted(){this.setTopTitle()},beforeUnmount(){this.chatStore.title=null,f(null),this.hideAlert(),this.showInputablePassword=!1}},U={class:"mt-2"},E=["href"];function N(e,t,R,c,F,r){return p(),u(k,{"max-width":"800",class:"px-2 pt-2"},{default:a(()=>[s(g,{"fast-fail":"",onSubmit:t[7]||(t[7]=m(l=>r.onSubmit(),["prevent"]))},{default:a(()=>[s(o,{onInput:t[0]||(t[0]=l=>r.hideAlert()),modelValue:e.login,"onUpdate:modelValue":t[1]||(t[1]=l=>e.login=l),label:e.$vuetify.locale.t("$vuetify.login"),rules:[e.rules.required],variant:"underlined"},null,8,["modelValue","label","rules"]),s(o,{onInput:t[3]||(t[3]=l=>r.hideAlert()),modelValue:e.password,"onUpdate:modelValue":t[4]||(t[4]=l=>e.password=l),type:e.showInputablePassword?"text":"password",label:e.$vuetify.locale.t("$vuetify.password"),rules:[e.rules.required,e.rules.min],variant:"underlined"},{append:a(()=>[s($,{onClick:t[2]||(t[2]=l=>e.showInputablePassword=!e.showInputablePassword),class:"mx-1 ml-3"},{default:a(()=>[n(i(e.showInputablePassword?"mdi-eye":"mdi-eye-off"),1)]),_:1})]),_:1},8,["modelValue","type","label","rules"]),s(o,{onInput:t[5]||(t[5]=l=>r.hideAlert()),modelValue:e.email,"onUpdate:modelValue":t[6]||(t[6]=l=>e.email=l),label:e.$vuetify.locale.t("$vuetify.email"),rules:[e.rules.required,e.rules.email],variant:"underlined"},null,8,["modelValue","label","rules"]),r.showError?(p(),u(V,{key:0,density:"compact",type:"error",text:e.error},null,8,["text"])):w("",!0),s(b,{type:"submit",color:"primary",block:"",class:"mt-2"},{default:a(()=>[n(i(e.$vuetify.locale.t("$vuetify.registration_submit")),1)]),_:1})]),_:1}),d("div",U,[n(i(e.$vuetify.locale.t("$vuetify.request_resend_confirmation_email_text"))+" ",1),d("a",{class:"colored-link",href:r.resend(),onClick:t[8]||(t[8]=m(l=>r.onResendClick(),["prevent"]))},i(e.$vuetify.locale.t("$vuetify.request_resend_confirmation_email_full")),9,E)])]),_:1})}const j=h(B,[["render",N]]);export{j as default};
