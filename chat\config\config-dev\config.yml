server:
  address: ":1235"
  shutdown.timeout: "10s"
  body.limit: "100G"

logger:
  level: info
  dir: "log"
  filename: "file.log"
  writeToFile: true

# Rest client
http:
  maxIdleConns: 2
  idleConnTimeout: '10s'
  disableCompression: false

auth:
  exclude:
    - "^/api/chat/public.*"
    - "^/internal.*"
    - "^/api/blog.*"

postgresql:
  # https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING
  url: "postgres://postgres:postgresqlPassword@localhost:45401/chat?sslmode=disable&application_name=chat-app"
  maxOpenConnections: 16
  maxIdleConnections: 4
  maxLifetime: 30s

aaa:
  url:
    base: "http://localhost:8060"
    getUsers: "/internal/user/list"
    getOnlines: "/internal/user/online"
    searchUsers: "/internal/user/search"
    checkUsersExistsPath: "/internal/user/exist"

rabbitmq:
  url: "amqp://videoChat:videoChatPazZw0rd@127.0.0.1:36672"

otlp:
  endpoint: "localhost:34317"

previewMaxTextSize: 240
previewMaxTextSizeDb: 280

# Along with resending, it makes all files in private chat available to GET by any logged in user
canResendFromTetATet: true

blogPreviewMaxTextSize: 400

frontendUrl: "http://localhost:8081"
message:
  # The list of allowed url prefixes which can be used in insertable image, video, ..., separated by , (comma)
  # Let's consider an example: "http://api.site.local:8080"
  # the element allows regular urls from this site, such as "http://api.site.local:8080/api/storage/download?file=chat%2F34%2Fbd92b094-35aa-43ee-9dae-a8aeb5912843%2Fpicture.jpg"
  allowedMediaUrls: ""
  allowedIframeUrls: ""
  # should be aligned with viewList.maxSize in storage
  maxMedias: 100 # image, video, audio, iframe

chat:
  allowedAvatarUrls: ""

onlyAdminCanCreateBlog: false

redis:
  address: :36379
  password: ""
  db: 5
  maxRetries: 10000

schedulers:
  cleanChatsOfDeletedUserTask:
    enabled: true
    cron: "0 * * * * *"
    batchChats: 20
    batchParticipants: 20
    expiration: "30m"
