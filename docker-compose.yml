# This file used for both developer and demo purposes.
# It contains environment
version: '3.7'

services:
  traefik:
    image: traefik:v3.4.1
    hostname: traefik
    restart: unless-stopped
    environment:
      - OTEL_PROPAGATORS=jaeger
    # The Static Configuration
    command: --configFile=/etc/traefik/traefik.yml
    ports:
      - 0.0.0.0:8081:8081
    extra_hosts:
      # https://github.com/moby/moby/pull/40007
      # works on Docker for Mac 3.4.0 or Linux Docker 20.10.4
      - "host.docker.internal:host-gateway"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:z
      - ./docker/traefik:/etc/traefik:z
    logging:
      # https://docs.docker.com/engine/logging/drivers/fluentd/
      driver: "fluentd"
      options:
        fluentd-address: localhost:24224
        fluentd-async: "true" # in order not to fail in case traefik started faster than fluent-bit
        tag: videochat.infra.traefik
        fluentd-sub-second-precision: "true"
    networks:
      backend:


  postgresql:
    image: postgres:17.0-alpine3.20
    hostname: postgresql
    restart: unless-stopped
    ports:
      - 35432:5432
    volumes:
      - ./docker/postgresql/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d:z
      - postgres_data:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

  postgresql-citus-coordinator-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-coordinator-1
    restart: unless-stopped
    ports:
      - 45401:5432
    volumes:
      - ./docker/postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - ./docker/postgresql-citus/coordinator/docker-entrypoint-initdb.d/003-coordinator-init.sh:/docker-entrypoint-initdb.d/003-coordinator-init.sh:z
      - ./scripts/wait-for-it.sh:/sbin/wait-for-it.sh:z
      - postgres_chat_citus_coordinator_1_data:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"
  postgresql-citus-worker-1:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-1
    restart: unless-stopped
    ports:
      - 45501:5432
    volumes:
      - ./docker/postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - postgres_chat_citus_worker_1_data:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"
  postgresql-citus-worker-2:
    image: citusdata/citus:13.0.1-alpine
    hostname: postgresql-citus-worker-2
    restart: unless-stopped
    ports:
      - 45502:5432
    volumes:
      - ./docker/postgresql-citus/common/docker-entrypoint-initdb.d/002-common-init.sql:/docker-entrypoint-initdb.d/002-common-init.sql:z
      - postgres_chat_citus_worker_2_data:/var/lib/postgresql/data:z
    environment:
      - POSTGRES_PASSWORD=postgresqlPassword
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"


  redis:
    image: valkey/valkey:8.1.0-alpine3.21
    hostname: redis
    restart: unless-stopped
    ports:
      - 36379:6379
    volumes:
      - redis_data_dir:/data:z
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

  minio:
    image: bitnami/minio:2024.1.29-debian-11-r0
    hostname: minio
    restart: unless-stopped
    ports:
      - 39000:9000
      - 39001:9001
    networks:
      backend:
    environment:
      - MINIO_ROOT_USER=AKIAIOSFODNN7EXAMPLE
      - MINIO_ROOT_PASSWORD=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
      - MINIO_NOTIFY_AMQP_ENABLE_primary=on
      - MINIO_NOTIFY_AMQP_URL_primary=amqp://videoChat:videoChatPazZw0rd@rabbitmq:5672
      - MINIO_NOTIFY_AMQP_EXCHANGE_primary=minio-events
      - MINIO_NOTIFY_AMQP_EXCHANGE_TYPE_primary=direct
      - MINIO_NOTIFY_AMQP_MANDATORY_primary=off
      - MINIO_NOTIFY_AMQP_DURABLE_primary=on
      - MINIO_NOTIFY_AMQP_NO_WAIT_primary=off
      - MINIO_NOTIFY_AMQP_AUTO_DELETED_primary=off
      - MINIO_NOTIFY_AMQP_DELIVERY_MODE_primary=2
      - MINIO_PROMETHEUS_AUTH_TYPE=public
      - MINIO_BROWSER_REDIRECT_URL=http://localhost:8081/minio
    volumes:
      - minio_data:/bitnami/minio/data:z
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

  rabbitmq:
    image: rabbitmq:4.0.9-management-alpine
    hostname: rabbitmq
    restart: unless-stopped
    ports:
      - 35672:15672
      - 36672:5672
    environment:
      - RABBITMQ_DEFAULT_USER=videoChat
      - RABBITMQ_DEFAULT_PASS=videoChatPazZw0rd
    volumes:
      - rabbitmq_data_dir:/var/lib/rabbitmq/mnesia:z
      - ./docker/rabbitmq/additional.conf:/etc/rabbitmq/conf.d/additional.conf:ro,z
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"

  livekit:
    image: livekit/livekit-server:v1.9.0
    command: --config /etc/livekit.yaml
    restart: unless-stopped
    network_mode: "host"
    volumes:
      - ./docker/livekit/livekit.yaml:/etc/livekit.yaml

  egress:
    image: livekit/egress:v1.9.0
    restart: unless-stopped
    networks:
      backend:
    environment:
      - EGRESS_CONFIG_FILE=/etc/egress/config.yaml
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./docker/egress/config.yaml:/etc/egress/config.yaml
      - egress_tmp:/home/<USER>/tmp:z
    cap_add:
      - SYS_ADMIN

  jaeger:
    # https://www.jaegertracing.io/docs/2.0/getting-started/
    image: jaegertracing/jaeger:2.7.0
    hostname: jaeger
    restart: unless-stopped
    ports:
      # https://www.jaegertracing.io/docs/1.54/deployment/
      - 34318:4318 # OTLP GRPC over HTTP (aaa, traefik)
      - 34317:4317 # OTLP GRPC (other microservices)
      - 36686:16686 # web ui
    networks:
      backend:
    # https://www.jaegertracing.io/docs/2.7/deployment/configuration/
    # https://github.com/jaegertracing/jaeger/blob/v2.7.0/cmd/jaeger/internal/all-in-one.yaml
    # https://github.com/jaegertracing/jaeger/blob/v2.7.0/cmd/jaeger/config-opensearch.yaml
    volumes:
      - ./docker/jaeger/config.yaml:/jaeger/config.yaml
    environment:
      - JAEGER_LISTEN_HOST=0.0.0.0
    command: [
      "--config",
      "/jaeger/config.yaml",
    ]
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

  pgadmin:
    restart: unless-stopped
    hostname: pgadmin
    image: dcagatay/pwless-pgadmin4:8.13
    environment:
      - POSTGRES_USER_1=postgres
      - POSTGRES_PASSWORD_1=postgresqlPassword
      - POSTGRES_HOST_1=postgresql
      - POSTGRES_PORT_1=5432
      - POSTGRES_USER_2=postgres
      - POSTGRES_PASSWORD_2=postgresqlPassword
      - POSTGRES_HOST_2=postgresql-citus-coordinator-1
      - POSTGRES_PORT_2=5432
      - PGADMIN_DISABLE_POSTFIX=1
      - PGADMIN_LISTEN_ADDRESS=0.0.0.0
      - PGADMIN_LISTEN_PORT=8080
      - SCRIPT_NAME=/postgresql
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

  fluentbit:
    image: fluent/fluent-bit:4.0.4
    restart: unless-stopped
    hostname: fluentbit
    volumes:
      - ./docker/fluent-bit/etc:/fluent-bit/etc:z
      - ./aaa/log:/var/log/videochat/aaa/log:z
      - ./chat/log:/var/log/videochat/chat/log:z
      - ./storage/log:/var/log/videochat/storage/log:z
      - ./video/log:/var/log/videochat/video/log:z
      - ./event/log:/var/log/videochat/event/log:z
      - ./notification/log:/var/log/videochat/notification/log:z
      - ./public/log:/var/log/videochat/public/log:z
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    networks:
      backend:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

  opensearch:
    image: opensearchproject/opensearch:2.18.0
    hostname: opensearch
    restart: unless-stopped
    environment:
      - node.name=opensearch
      - discovery.type=single-node
      - bootstrap.memory_lock=true # along with the memlock settings below, disables swapping
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m" # minimum and maximum Java heap size, recommend setting both to 50% of system RAM
      # - OPENSEARCH_INITIAL_ADMIN_PASSWORD=yourStrongPassword123!
      # https://opensearch.org/docs/latest/install-and-configure/install-opensearch/docker/
      - DISABLE_SECURITY_PLUGIN=true
      - DISABLE_INSTALL_DEMO_CONFIG=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536 # maximum number of open files for the OpenSearch user, set to at least 65536 on modern systems
        hard: 65536
    networks:
      backend:
    volumes:
      - opensearch_data_dir:/usr/share/opensearch/data
      - ./docker/opensearch/docker-entrypoint-init.d.sh:/docker-entrypoint-init.d.sh:z
      - ./docker/opensearch/docker-entrypoint-init.d:/docker-entrypoint-init.d:z
    ports:
      - 9200:9200
      # - 9600:9600 # required for Performance Analyzer
    entrypoint: ["/docker-entrypoint-init.d.sh"]
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

  dashboards:
    image: opensearchproject/opensearch-dashboards:2.18.0
    hostname: dashboards
    restart: unless-stopped
    ports:
      - 5601:5601
    networks:
      backend:
    environment:
      OPENSEARCH_HOSTS: '["http://opensearch:9200"]'
      # https://stackoverflow.com/a/74178195
      DISABLE_SECURITY_DASHBOARDS_PLUGIN: true
      SERVER_BASEPATH: /opensearch-dashboards
      SERVER_REWRITEBASEPATH: true
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "1"

volumes:
  postgres_data:
  postgres_chat_citus_coordinator_1_data:
  postgres_chat_citus_worker_1_data:
  postgres_chat_citus_worker_2_data:
  redis_data_dir:
  minio_data:
  rabbitmq_data_dir:
  egress_tmp:
  opensearch_data_dir:

networks:
  backend:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.host_binding_ipv4: "127.0.0.1"
    ipam:
      driver: default
      config:
        - subnet: **********/24
