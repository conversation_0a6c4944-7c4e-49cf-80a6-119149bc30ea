@import "constants.styl"

.image-custom-class {
  max-width: 100% !important
  height: 360px !important // we should use the fixed height, otherwise during first load on Firefox page won't be scrolled exactly down
  cursor pointer
}

.mention {
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.4rem;
  padding: 0.1rem 0.3rem;
  box-decoration-break: clone;
}

.video-custom-class {
  max-width: 100% !important
  height: 360px !important
}

.audio-custom-class {
  max-width: 100% !important
}

@media (min-width: $mobileWidth) {
  .audio-custom-class {
    min-width: 480px
    height: 360px
  }

  audio.audio-custom-class {
    min-width: 640px
    height: unset
  }

}

.iframe-custom-class {
  position: relative;
  overflow: hidden;
  max-height: 100%;
  max-width: 100%;
  &.ProseMirror-selectednode {
    outline: 3px solid #68CEF8;
  }
  background black
  border-width 0
}

.message-item-wrapper a, .editorContent a {
  color: $linkColor;
}

.message-item-wrapper ul,
.editorContent ul {
  padding: 0 1rem;
}

.message-item-wrapper ol,
.editorContent ol {
  padding: 0 1.6rem;
}

.message-item-wrapper code, .editorContent code {
  border-radius: 3px;
  font-size: 85%;
  font-weight: normal;
  padding: 0.2em 0.4em;
  background-color: rgba(0, 0, 0, 0.05);
}

.message-item-wrapper pre, .editorContent pre {
  background: #0D0D0D;
  color: #FFF;
  font-family: 'JetBrainsMono', monospace;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  width: fit-content;

  code {
    color: inherit;
    padding: 0;
    background: none;
    font-size: 0.8rem;
    white-space: pre-wrap;

    word-break: break-word;
  }

}

.media-in-message-wrapper {
  position relative
  display flex
  height: 360px
  width: fit-content;
}

.media-in-message-wrapper-audio {
  height: unset !important
  display: block;
}

// open in player button
.media-in-message-button-open {
  font-size: 3em;
  line-height 1em
  color white
  cursor pointer
  text-shadow: 1px 1px 2px #000;

  z-index 2
  display inherit
  right 0.4em
  top 0.4em
  position: absolute
}

.media-in-message-button-replace {
  font-size: 3em;
  line-height 1em
  color white
  cursor pointer
  text-shadow: 1px 1px 2px #000;

  z-index 2
  display inherit
  right calc(50% - 24px) // minus half of button
  top calc(50% - 24px)
  position: absolute
}

.media-in-message-button-replace-first {
  //top 0.4em
}

@media screen and (max-width: $mobileWidth) {
  .image-custom-class {
    max-height: 300px !important
  }

  .video-custom-class {
    max-height: 300px !important
  }

  .audio-custom-class {
    min-width: unset
    max-height: 300px !important
  }

  .media-in-message-wrapper {
    max-height: 300px
  }
}
