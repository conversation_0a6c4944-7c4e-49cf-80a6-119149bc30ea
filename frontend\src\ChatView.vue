<template>
    <splitpanes ref="splOuter" class="default-theme" id="root-splitpanes" :dbl-click-splitter="false" :style="heightWithoutAppBar" @resize="onPanelResized($event)" @pane-add="onPanelAdd($event)" @pane-remove="onPanelRemove($event)">
      <pane :size="leftPaneSize()" v-if="showLeftPane()">
        <ChatList :embedded="true" v-if="isAllowedChatList()" ref="chatListRef"/>
      </pane>

      <pane style="background: white" :size="centralPaneSize()">
        <splitpanes ref="splCentral" id="central-splitpanes" class="default-theme" :dbl-click-splitter="false" horizontal @resize="onPanelResized($event)" @pane-add="onPanelAdd($event)" @pane-remove="onPanelRemove($event)">
          <pane v-if="showTopPane()" :size="topPaneSize()" class="video-top-pane">
            <ChatVideo v-if="chatDtoIsReady" :chatId="chatId" ref="chatVideoRef"/>
          </pane>

          <pane :class="messageListPaneClass()" :size="messageListPaneSize()">
            <v-tooltip
                v-if="broadcastMessage"
                :model-value="showTooltip"
                :activator="showBottomPane() ? '.message-edit-pane' : '.message-pane-mobile'"
                location="bottom center"
            >
              <span v-html="broadcastMessage"></span>
            </v-tooltip>

            <div v-if="pinnedPromoted" :key="pinnedPromotedKey" class="pinned-promoted" :title="$vuetify.locale.t('$vuetify.goto_pinned_message')">
              <v-alert
                  color="red-lighten-4"
                  elevation="2"
                  density="compact"
              >
                <template v-slot:text>
                  <a
                      :href="getPinnedPromotedRoute(pinnedPromoted)"
                      @click.prevent="onClickPinnedPromoted(pinnedPromoted)"
                      class="pinned-text"
                      v-html="pinnedPromoted.text"
                  ></a>
                </template>
                <template v-slot:append>
                  <v-btn density="compact" icon rounded="0" variant="plain" :title="$vuetify.locale.t('$vuetify.pinned_messages')" @click.stop.prevent="openPinnedMessages()"><v-icon>mdi-view-list-outline</v-icon></v-btn>
                </template>
              </v-alert>
            </div>

            <MessageList :isCompact="isVideoRoute()"/>

            <v-btn v-if="chatStore.showScrollDown" variant="elevated" color="primary" icon="mdi-arrow-down-thick" :class="scrollDownClass()" @click="scrollDown()"></v-btn>
            <v-btn v-if="isMobile() && canWriteMessage" variant="elevated" icon color="primary" class="new-fab-b" @click="openNewMessageDialog()">
              <v-badge
                  color="red"
                  dot
                  location="right top"
                  overlap
                  bordered
                  offset-x="-9"
                  offset-y="-9"
                  :model-value="chatStore.hasMessageEditingText()"
              >
                <v-icon>{{chatStore.isMessageEditing() ? 'mdi-lead-pencil' : 'mdi-plus-thick'}}</v-icon>
              </v-badge>
            </v-btn>
          </pane>
          <pane class="message-edit-pane d-flex flex-row justify-center align-center" v-if="showBottomPane()" :size="bottomPaneSize()" style="background: white; color: #3a3a3e">
            <MessageEdit v-if="canWriteMessage" :chatId="this.chatId"/>
            <span v-else>{{ $vuetify.locale.t('$vuetify.you_cannot_write_message') }}</span>
          </pane>
        </splitpanes>
      </pane>

      <pane v-if="showRightPane()" :size="rightPaneSize()">
        <ChatVideo v-if="chatDtoIsReady" :chatId="chatId" ref="chatVideoRef"/>
      </pane>

    </splitpanes>
</template>

<script>
import { Splitpanes, Pane } from 'splitpanes';
import ChatList from "@/ChatList.vue";
import MessageList from "@/MessageList.vue";
import MessageEdit from "@/MessageEdit.vue";
import heightMixin from "@/mixins/heightMixin";
import {mapStores} from "pinia";
import {useChatStore} from "@/store/chatStore";
import axios from "axios";
import {
  hasLength,
  isCalling,
  isChatRoute,
  new_message,
  setTitle,
  goToPreservingQuery,
  setSplitter,
  isMobileBrowser,
  upsertToWritingUsers,
  buildWritingUsersSubtitleInfo,
  filterOutOldWritingUsers, isStrippedUserLogin
} from "@/utils";
import bus, {
  CHAT_DELETED,
  CHAT_EDITED, CO_CHATTED_PARTICIPANT_CHANGED,
  FILE_CREATED,
  FILE_REMOVED,
  FILE_UPDATED,
  LOGGED_OUT,
  MESSAGE_ADD,
  MESSAGE_BROADCAST,
  MESSAGE_DELETED,
  MESSAGE_EDITED, MESSAGES_RELOAD,
  OPEN_EDIT_MESSAGE, OPEN_PINNED_MESSAGES_MODAL,
  PARTICIPANT_ADDED,
  PARTICIPANT_DELETED,
  PARTICIPANT_EDITED, PINNED_MESSAGE_EDITED,
  PINNED_MESSAGE_PROMOTED,
  PINNED_MESSAGE_UNPROMOTED,
  PREVIEW_CREATED, PROFILE_SET,
  PUBLISHED_MESSAGE_ADD, PUBLISHED_MESSAGE_EDITED,
  PUBLISHED_MESSAGE_REMOVE,
  REACTION_CHANGED,
  REACTION_REMOVED,
  REFRESH_ON_WEBSOCKET_RESTORED,
  SCROLL_DOWN,
  USER_TYPING,
  VIDEO_CALL_USER_COUNT_CHANGED,
  VIDEO_DIAL_STATUS_CHANGED, WEBSOCKET_INITIALIZED, WEBSOCKET_UNINITIALIZED,
} from "@/bus/bus";
import {chat, chat_list_name, chat_name, messageIdHashPrefix, video_suffix, videochat_name} from "@/router/routes";
import graphqlSubscriptionMixin from "@/mixins/graphqlSubscriptionMixin";
import ChatVideo from "@/ChatVideo.vue";
import videoPositionMixin from "@/mixins/videoPositionMixin";
import {SEARCH_MODE_CHATS, searchString} from "@/mixins/searchString.js";
import onFocusMixin from "@/mixins/onFocusMixin.js";
import userStatusMixin from "@/mixins/userStatusMixin.js";
import {getStoredVideoMessages} from "@/store/localStore.js";

const getChatEventsData = (message) => {
  return message.data?.chatEvents
};

let writingUsersTimerId;

const panelSizesKey = "panelSizes";

const messagesSplitpanesSelector = isMobileBrowser() ? '#central-splitpanes' : "#root-splitpanes";
const messagesSplitterDisplayVarName = "--splitter-v-display";

const emptyStoredPanes = () => {
  return {
    topPane: 60, // ChatVideo mobile
    leftPane: 20, // ChatList
    rightPane: 70, // ChatVideo desktop
    bottomPane: 15, // MessageEdit in case desktop (!isMobile())
    bottomPaneBig: 60, // MessageEdit in case desktop (!isMobile()) and a text containing a newline
  }
}

export default {
  mixins: [
    heightMixin(),
    videoPositionMixin(),
    searchString(SEARCH_MODE_CHATS),
    onFocusMixin(),
    userStatusMixin('userStatusInChatViewTetATet'), // subscription
  ],
  data() {
    return {
      pinnedPromoted: null,
      pinnedPromotedKey: +new Date(),
      writingUsers: [],
      showTooltip: true,
      broadcastMessage: null,
      // shows that all the possible PUT /join have happened and we can get ChatList. Intentionally doesn't reset on switching chat at left
      // if we remove it (or replace with chatDtoIsReady) - there are going to be disappears of ChatList when user clicks on the different chat
      initialLoaded: false,
      chatEventsSubscription: null,
      canWriteMessage: true, // for sake prevent disappearing TipTap on switching in the left pane
      initialized: false,
      chatEventsSubscribed: false,
    }
  },
  components: {
    ChatVideo,
    Splitpanes,
    Pane,
    ChatList,
    MessageList,
    MessageEdit,
  },
  computed: {
    ...mapStores(useChatStore),
    chatId() {
      return this.$route.params.id
    },
    chatDtoIsReady() {
      return !!this.chatStore.chatDto.id
    },
  },
  methods: {
    onProfileSet() {
      return this.getInfo(this.chatId).then(()=>{
        this.chatStore.showCallManagement = true;
        if (!this.chatEventsSubscribed) {
          this.chatEventsSubscribed = true;
          this.chatEventsSubscription.graphQlSubscribe();
        }
      })
    },
    async doInitialize() {
      if (!this.initialized) {
        this.initialized = true;
        await this.onProfileSet();
      }
    },
    onLogout() {
      this.partialReset(true);
      this.initialLoaded = false;
      this.chatEventsSubscription.graphQlUnsubscribe();
    },
    doUninitialize() {
      if (this.initialized) {
        this.onLogout();
        this.initialized = false;
      }
    },
    fetchAndSetChat(chatId) {
      return axios.get(`/api/chat/${chatId}`, {
        signal: this.requestAbortController.signal
      }).then((response) => {
        if (response.status == 205) {
          return axios.put(`/api/chat/${chatId}/join`, null, {
            signal: this.requestAbortController.signal
          }).then((response)=>{
              return axios.get(`/api/chat/${chatId}`, {
                signal: this.requestAbortController.signal
              }).then((response)=>{
                  return this.processNormalInfoResponse(response)
              })
          })
        } else if (response.status == 204) {
          this.goToChatList();
          this.setWarning(this.$vuetify.locale.t('$vuetify.chat_not_found'));
          return Promise.reject();
        } else {
            return this.processNormalInfoResponse(response);
        }
      })
    },
    processNormalInfoResponse(response) {
        const data = response.data;
        console.log("Got info about chat in ChatView, chatId=", this.chatId, data);
        this.commonChatEdit(data);
        this.chatStore.tetATet = data.tetATet;
        this.chatStore.setChatDto(data);
        this.initialLoaded = true;
        this.canWriteMessage = data.canWriteMessage;
        return Promise.resolve(data);
    },
    setParticipantsFields(data) {
      this.chatStore.title = data.name;
      this.chatStore.titleStrike = isStrippedUserLogin(data);
      setTitle(data.name);
      this.chatStore.avatar = data.avatar;
    },
    commonChatEdit(data) {
        this.setParticipantsFields(data);
        this.chatStore.chatUsersCount = data.participantsCount;
        this.chatStore.showChatEditButton = data.canEdit;
        this.chatStore.canBroadcastTextMessage = data.canBroadcast;
        if (data.blog) {
            this.chatStore.showGoToBlogButton = this.chatId;
        } else {
            this.chatStore.showGoToBlogButton = null;
        }
        if (this.isRealTetATet(data)) {
            this.chatStore.oppositeUserLastSeenDateTime = data.lastSeenDateTime;
        }
    },
    isRealTetATet(data) {
      if (data.tetATet && data.participantsCount == 2) {
        return true
      } else {
        return false
      }
    },
    fetchPromotedMessage(chatId) {
      axios.get(`/api/chat/${chatId}/message/pin/promoted`, {
        signal: this.requestAbortController.signal
      }).then((response) => {
        if (response.status != 204) {
          this.pinnedPromoted = response.data;
          this.pinnedPromotedKey++
        } else {
          this.pinnedPromoted = null;
        }
      });
    },
    hasTetATetUserStatusSubscriptions() {
      return !!this.subscriptionElements.length
    },
    getInfo(chatId) {
      this.updateLastUpdateDateTime();
      return this.fetchAndSetChat(chatId).then((data) => {
        if (this.isRealTetATet(data)) {
            if (!this.hasTetATetUserStatusSubscriptions()) {
              console.info("Subscribing onto tet-a-tet online")
              this.graphQlUserStatusSubscribe();
            }
            this.requestTetAtTetStatuses();
        }
        // async call
        this.fetchPromotedMessage(chatId);
        axios.get(`/api/video/${chatId}/users`, {
          signal: this.requestAbortController.signal
        })
          .then(response => response.data)
          .then(data => {
            bus.emit(VIDEO_CALL_USER_COUNT_CHANGED, data);
          })
        return Promise.resolve();
      }).then(() => {
        // async call
        axios.get(`/api/video/${chatId}/record/status`, {
          signal: this.requestAbortController.signal
        }).then(({data}) => {
          this.chatStore.canMakeRecord = data.canMakeRecord;
          if (data.canMakeRecord) {
            const record = data.recordInProcess;
            if (record) {
              this.chatStore.showRecordStopButton = true;
            }
          }
        })
        return Promise.resolve();
      })
    },
    getTetAtetOppositeParticipant() {
      return this.chatStore.chatDto.participantIds.find(pi => pi !== this.chatStore.currentUser.id)
    },
    requestTetAtTetStatuses() {
      this.$nextTick(() => {
        if (this.chatStore.currentUser && this.chatId) {
          const userId = this.getTetAtetOppositeParticipant();
          this.triggerUsesStatusesEvents(userId, this.requestAbortController.signal);
        }
      })
    },
    getUserIdsSubscribeTo() {
      const userId = this.getTetAtetOppositeParticipant();
      return [userId];
    },
    // testcase
    // user 1 opens a tet-a-tet with user 2
    // user 2 is offline
    // user 1 sees "last login..." instead of num of participants
    // user 2 logs in
    // user 1 sees num of participants
    // user 2 logs out
    // user 1 sees "last login..."
    onUserStatusChanged(dtos) {
      const userId = this.getTetAtetOppositeParticipant();

      if (dtos && userId) {
        dtos.forEach(dtoItem => {
          if (dtoItem.online != null && userId == dtoItem.userId) {
            if (dtoItem.online) {
              this.chatStore.oppositeUserOnline = true;
              this.chatStore.oppositeUserLastSeenDateTime = null;
            } else {
              this.chatStore.oppositeUserOnline = false;
              this.chatStore.oppositeUserLastSeenDateTime = dtoItem.lastSeenDateTime;
            }
          }
          if (dtoItem.isInVideo !== null && userId == dtoItem.userId) {
            if (dtoItem.isInVideo) {
              this.chatStore.oppositeUserInVideo = true
            } else {
              this.chatStore.oppositeUserInVideo = false
            }
          }
        })
      }
    },
    goToChatList() {
      this.$router.push(({name: chat_list_name}))
    },
    getGraphQlSubscriptionQuery() {
      return `
                                fragment DisplayMessageDtoFragment on DisplayMessageDto {
                                  id
                                  text
                                  chatId
                                  ownerId
                                  createDateTime
                                  editDateTime
                                  owner {
                                    id
                                    login
                                    avatar
                                    shortInfo
                                    loginColor
                                    additionalData {
                                      enabled,
                                      expired,
                                      locked,
                                      confirmed,
                                      roles,
                                    }
                                  }
                                  canEdit
                                  canDelete
                                  fileItemUuid
                                  embedMessage {
                                    id
                                    chatId
                                    chatName
                                    text
                                    owner {
                                      id
                                      login
                                      avatar
                                      shortInfo
                                      loginColor
                                      additionalData {
                                        enabled,
                                        expired,
                                        locked,
                                        confirmed,
                                        roles,
                                      }
                                    }
                                    embedType
                                    isParticipant
                                  }
                                  pinned
                                  blogPost
                                  pinnedPromoted
                                  reactions {
                                    count
                                    users {
                                      id
                                      login
                                      avatar
                                      shortInfo
                                      loginColor
                                      additionalData {
                                        enabled,
                                        expired,
                                        locked,
                                        confirmed,
                                        roles,
                                      }
                                    }
                                    reaction
                                  }
                                  published
                                  canPublish
                                  canPin
                                }

                                subscription{
                                  chatEvents(chatId: ${this.chatId}) {
                                    eventType
                                    messageEvent {
                                      ...DisplayMessageDtoFragment
                                    }
                                    messageDeletedEvent {
                                      id
                                      chatId
                                    }
                                    messageBroadcastEvent {
                                      login
                                      userId
                                      text
                                    }
                                    previewCreatedEvent {
                                      id
                                      url
                                      previewUrl
                                      aType
                                      correlationId
                                    }
                                    participantsEvent {
                                      id
                                      login
                                      avatar
                                      admin
                                      shortInfo
                                      loginColor
                                      additionalData {
                                        enabled,
                                        expired,
                                        locked,
                                        confirmed,
                                        roles,
                                      }
                                    }
                                    promoteMessageEvent {
                                      count
                                      message {
                                        id
                                        text
                                        chatId
                                        ownerId
                                        owner {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        pinnedPromoted
                                        createDateTime
                                        canPin
                                      }
                                    }
                                    publishedMessageEvent {
                                      count
                                      message {
                                        id
                                        text
                                        chatId
                                        ownerId
                                        owner {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        createDateTime
                                        canPublish
                                      }
                                    }
                                    fileEvent {
                                      fileInfoDto {
                                        id
                                        filename
                                        url
                                        publishedUrl
                                        previewUrl
                                        size
                                        canDelete
                                        canEdit
                                        canShare
                                        lastModified
                                        ownerId
                                        owner {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        canPlayAsVideo
                                        canShowAsImage
                                        canPlayAsAudio
                                        fileItemUuid
                                        correlationId
                                        previewable
                                        aType
                                      }
                                    }
                                    reactionChangedEvent {
                                      messageId
                                      reaction {
                                        count
                                        users {
                                          id
                                          login
                                          avatar
                                          shortInfo
                                          loginColor
                                          additionalData {
                                            enabled,
                                            expired,
                                            locked,
                                            confirmed,
                                            roles,
                                          }
                                        }
                                        reaction
                                      }
                                    }
                                  }
                                }
                `
    },
    onNextSubscriptionElement(e) {
      if (getChatEventsData(e).eventType === 'message_created') {
        const d = getChatEventsData(e).messageEvent;
        bus.emit(MESSAGE_ADD, d);
      } else if (getChatEventsData(e).eventType === 'message_deleted') {
        const d = getChatEventsData(e).messageDeletedEvent;
        bus.emit(MESSAGE_DELETED, d);
      } else if (getChatEventsData(e).eventType === 'message_edited') {
        const d = getChatEventsData(e).messageEvent;
        bus.emit(MESSAGE_EDITED, d);
      } else if (getChatEventsData(e).eventType === "user_broadcast") {
        const d = getChatEventsData(e).messageBroadcastEvent;
        bus.emit(MESSAGE_BROADCAST, d);
      } else if (getChatEventsData(e).eventType === "preview_created") {
        const d = getChatEventsData(e).previewCreatedEvent;
        bus.emit(PREVIEW_CREATED, d);
      } else if (getChatEventsData(e).eventType === "participant_added") {
        const d = getChatEventsData(e).participantsEvent;
        bus.emit(PARTICIPANT_ADDED, d);
      } else if (getChatEventsData(e).eventType === "participant_deleted") {
        const d = getChatEventsData(e).participantsEvent;
        bus.emit(PARTICIPANT_DELETED, d);
      } else if (getChatEventsData(e).eventType === "participant_edited") {
        const d = getChatEventsData(e).participantsEvent;
        bus.emit(PARTICIPANT_EDITED, d);
      } else if (getChatEventsData(e).eventType === "pinned_message_promote") {
        const d = getChatEventsData(e).promoteMessageEvent;
        bus.emit(PINNED_MESSAGE_PROMOTED, d);
      } else if (getChatEventsData(e).eventType === "pinned_message_unpromote") {
        const d = getChatEventsData(e).promoteMessageEvent;
        bus.emit(PINNED_MESSAGE_UNPROMOTED, d);
      } else if (getChatEventsData(e).eventType === "pinned_message_edit") {
        const d = getChatEventsData(e).promoteMessageEvent;
        bus.emit(PINNED_MESSAGE_EDITED, d);
      } else if (getChatEventsData(e).eventType === "published_message_add") {
          const d = getChatEventsData(e).publishedMessageEvent;
          bus.emit(PUBLISHED_MESSAGE_ADD, d);
      } else if (getChatEventsData(e).eventType === "published_message_remove") {
          const d = getChatEventsData(e).publishedMessageEvent;
          bus.emit(PUBLISHED_MESSAGE_REMOVE, d);
      } else if (getChatEventsData(e).eventType === "published_message_edit") {
          const d = getChatEventsData(e).publishedMessageEvent;
          bus.emit(PUBLISHED_MESSAGE_EDITED, d);
      } else if (getChatEventsData(e).eventType === "file_created") {
        const d = getChatEventsData(e).fileEvent;
        bus.emit(FILE_CREATED, d);
      } else if (getChatEventsData(e).eventType === "file_removed") {
        const d = getChatEventsData(e).fileEvent;
        bus.emit(FILE_REMOVED, d);
      } else if (getChatEventsData(e).eventType === "file_updated") {
        const d = getChatEventsData(e).fileEvent;
        bus.emit(FILE_UPDATED, d);
      } else if (getChatEventsData(e).eventType === "reaction_changed") {
        const d = getChatEventsData(e).reactionChangedEvent;
        bus.emit(REACTION_CHANGED, d);
      } else if (getChatEventsData(e).eventType === "reaction_removed") {
        const d = getChatEventsData(e).reactionChangedEvent;
        bus.emit(REACTION_REMOVED, d);
      } else if (getChatEventsData(e).eventType === "messages_reload") {
          bus.emit(MESSAGES_RELOAD);
      }
    },
    getPinnedPromotedRoute(item) {
      let mbVideo = "";
      if (this.isVideoRoute()) {
        mbVideo = video_suffix;
      }
      return chat + "/" + item.chatId + mbVideo + messageIdHashPrefix + item.id
    },
    onClickPinnedPromoted(item) {
      const routeName = this.isVideoRoute() ? videochat_name : chat_name;
      const obj = {name: routeName, params: {id: item.chatId}, hash: messageIdHashPrefix + item.id};
      if (this.chatStore.canShowPinnedLink) {
        this.$router.push(obj)
      }
    },
    onPinnedMessagePromoted(item) {
      this.pinnedPromoted = item.message;
      this.pinnedPromotedKey++;
    },
    onPinnedMessageUnpromoted(item) {
      if (this.pinnedPromoted && this.pinnedPromoted.id == item.message.id) {
        this.pinnedPromoted = null;
      }
    },
    onPinnedMessageChanged(item) {
        if (this.pinnedPromoted && this.pinnedPromoted.id == item.message.id) {
            this.onPinnedMessagePromoted(item);
        }
    },
    onFocus() {
        if (this.chatStore.currentUser && this.chatId) {
            this.getInfo(this.chatId);
        }
    },
    onUserBroadcast(dto) {
      console.log("onUserBroadcast", dto);
      const stripped = dto.text;
      if (stripped && stripped.length > 0) {
        this.showTooltip = true;
        this.broadcastMessage = dto.text;
      } else {
        this.broadcastMessage = null;
      }
    },
    onChatChange(data) {
        if (data.id == this.chatId) {
            this.commonChatEdit(data);
            this.chatStore.setChatDto(data);
        }
    },
    onChatDelete(dto) {
      if (dto.id == this.chatId) {
          this.$router.push(({name: chat_list_name}))
      }
    },
    onParticipantDeleted(dtos) { // also there is redraw/delete logic in ChatList::redrawItem
        if (dtos.find(p => p.id == this.chatStore.currentUser.id)) {
            const routerNewState = { name: chat_list_name};
            goToPreservingQuery(this.$route, this.$router, routerNewState);
        }
    },
    isAllowedVideo() {
      return this.chatStore.currentUser && this.$route.name == videochat_name && this.chatStore.chatDto?.participantIds?.length
    },
    isAllowedChatList() {
        // second condition is for waiting full loading (including PUT /join) of chat in order to be visible
        // testcase: user 1 creates blog without any other users
        // user 2 wants to write a comment, clicking the button in blog
        // he is being redirected to chat, because user 2 is not a participant, he gets a http code and the browser issues /join
        // after the joining all user 2 want to see chat of blog at the left
        return this.chatStore.currentUser && this.initialLoaded
    },
    onWsRestoredRefresh() {
      this.getInfo(this.chatId)
    },
    partialReset(keepTitle) {
      this.chatEventsSubscribed = false;
      this.chatStore.resetChatDto();

      this.chatStore.videoChatUsersCount=0;
      this.chatStore.canMakeRecord=false;
      this.pinnedPromoted=null;
      this.chatStore.canBroadcastTextMessage = false;
      this.chatStore.showRecordStartButton = false;
      this.chatStore.showRecordStopButton = false;
      this.chatStore.showChatEditButton = false;

      if (!keepTitle) {
        this.chatStore.titleStrike = false;
        this.chatStore.title = null;
        setTitle(null);
      }
      this.chatStore.avatar = null;
      this.chatStore.showGoToBlogButton = null;

      this.chatStore.chatUsersCount = 0;

      this.chatStore.oppositeUserLastSeenDateTime = null;
      this.chatStore.oppositeUserInVideo = false;
      this.chatStore.oppositeUserOnline = false;

      this.chatStore.showCallManagement = false;

      if (this.hasTetATetUserStatusSubscriptions()) {
        console.info("Unsubscribing from tet-a-tet online")
        this.graphQlUserStatusUnsubscribe();
      }
    },
    onChatDialStatusChange(dto) {
      if (this.chatStore.chatDto?.tetATet && dto.chatId == this.chatId) { // if tet-a-tet
        for (const videoDialChanged of dto.dials) {
          if (this.chatStore.currentUser.id != videoDialChanged.userId) { // if counterpart exists
            this.chatStore.shouldPhoneBlink = isCalling(videoDialChanged.status); // and if their status is being calling - turn on blinking on my frontend
          }
        }
      }
    },
    openNewMessageDialog() { // on mobile OPEN_EDIT_MESSAGE with the null argument
      bus.emit(OPEN_EDIT_MESSAGE, {dto: null, actionType: new_message});
    },
    messageListPaneClass() {
      const classes = [];
      classes.push('message-pane');
      if (this.isMobile()) {
        classes.push('message-pane-mobile');
      }
      return classes;
    },

    showRightPane() {
      return !this.isMobile() && this.isAllowedVideo()
    },

    showLeftPane() {
      return this.shouldShowChatList()
    },
    showBottomPane() {
      return !this.isMobile();
    },

    leftPaneSize() {
      return this.getStored().leftPane;
    },
    rightPaneSize() {
      if (this.chatStore.videoMessagesEnabled) {
        return this.getStored().rightPane;
      } else {
        return 100
      }
    },
    showTopPane() {
      return this.isMobile() && this.isAllowedVideo()
    },
    topPaneSize() { // only for mobile
      if (this.chatStore.videoMessagesEnabled) {
        return this.getStored().topPane;
      } else {
        return 100
      }
    },
    centralPaneSize() {
      if (this.isMobile()) {
        return 100
      } else {
        if (this.showRightPane()) {
          return 100 - this.rightPaneSize();
        } else if (this.showLeftPane()) {
          return 100 - this.leftPaneSize();
        } else {
          return 100;
        }
      }
    },
    bottomPaneSize() {
      if (!this.chatStore.isEditingBigText) {
        return this.getStored().bottomPane;
      } else {
        return this.getStored().bottomPaneBig;
      }
    },
    messageListPaneSize() {
      if (this.isMobile()) {
        if (this.showTopPane()) {
          return 100 - this.topPaneSize();
        } else {
          return 100;
        }
      } else {
        if (this.showBottomPane()) {
          return 100 - this.bottomPaneSize()
        } else {
          return 100
        }
      }
    },

    // returns json with sizes from localstore
    getStored() {
      const mbItem = localStorage.getItem(panelSizesKey);
      if (!mbItem) {
        return emptyStoredPanes();
      } else {
        return JSON.parse(mbItem);
      }
    },
    // saves to localstore
    saveToStored(obj) {
      localStorage.setItem(panelSizesKey, JSON.stringify(obj));
    },
    // prepares json to store by extracting concrete panel sizes
    prepareForStore() {
      const outerPaneSizes = this.$refs.splOuter.panes.map(i => i.size);
      const centralPaneSizes = this.$refs.splCentral.panes.map(i => i.size);
      const ret = this.getStored();
      if (this.isMobile()) {
        if (this.showTopPane()) {
          const topPaneSize = centralPaneSizes[0];
          ret.topPane = topPaneSize;
        }
      } else {
        if (this.showLeftPane()) {
          ret.leftPane = outerPaneSizes[0];
        }
        if (this.showRightPane()) {
          ret.rightPane = outerPaneSizes[outerPaneSizes.length - 1]
        }
        if (this.showBottomPane()) {
          const bottomPaneSize = centralPaneSizes[centralPaneSizes.length - 1];
          if (!this.chatStore.isEditingBigText) {
            ret.bottomPane = bottomPaneSize;
          } else {
            ret.bottomPaneBig = bottomPaneSize;
          }
        }
      }
      // console.debug("Preparing for store", ret)
      return ret
    },
    // sets concrete panel sizes
    restorePanelsSize(ret) {
      // console.debug("Restoring from", ret);
      if (this.isMobile()) {
        if (this.showTopPane()) {
          if (this.chatStore.videoMessagesEnabled) {
            this.$refs.splCentral.panes[0].size = ret.topPane;
          } else {
            this.$refs.splCentral.panes[0].size = 100
          }
        }
      } else {
        if (this.showLeftPane()) {
          this.$refs.splOuter.panes[0].size = ret.leftPane;
        }
        if (this.showRightPane()) {
          if (this.chatStore.videoMessagesEnabled) {
            this.$refs.splOuter.panes[this.$refs.splOuter.panes.length - 1].size = ret.rightPane;
          } else {
            this.$refs.splOuter.panes[this.$refs.splOuter.panes.length - 1].size = 100;
          }
        }
        if (this.showBottomPane()) {
          let bottomPaneSize;
          if (!this.chatStore.isEditingBigText) {
            bottomPaneSize = ret.bottomPane;
          } else {
            bottomPaneSize = ret.bottomPaneBig;
          }
          this.$refs.splCentral.panes[this.$refs.splCentral.panes.length - 1].size = bottomPaneSize;
        }
      }
    },
    onPanelAdd() {
      this.$refs.chatVideoRef?.recalculateLayout();

      if (this.isAllowedVideo()) {
        setSplitter(messagesSplitpanesSelector, messagesSplitterDisplayVarName, this.chatStore.videoMessagesEnabled);
      }

      // console.debug("On panel add", this.$refs.splOuter.panes);
      this.$nextTick(() => {
        const stored = this.getStored();
        // console.debug("Restoring on add", stored)
        this.restorePanelsSize(stored);
      })

    },
    onPanelRemove() {
      this.$refs.chatVideoRef?.recalculateLayout();

      if (!this.isAllowedVideo()) {
        setSplitter(messagesSplitpanesSelector, messagesSplitterDisplayVarName, true);
      }

      // console.debug("On panel removed", this.$refs.splOuter.panes);
      this.$nextTick(() => {
        const stored = this.getStored();
        // console.debug("Restoring on remove", stored)
        this.restorePanelsSize(stored);
      })
    },
    onPanelResized() {
      this.$refs.chatVideoRef?.recalculateLayout();

      this.$nextTick(() => {
        this.saveToStored(this.prepareForStore());
      })
    },
    scrollDown () {
      bus.emit(SCROLL_DOWN)
    },
    scrollDownClass() {
      if (this.isMobile()) {
        if (!this.canWriteMessage) {
          return "new-fab-b"
        } else {
          return "new-fab-t"
        }
      } else {
        return "new-fab-b"
      }
    },

    onUserTyping(data) {
      if (data.chatId == this.chatId) {
        upsertToWritingUsers(this.writingUsers, data);
        this.chatStore.usersWritingSubtitleInfo = buildWritingUsersSubtitleInfo(this.writingUsers, this.$vuetify);
      }
    },
    onCoChattedParticipantChanged(user) {
      if (this.chatStore.chatDto.tetATet && this.chatStore.chatDto.participantIds.find(participantId => participantId == user.id)) {
        const o = {
          name: user.login,
          additionalData: user.additionalData,
          avatar: user.avatar,
        }
        this.setParticipantsFields(o);
        this.chatStore.setChatDto({
          ...this.chatStore.chatDto,
          ...o,
        });
      }
    },
    openPinnedMessages() {
      bus.emit(OPEN_PINNED_MESSAGES_MODAL, {chatId: this.chatId});
    },
  },
  watch: {
    '$route': {
      handler: function (newValue, oldValue) {
        if (isChatRoute(newValue)) {
          if (newValue.params.id != oldValue.params.id) {
            console.debug("Chat id has been changed", oldValue.params.id, "->", newValue.params.id);
            if (hasLength(newValue.params.id)) {
              this.chatStore.incrementProgressCount();

              // used for
              // 1. to prevent opening ChatVideo with old (previous) chatDto that contains old chatId
              // 2. to prevent rendering MessageList and get 401
              this.partialReset(); // also unsets "this.chatEventsSubscribed"
              this.onProfileSet().then(()=>{
                this.chatStore.decrementProgressCount();
              })
            }
          }
        }
      }
    },
    'chatStore.videoMessagesEnabled': {
      handler: function (newValue, oldValue) {
        setSplitter(messagesSplitpanesSelector, messagesSplitterDisplayVarName, newValue || !this.isAllowedVideo());
      }
    },
    'chatStore.chatDto.canWriteMessage': {
      handler: function (newValue, oldValue) {
        if (newValue !== undefined) {
          this.canWriteMessage = newValue;
        }
      }
    },
  },
  created() {

  },
  async mounted() {
    this.chatStore.titleStrike = false;
    this.chatStore.title = `Chat #${this.chatId}`;
    this.chatStore.chatUsersCount = 0;
    this.chatStore.isShowSearch = true;
    this.chatStore.showChatEditButton = false;

    const videoMessages = getStoredVideoMessages();
    setSplitter(messagesSplitpanesSelector, messagesSplitterDisplayVarName, videoMessages || !this.isAllowedVideo());
    this.chatStore.videoMessagesEnabled = videoMessages;

    // before subscribing with onProfileSet() because former invokes chatEventsSubscription
    this.chatEventsSubscription = graphqlSubscriptionMixin('chatEvents', this.getGraphQlSubscriptionQuery, this.setErrorSilent, this.onNextSubscriptionElement);

    bus.on(WEBSOCKET_INITIALIZED, this.doInitialize);
    bus.on(WEBSOCKET_UNINITIALIZED, this.doUninitialize);
    bus.on(PINNED_MESSAGE_PROMOTED, this.onPinnedMessagePromoted);
    bus.on(PINNED_MESSAGE_UNPROMOTED, this.onPinnedMessageUnpromoted);
    bus.on(PINNED_MESSAGE_EDITED, this.onPinnedMessageChanged);
    bus.on(USER_TYPING, this.onUserTyping);
    bus.on(MESSAGE_BROADCAST, this.onUserBroadcast);
    bus.on(CHAT_EDITED, this.onChatChange);
    bus.on(CHAT_DELETED, this.onChatDelete);
    bus.on(REFRESH_ON_WEBSOCKET_RESTORED, this.onWsRestoredRefresh);
    bus.on(VIDEO_DIAL_STATUS_CHANGED, this.onChatDialStatusChange);
    bus.on(PARTICIPANT_DELETED, this.onParticipantDeleted);
    bus.on(CO_CHATTED_PARTICIPANT_CHANGED, this.onCoChattedParticipantChanged);

    if (this.chatStore.currentUser) {
      await this.doInitialize();
    }

    writingUsersTimerId = setInterval(()=>{
      this.writingUsers = filterOutOldWritingUsers(this.writingUsers);
      if (this.writingUsers.length == 0) {
        this.chatStore.usersWritingSubtitleInfo = null;
      } else {
        this.chatStore.usersWritingSubtitleInfo = buildWritingUsersSubtitleInfo(this.writingUsers, this.$vuetify);
      }
    }, 500);

    this.installOnFocus();
  },
  beforeUnmount() {
    this.uninstallOnFocus();

    this.doUninitialize();

    bus.off(WEBSOCKET_INITIALIZED, this.doInitialize);
    bus.off(WEBSOCKET_UNINITIALIZED, this.doUninitialize);
    bus.off(PINNED_MESSAGE_PROMOTED, this.onPinnedMessagePromoted);
    bus.off(PINNED_MESSAGE_UNPROMOTED, this.onPinnedMessageUnpromoted);
    bus.off(PINNED_MESSAGE_EDITED, this.onPinnedMessageChanged);
    bus.off(USER_TYPING, this.onUserTyping);
    bus.off(MESSAGE_BROADCAST, this.onUserBroadcast);
    bus.off(CHAT_EDITED, this.onChatChange);
    bus.off(CHAT_DELETED, this.onChatDelete);
    bus.off(REFRESH_ON_WEBSOCKET_RESTORED, this.onWsRestoredRefresh);
    bus.off(VIDEO_DIAL_STATUS_CHANGED, this.onChatDialStatusChange);
    bus.off(PARTICIPANT_DELETED, this.onParticipantDeleted);
    bus.off(CO_CHATTED_PARTICIPANT_CHANGED, this.onCoChattedParticipantChanged);

    this.chatStore.isShowSearch = false;

    this.partialReset();
    clearInterval(writingUsersTimerId);
    this.initialLoaded = false;

    this.chatStore.isEditingBigText = false;
    this.canWriteMessage = true;

    this.chatEventsSubscription = null;
  }
}
</script>


<style scoped lang="stylus">
@import "pinned.styl"

.pinned-promoted {
  font-size: 0.875rem
  position: absolute
  left 0
  margin-right 2em
  z-index: 4;
}
.message-pane {
  position: relative // needed for the correct displaying .pinned-promoted
}
.message-pane-mobile {
    align-items: unset;
}

.new-fab-b {
  position: absolute
  bottom: 20px
  right: 20px
  z-index: 1000
}

.new-fab-t {
  position: absolute
  bottom: 90px
  right: 20px
  z-index: 1000
}

@media screen and (max-width: $mobileWidth) {
    .pinned-promoted {
        margin-right unset
    }
}

.video-top-pane {
  position: relative // to work position fixed for VideoButtons in case mobile
}
</style>

<style lang="stylus">
.pinned-promoted {
  .v-alert__content{
    text-overflow: ellipsis;
    height: 20px; // to force vertical align on Chrome
  }
  .v-alert {
    padding-top 0
    padding-bottom 0
  }
}

// desktop
#root-splitpanes.splitpanes--vertical > .splitpanes__splitter {
  display: var(--splitter-v-display);
}

// mobile
#central-splitpanes.splitpanes--horizontal > .splitpanes__splitter::before {
  display: var(--splitter-v-display);
}
</style>
