import{_ as w,m as f,c_ as h,c as l,w as a,b as o,a as m,c$ as i,l as y,e as u,t as n,bW as b,j as V,k as v,d0 as $,aZ as g,s as d,a2 as T,d6 as S,d7 as P,d8 as I,d4 as c,T as k,A,B as p}from"./appMain-DAGYo8tP.js";const E={mixins:[h()],data:()=>({password:null,showInputablePassword:!1,error:""}),computed:{...f(A),showError(){return k(this.error)},login(){return this.$route.query.login}},methods:{onSubmit(){T.post("/api/aaa/password-reset-set-new",{passwordResetToken:this.$route.query.uuid,newPassword:this.password}).then(()=>(this.$router.push({name:S}),this.chatStore.fetchUserProfile().then(()=>{P(this,I())}))).catch(e=>{this.error=c(e)})},hideAlert(){this.error=""},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.password_restoration"),d(this.$vuetify.locale.t("$vuetify.password_restoration"))}},watch:{"$vuetify.locale.current":{handler:function(e,t){this.setTopTitle()}}},mounted(){this.setTopTitle()},beforeUnmount(){this.chatStore.title=null,d(null),this.showInputablePassword=!1,this.error=""}};function U(e,t,q,B,C,r){return p(),l(g,{"max-width":"640",class:"px-2 pt-2"},{default:a(()=>[o($,{"fast-fail":"",onSubmit:t[5]||(t[5]=v(s=>r.onSubmit(),["prevent"]))},{default:a(()=>[o(i,{disabled:"",onInput:t[0]||(t[0]=s=>r.hideAlert()),modelValue:r.login,"onUpdate:modelValue":t[1]||(t[1]=s=>r.login=s),label:e.$vuetify.locale.t("$vuetify.login"),rules:[e.rules.required],variant:"underlined"},null,8,["modelValue","label","rules"]),o(i,{onInput:t[3]||(t[3]=s=>r.hideAlert()),modelValue:e.password,"onUpdate:modelValue":t[4]||(t[4]=s=>e.password=s),type:e.showInputablePassword?"text":"password",label:e.$vuetify.locale.t("$vuetify.password"),rules:[e.rules.required,e.rules.min],variant:"underlined"},{append:a(()=>[o(y,{onClick:t[2]||(t[2]=s=>e.showInputablePassword=!e.showInputablePassword),class:"mx-1 ml-3"},{default:a(()=>[u(n(e.showInputablePassword?"mdi-eye":"mdi-eye-off"),1)]),_:1})]),_:1},8,["modelValue","type","label","rules"]),r.showError?(p(),l(b,{key:0,density:"compact",type:"error",text:e.error},null,8,["text"])):m("",!0),o(V,{type:"submit",color:"primary",block:"",class:"mt-2"},{default:a(()=>[u(n(e.$vuetify.locale.t("$vuetify.set_new_password")),1)]),_:1})]),_:1})]),_:1})}const L=w(E,[["render",U]]);export{L as default};
