http:
  services:
    frontend-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:3000
    public-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:3100

    aaa-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:8060
    chat-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:1235
    storage-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:1236
    video-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:1237
    livekit-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:7880
    event-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:1238
    notification-service:
      loadBalancer:
        servers:
          - url: http://host.docker.internal:1230
    minio-service:
      loadBalancer:
        servers:
          - url: http://minio:9000
    jaeger-ui-service:
      loadBalancer:
        servers:
          - url: http://jaeger:16686
    minio-ui-service:
      loadBalancer:
        servers:
          - url: http://minio:9001
    rabbitmq-ui-service:
      loadBalancer:
        servers:
          - url: http://rabbitmq:15672
    postgresql-ui-service:
      loadBalancer:
        servers:
          - url: http://pgadmin:8080

    opensearch-dashboards-ui-service:
      loadBalancer:
        servers:
          - url: http://dashboards:5601

  routers:
    frontend-router:
      rule: "PathPrefix(`/`)"
      service: frontend-service
      middlewares:
        - "retry-middleware"
    public-router:
      rule: "PathPrefix(`/public`)"
      service: public-service
      middlewares:
        - "retry-middleware"

    public-router-robots:
      rule: "Path(`/robots.txt`)"
      service: public-service
      middlewares:
        - "retry-middleware"

    public-router-sitemap:
      rule: "Path(`/sitemap.xml`)"
      service: public-service
      middlewares:
        - "retry-middleware"

    aaa-router:
      rule: "PathPrefix(`/api/aaa`)"
      service: aaa-service
      middlewares:
        - "retry-middleware"
    chat-router:
      rule: "PathPrefix(`/api/chat`)"
      service: chat-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"
    blog-router:
      rule: "PathPrefix(`/api/blog`)"
      service: chat-service
      middlewares:
        - "retry-middleware"
    storage-router:
      rule: "PathPrefix(`/api/storage`)"
      service: storage-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"
    video-router:
      rule: "PathPrefix(`/api/video`)"
      service: video-service
      middlewares:
        - "auth-middleware"
    notification-router:
      rule: "PathPrefix(`/api/notification`)"
      service: notification-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"

    chat-public-router:
      rule: "PathPrefix(`/api/chat/public`)"
      service: chat-service
      middlewares:
        - "retry-middleware"
    storage-public-router:
      rule: "PathPrefix(`/api/storage/public`)"
      service: storage-service
      middlewares:
        - "retry-middleware"
    livekit-router:
      rule: "PathPrefix(`/api/livekit`)"
      service: livekit-service
      middlewares:
        - "livekit-strip-prefix-middleware"
        - "retry-middleware"
    event-graphql-router:
      rule: "PathPrefix(`/event/playground`) || PathPrefix(`/api/event/graphql`)"
      service: event-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"
    event-public-router:
      rule: "PathPrefix(`/api/event/public`)"
      service: event-service
      middlewares:
        - "retry-middleware"
    notification-public-router:
      rule: "PathPrefix(`/api/notification/public`)"
      service: notification-service
      middlewares:
        - "retry-middleware"
    minio-router:
      rule: "PathPrefix(`/api/s3`)"
      service: minio-service
      middlewares:
        - "minio-strip-prefix-middleware"
        - "retry-middleware"
        - "minio-fix-host-middleware"
    jaeger-ui-router:
      rule: "PathPrefix(`/jaeger`)"
      service: jaeger-ui-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"
    minio-ui-router:
      rule: "PathPrefix(`/minio`)"
      service: minio-ui-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"
        - "minio-ui-strip-prefix-middleware"
    rabbitmq-ui-router:
      rule: "PathPrefix(`/rabbitmq`)"
      service: rabbitmq-ui-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"
    postgresql-ui-router:
      rule: "PathPrefix(`/postgresql`)"
      service: postgresql-ui-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"
    opensearch-dashboards-ui-router:
      rule: "PathPrefix(`/opensearch-dashboards`)"
      service: opensearch-dashboards-ui-service
      middlewares:
        - "auth-middleware"
        - "retry-middleware"

  middlewares:
    livekit-strip-prefix-middleware:
      stripPrefix:
        prefixes:
          - "/api/livekit"
    minio-strip-prefix-middleware:
      stripPrefix:
        prefixes:
          - "/api/s3"
    minio-fix-host-middleware:
      headers:
        customRequestHeaders:
          Host: "127.0.0.1:39000"
    minio-ui-strip-prefix-middleware:
      stripPrefix:
        prefixes:
          - "/minio"
    retry-middleware:
      retry:
        attempts: 4
    auth-middleware:
      forwardAuth:
        address: "http://host.docker.internal:8060/internal/profile/auth"
        headerField: "X-Auth-UserId"
        authRequestHeaders:
          - "Accept"
          - "Cookie"
          - "uber-trace-id"
        authResponseHeadersRegex: "^X-Auth-"
