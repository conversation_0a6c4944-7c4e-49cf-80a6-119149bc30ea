import{_ as r,m as l,c,w as u,e as s,f as a,t as i,k as d,aZ as f,s as o,d1 as _,d2 as m,A as h,B as p}from"./appMain-DAGYo8tP.js";const $={computed:{...l(h)},methods:{resend(){return m},onResendClick(){this.$router.push({name:_})},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.registration_pending_confirmation_title"),o(this.$vuetify.locale.t("$vuetify.registration_pending_confirmation_title"))}},watch:{"$vuetify.locale.current":{handler:function(e,t){this.setTopTitle()}}},mounted(){this.setTopTitle()},beforeUnmount(){this.chatStore.title=null,o(null)}},v=["href"];function y(e,t,g,T,k,n){return p(),c(f,{"max-width":"800",class:"px-2 pt-2"},{default:u(()=>[s(i(e.$vuetify.locale.t("$vuetify.registration_pending_confirmation"))+" ",1),a("div",null,[s(i(e.$vuetify.locale.t("$vuetify.request_resend_confirmation_email_text"))+" ",1),a("a",{class:"colored-link",href:n.resend(),onClick:t[0]||(t[0]=d(w=>n.onResendClick(),["prevent"]))},i(e.$vuetify.locale.t("$vuetify.request_resend_confirmation_email")),9,v)])]),_:1})}const S=r($,[["render",y]]);export{S as default};
