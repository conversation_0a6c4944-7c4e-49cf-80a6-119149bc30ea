spring.security:
  oauth2:
    client:
      registration:
        keycloak:
          client-id: my_client2
          client-secret: "z8cr0Nw2z8c7OpSvEix75GgZeDrWJi60"
          redirect-uri: "{baseUrl}/api/aaa/login/oauth2/code/{registrationId}"
      provider:
        keycloak:
          issuer-uri: http://localhost:8484/realms/my_realm2

custom.role-mappings:
  keycloak:
    - their: "default-roles-my_realm2"
      our: "ROLE_USER"
    - their: "USER"
      our: "ROLE_ADMIN"
