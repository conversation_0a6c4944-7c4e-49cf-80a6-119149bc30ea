server:
  apiAddress: ":1237"
  shutdownTimeout: "10s"
  bodyLimit: "100G"

logger:
  level: info
  dir: "log"
  filename: "file.log"
  writeToFile: true

# Rest client
http:
  maxIdleConns: 2
  idleConnTimeout: '10s'
  disableCompression: false

auth:
  exclude:
    - "^/api/video/public.*"
    - "^/internal/.*"

postgresql:
  # https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING
  url: "postgres://video:videoPazZw0rd@localhost:35432/video?sslmode=disable&application_name=video-app"
  maxOpenConnections: 16
  maxIdleConnections: 4
  maxLifetime: 30s

otlp:
  endpoint: "localhost:34317"

chat:
  url:
    base: "http://localhost:1235"
    access: "/internal/access"
    isChatAdmin: "/internal/is-admin"
    doesParticipantBelongToChat: "/internal/does-participant-belong-to-chat"
    chatParticipants: "/internal/participant-ids"
    chatInviteName: "/internal/name-for-invite"
    chatBasicInfoPath: "/internal/basic"

aaa:
  url:
    base: "http://localhost:8060"
    getUsers: "/internal/user/list"

storage:
  url:
    base: "http://localhost:1236"
    s3: "/internal/s3"

livekit:
  api:
    key: "APIznJxWShGW3Kt"
    secret: "KEUUtCDVRqXk9me0Ok94g8G9xwtnjMeUxfNMy8dow6iA"
  url: "http://localhost:7880"

frontend:
  videoResolution: h720
  screenResolution: "null"
  videoSimulcast: true
  screenSimulcast: false
  roomDynacast: true
  roomAdaptiveStream: true
  # set null to trigger browser's default, otherwise livekit-js is going to set vp8
  codec: "null"

rabbitmq:
  url: "amqp://videoChat:videoChatPazZw0rd@127.0.0.1:36672"
  debug: true


# used to forbid records by guests on demo server
onlyRoleAdminRecording: false
recordPreset: H264_1080P_30

videoTokenValidTime: 1h

redis:
  address: :36379
  password: ""
  db: 4
  maxRetries: 10000

schedulers:
  videoCallUsersCountNotifierTask:
    enabled: true
    cron: "*/2 * * * * *"
    expiration: "30m"
  chatDialerTask:
    enabled: true
    cron: "*/5 * * * * *"
    removeTemporaryUserCallStatusAfter: 20s
    removeDanglingCallStatusBeingInvitedAfter: 5m
    expiration: "30m"
  videoRecordingNotifierTask:
    enabled: true
    cron: "*/10 * * * * *"
    expiration: "30m"
  usersInVideoStatusNotifierTask:
    enabled: true
    # more or less close to aaa's user online and less than closing empty room timeout in livekit
    cron: "*/5 * * * * *"
    expiration: "30m"
  synchronizeWithLivekitTask:
    enabled: true
    cron: "*/10 * * * * *"
    orphanUserIteration: 3 # how many iteration must it take on orphaned "inCall" user to assign "cancellig status" state
    expiration: "30m"
