import{_ as $,m as I,a_ as C,A as _,c as a,w as s,b as n,G as k,a8 as g,a7 as V,b2 as w,a6 as M,b3 as R,B as o,b4 as x,e as h,t as d,l as U,z as f,cL as p,a2 as b,a as y,V as S,bh as P,cW as D,cX as L,cY as N,j as v,i as A,cZ as B}from"./appMain-DAGYo8tP.js";const E={mixins:[C()],computed:{...I(_)},methods:{className(){return"user-list-item-context-menu"},onShowContextMenu(e,t){this.onShowContextMenuBase(e,t)},onCloseContextMenu(){this.onCloseContextMenuBase()},getContextMenuItems(){var t,r,m;const e=[];return this.menuableItem&&(this.isMobile()&&e.push({title:this.$vuetify.locale.t("$vuetify.close"),icon:"mdi-close",action:()=>{this.onCloseContextMenu()}}),e.push({title:this.$vuetify.locale.t("$vuetify.user_open_chat"),icon:"mdi-message-text-outline",action:()=>this.$emit("tetATet",this.menuableItem)}),this.menuableItem.canRemoveSessions&&e.push({title:this.$vuetify.locale.t("$vuetify.remove_sessions"),icon:"mdi-logout",action:()=>this.$emit("removeSessions",this.menuableItem)}),this.menuableItem.canLock&&((t=this.menuableItem)!=null&&t.additionalData.locked?e.push({title:this.$vuetify.locale.t("$vuetify.unlock_user"),icon:"mdi-lock-open-outline",action:()=>this.$emit("unlockUser",this.menuableItem)}):e.push({title:this.$vuetify.locale.t("$vuetify.lock_user"),icon:"mdi-lock",action:()=>this.$emit("lockUser",this.menuableItem)})),this.menuableItem.canEnable&&((r=this.menuableItem)!=null&&r.additionalData.enabled?e.push({title:this.$vuetify.locale.t("$vuetify.disable_user"),icon:"mdi-power-off",action:()=>this.$emit("disableUser",this.menuableItem)}):e.push({title:this.$vuetify.locale.t("$vuetify.enable_user"),icon:"mdi-power",action:()=>this.$emit("enableUser",this.menuableItem)})),this.menuableItem.canConfirm&&((m=this.menuableItem)!=null&&m.additionalData.confirmed?e.push({title:this.$vuetify.locale.t("$vuetify.unconfirm_user"),icon:"mdi-close-thick",action:()=>this.$emit("unconfirmUser",this.menuableItem)}):e.push({title:this.$vuetify.locale.t("$vuetify.confirm_user"),icon:"mdi-check-bold",action:()=>this.$emit("confirmUser",this.menuableItem)})),this.menuableItem.canDelete&&e.push({title:this.$vuetify.locale.t("$vuetify.delete_user"),icon:"mdi-delete",iconColor:"error",action:()=>this.$emit("deleteUser",this.menuableItem)}),this.menuableItem.canChangeRole&&e.push({title:this.$vuetify.locale.t("$vuetify.change_roles"),icon:"mdi-account-edit",action:()=>this.$emit("changeRole",this.menuableItem)}),this.menuableItem.canSetPassword&&e.push({title:this.$vuetify.locale.t("$vuetify.set_password"),icon:"mdi-lock-reset",action:()=>this.$emit("setPassword",this.menuableItem)})),e}}};function G(e,t,r,m,l,u){return o(),a(R,{class:M(u.className()),"model-value":e.showContextMenu,transition:!1,"open-on-click":!1,"open-on-focus":!1,"open-on-hover":!1,"open-delay":0,"close-delay":0,"close-on-back":!1,"onUpdate:modelValue":e.onUpdate},{default:s(()=>[n(w,null,{default:s(()=>[(o(!0),k(V,null,g(u.getContextMenuItems(),(i,c)=>(o(),a(x,{key:c,onClick:i.action},{prepend:s(()=>[n(U,{color:i.iconColor},{default:s(()=>[h(d(i.icon),1)]),_:2},1032,["color"])]),title:s(()=>[h(d(i.title),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1},8,["class","model-value","onUpdate:modelValue"])}const O=$(E,[["render",G]]),T={data(){return{show:!1,user:null,allPossibleRoles:[],chosenRoles:[],loading:!1}},methods:{showModal(e){this.show=!0,this.user=e,this.chosenRoles=e.additionalData.roles,this.requestAllPossibleRolesIfNeed()},closeModal(){this.show=!1,this.user=null,this.chosenRoles=[]},requestAllPossibleRolesIfNeed(){this.allPossibleRoles.length||(this.loading=!0,b.get("/api/aaa/user/role").then(e=>{this.allPossibleRoles=e.data}).finally(()=>{this.loading=!1}))},changeRole(){this.loading=!0,b.put("/api/aaa/user/role",{userId:this.user.id,roles:this.chosenRoles}).then(()=>{this.closeModal()}).finally(()=>{this.loading=!1})}},mounted(){f.on(p,this.showModal)},beforeUnmount(){f.off(p,this.showModal)}};function q(e,t,r,m,l,u){return o(),a(B,{modelValue:l.show,"onUpdate:modelValue":t[3]||(t[3]=i=>l.show=i),"max-width":"440",persistent:""},{default:s(()=>{var i;return[l.show?(o(),a(S,{key:0,title:e.$vuetify.locale.t("$vuetify.change_roles_for",(i=l.user)==null?void 0:i.login)},{default:s(()=>[n(L,{class:"pb-0"},{default:s(()=>[l.loading?(o(),a(D,{key:1,class:"ma-4",indeterminate:"",color:"primary"})):(o(),a(P,{key:0,items:l.allPossibleRoles,label:"Select roles",modelValue:l.chosenRoles,"onUpdate:modelValue":t[0]||(t[0]=c=>l.chosenRoles=c),variant:"outlined",density:"compact",color:"primary",multiple:""},null,8,["items","modelValue"]))]),_:1}),n(A,null,{default:s(()=>[n(N),l.chosenRoles.length?(o(),a(v,{key:0,variant:"flat",color:"primary",onClick:t[1]||(t[1]=c=>u.changeRole())},{default:s(()=>[h(d(e.$vuetify.locale.t("$vuetify.ok")),1)]),_:1})):y("",!0),n(v,{variant:"flat",color:"red",onClick:t[2]||(t[2]=c=>u.closeModal())},{default:s(()=>[h(d(e.$vuetify.locale.t("$vuetify.close")),1)]),_:1})]),_:1})]),_:1},8,["title"])):y("",!0)]}),_:1},8,["modelValue"])}const j=$(T,[["render",q]]);export{O as U,j as a};
