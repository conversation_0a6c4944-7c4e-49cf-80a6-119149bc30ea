// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package client

import (
	"context"

	"github.com/livekit/protocol/livekit"
	mock "github.com/stretchr/testify/mock"
)

// NewMockLivekitRoomClient creates a new instance of MockLivekitRoomClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLivekitRoomClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLivekitRoomClient {
	mock := &MockLivekitRoomClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockLivekitRoomClient is an autogenerated mock type for the LivekitRoomClient type
type MockLivekitRoomClient struct {
	mock.Mock
}

type MockLivekitRoomClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLivekitRoomClient) EXPECT() *MockLivekitRoomClient_Expecter {
	return &MockLivekitRoomClient_Expecter{mock: &_m.Mock}
}

// ListParticipants provides a mock function for the type MockLivekitRoomClient
func (_mock *MockLivekitRoomClient) ListParticipants(ctx context.Context, req *livekit.ListParticipantsRequest) (*livekit.ListParticipantsResponse, error) {
	ret := _mock.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListParticipants")
	}

	var r0 *livekit.ListParticipantsResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.ListParticipantsRequest) (*livekit.ListParticipantsResponse, error)); ok {
		return returnFunc(ctx, req)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.ListParticipantsRequest) *livekit.ListParticipantsResponse); ok {
		r0 = returnFunc(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*livekit.ListParticipantsResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *livekit.ListParticipantsRequest) error); ok {
		r1 = returnFunc(ctx, req)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLivekitRoomClient_ListParticipants_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListParticipants'
type MockLivekitRoomClient_ListParticipants_Call struct {
	*mock.Call
}

// ListParticipants is a helper method to define mock.On call
//   - ctx context.Context
//   - req *livekit.ListParticipantsRequest
func (_e *MockLivekitRoomClient_Expecter) ListParticipants(ctx interface{}, req interface{}) *MockLivekitRoomClient_ListParticipants_Call {
	return &MockLivekitRoomClient_ListParticipants_Call{Call: _e.mock.On("ListParticipants", ctx, req)}
}

func (_c *MockLivekitRoomClient_ListParticipants_Call) Run(run func(ctx context.Context, req *livekit.ListParticipantsRequest)) *MockLivekitRoomClient_ListParticipants_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *livekit.ListParticipantsRequest
		if args[1] != nil {
			arg1 = args[1].(*livekit.ListParticipantsRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockLivekitRoomClient_ListParticipants_Call) Return(listParticipantsResponse *livekit.ListParticipantsResponse, err error) *MockLivekitRoomClient_ListParticipants_Call {
	_c.Call.Return(listParticipantsResponse, err)
	return _c
}

func (_c *MockLivekitRoomClient_ListParticipants_Call) RunAndReturn(run func(ctx context.Context, req *livekit.ListParticipantsRequest) (*livekit.ListParticipantsResponse, error)) *MockLivekitRoomClient_ListParticipants_Call {
	_c.Call.Return(run)
	return _c
}

// ListRooms provides a mock function for the type MockLivekitRoomClient
func (_mock *MockLivekitRoomClient) ListRooms(ctx context.Context, req *livekit.ListRoomsRequest) (*livekit.ListRoomsResponse, error) {
	ret := _mock.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListRooms")
	}

	var r0 *livekit.ListRoomsResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.ListRoomsRequest) (*livekit.ListRoomsResponse, error)); ok {
		return returnFunc(ctx, req)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.ListRoomsRequest) *livekit.ListRoomsResponse); ok {
		r0 = returnFunc(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*livekit.ListRoomsResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *livekit.ListRoomsRequest) error); ok {
		r1 = returnFunc(ctx, req)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLivekitRoomClient_ListRooms_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRooms'
type MockLivekitRoomClient_ListRooms_Call struct {
	*mock.Call
}

// ListRooms is a helper method to define mock.On call
//   - ctx context.Context
//   - req *livekit.ListRoomsRequest
func (_e *MockLivekitRoomClient_Expecter) ListRooms(ctx interface{}, req interface{}) *MockLivekitRoomClient_ListRooms_Call {
	return &MockLivekitRoomClient_ListRooms_Call{Call: _e.mock.On("ListRooms", ctx, req)}
}

func (_c *MockLivekitRoomClient_ListRooms_Call) Run(run func(ctx context.Context, req *livekit.ListRoomsRequest)) *MockLivekitRoomClient_ListRooms_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *livekit.ListRoomsRequest
		if args[1] != nil {
			arg1 = args[1].(*livekit.ListRoomsRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockLivekitRoomClient_ListRooms_Call) Return(listRoomsResponse *livekit.ListRoomsResponse, err error) *MockLivekitRoomClient_ListRooms_Call {
	_c.Call.Return(listRoomsResponse, err)
	return _c
}

func (_c *MockLivekitRoomClient_ListRooms_Call) RunAndReturn(run func(ctx context.Context, req *livekit.ListRoomsRequest) (*livekit.ListRoomsResponse, error)) *MockLivekitRoomClient_ListRooms_Call {
	_c.Call.Return(run)
	return _c
}

// MutePublishedTrack provides a mock function for the type MockLivekitRoomClient
func (_mock *MockLivekitRoomClient) MutePublishedTrack(ctx context.Context, req *livekit.MuteRoomTrackRequest) (*livekit.MuteRoomTrackResponse, error) {
	ret := _mock.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for MutePublishedTrack")
	}

	var r0 *livekit.MuteRoomTrackResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.MuteRoomTrackRequest) (*livekit.MuteRoomTrackResponse, error)); ok {
		return returnFunc(ctx, req)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.MuteRoomTrackRequest) *livekit.MuteRoomTrackResponse); ok {
		r0 = returnFunc(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*livekit.MuteRoomTrackResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *livekit.MuteRoomTrackRequest) error); ok {
		r1 = returnFunc(ctx, req)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLivekitRoomClient_MutePublishedTrack_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MutePublishedTrack'
type MockLivekitRoomClient_MutePublishedTrack_Call struct {
	*mock.Call
}

// MutePublishedTrack is a helper method to define mock.On call
//   - ctx context.Context
//   - req *livekit.MuteRoomTrackRequest
func (_e *MockLivekitRoomClient_Expecter) MutePublishedTrack(ctx interface{}, req interface{}) *MockLivekitRoomClient_MutePublishedTrack_Call {
	return &MockLivekitRoomClient_MutePublishedTrack_Call{Call: _e.mock.On("MutePublishedTrack", ctx, req)}
}

func (_c *MockLivekitRoomClient_MutePublishedTrack_Call) Run(run func(ctx context.Context, req *livekit.MuteRoomTrackRequest)) *MockLivekitRoomClient_MutePublishedTrack_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *livekit.MuteRoomTrackRequest
		if args[1] != nil {
			arg1 = args[1].(*livekit.MuteRoomTrackRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockLivekitRoomClient_MutePublishedTrack_Call) Return(muteRoomTrackResponse *livekit.MuteRoomTrackResponse, err error) *MockLivekitRoomClient_MutePublishedTrack_Call {
	_c.Call.Return(muteRoomTrackResponse, err)
	return _c
}

func (_c *MockLivekitRoomClient_MutePublishedTrack_Call) RunAndReturn(run func(ctx context.Context, req *livekit.MuteRoomTrackRequest) (*livekit.MuteRoomTrackResponse, error)) *MockLivekitRoomClient_MutePublishedTrack_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveParticipant provides a mock function for the type MockLivekitRoomClient
func (_mock *MockLivekitRoomClient) RemoveParticipant(ctx context.Context, req *livekit.RoomParticipantIdentity) (*livekit.RemoveParticipantResponse, error) {
	ret := _mock.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RemoveParticipant")
	}

	var r0 *livekit.RemoveParticipantResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.RoomParticipantIdentity) (*livekit.RemoveParticipantResponse, error)); ok {
		return returnFunc(ctx, req)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *livekit.RoomParticipantIdentity) *livekit.RemoveParticipantResponse); ok {
		r0 = returnFunc(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*livekit.RemoveParticipantResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *livekit.RoomParticipantIdentity) error); ok {
		r1 = returnFunc(ctx, req)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLivekitRoomClient_RemoveParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveParticipant'
type MockLivekitRoomClient_RemoveParticipant_Call struct {
	*mock.Call
}

// RemoveParticipant is a helper method to define mock.On call
//   - ctx context.Context
//   - req *livekit.RoomParticipantIdentity
func (_e *MockLivekitRoomClient_Expecter) RemoveParticipant(ctx interface{}, req interface{}) *MockLivekitRoomClient_RemoveParticipant_Call {
	return &MockLivekitRoomClient_RemoveParticipant_Call{Call: _e.mock.On("RemoveParticipant", ctx, req)}
}

func (_c *MockLivekitRoomClient_RemoveParticipant_Call) Run(run func(ctx context.Context, req *livekit.RoomParticipantIdentity)) *MockLivekitRoomClient_RemoveParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *livekit.RoomParticipantIdentity
		if args[1] != nil {
			arg1 = args[1].(*livekit.RoomParticipantIdentity)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockLivekitRoomClient_RemoveParticipant_Call) Return(removeParticipantResponse *livekit.RemoveParticipantResponse, err error) *MockLivekitRoomClient_RemoveParticipant_Call {
	_c.Call.Return(removeParticipantResponse, err)
	return _c
}

func (_c *MockLivekitRoomClient_RemoveParticipant_Call) RunAndReturn(run func(ctx context.Context, req *livekit.RoomParticipantIdentity) (*livekit.RemoveParticipantResponse, error)) *MockLivekitRoomClient_RemoveParticipant_Call {
	_c.Call.Return(run)
	return _c
}
