.splitpanes{
  display:flex;
  width:100%;
  height:100%
}
.splitpanes--vertical{flex-direction:row}
.splitpanes--horizontal{flex-direction:column}
.splitpanes--dragging *{user-select:none}
.splitpanes__pane{width:100%;height:100%;overflow:hidden}

.splitpanes {
  background-color: #f2f2f2;

  &__pane {
    justify-content: center;
    align-items: center;
    display: flex;
  }

  &__splitter {background-color: #ccc;position: relative;}

  &__splitter:before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    transition: 0.4s;
  }
  &--vertical > &__splitter:before {left: -30px;right: -30px; z-index: 10;}
  &--horizontal > &__splitter:before {top: -30px;bottom: -30px; z-index: 10;}
  &--vertical > &__splitter {cursor: col-resize;}
  &--horizontal > &__splitter {cursor: row-resize;}
  &__splitter:hover:before {background-color: rgba(255, 0, 0, 0.3);}
}
