{"name": "frontend", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/free-brands-svg-icons": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/vue-fontawesome": "^3.0.8", "@lifeomic/attempt": "^3.1.0", "@mdi/font": "7.4.47", "@tiptap/extension-bullet-list": "2.6.4", "@tiptap/extension-code-block": "2.6.4", "@tiptap/extension-color": "2.6.4", "@tiptap/extension-highlight": "2.6.4", "@tiptap/extension-image": "2.6.4", "@tiptap/extension-link": "2.6.4", "@tiptap/extension-list-item": "2.6.4", "@tiptap/extension-mention": "2.6.4", "@tiptap/extension-placeholder": "2.6.4", "@tiptap/extension-text-style": "2.6.4", "@tiptap/extension-underline": "2.6.4", "@tiptap/pm": "2.6.4", "@tiptap/starter-kit": "2.6.4", "@tiptap/suggestion": "2.6.4", "@tiptap/vue-3": "2.6.4", "axios": "^1.7.9", "chroma-js": "^3.1.2", "core-js": "^3.39.0", "date-fns": "^4.1.0", "graphql-ws": "6.0.4", "he": "^1.2.0", "livekit-client": "2.13.3", "lodash": "^4.17.21", "mark.js": "^8.11.1", "mitt": "^3.0.1", "pinia": "2.3.0", "recordrtc": "^5.6.2", "rect-scaler": "^1.1.0", "splitpanes": "^3.1.5", "typeface-roboto": "1.1.13", "uuid": "^11.0.3", "vue": "3.5.13", "vue-router": "4.5.0", "vuetify": "3.8.1"}, "type": "module", "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "sass": "1.83.0", "stylus": "^0.64.0", "vite": "^6.0.3", "vite-plugin-vuetify": "^2.0.4"}}