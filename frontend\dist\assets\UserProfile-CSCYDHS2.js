import{_ as V,$ as O,g as z,m as $,bV as q,ab as R,c as l,a as n,w as s,b as a,G as m,f,d as g,e as c,t as d,cG as B,n as F,l as _,cH as H,p as S,j as I,a7 as C,a8 as Q,i as A,cI as p,a5 as w,z as h,ai as T,ah as E,c6 as G,cJ as W,a2 as o,y as L,cK as K,s as M,aL as j,T as b,a9 as Z,cL as J,aO as X,aP as Y,a0 as ee,A as te,B as r}from"./appMain-DAGYo8tP.js";import{U as se,a as ie}from"./UserRoleModal-COewB_-i.js";const ae={components:{UserRoleModal:ie,UserListContextMenu:se},mixins:[q("userStatusInUserProfile"),R()],data(){return{viewableUser:null,online:!1,isInVideo:!1,userProfileEventsSubscription:null,initialized:!1}},computed:{...$(te),userId(){return this.$route.params.id},ava(){const e=this.viewableUser;if(e)return b(e.avatarBig)?e.avatarBig:b(e.avatar)?e.avatar:null},hasAva(){const e=this.viewableUser;return b(e==null?void 0:e.avatarBig)||b(e==null?void 0:e.avatar)}},methods:{getLoginColoredStyle:z,getHumanReadableDate:O,getUserNamePretty(){var e,t;return ee(this.viewableUser)?"<s>"+((e=this.viewableUser)==null?void 0:e.login)+"</s>":(t=this.viewableUser)==null?void 0:t.login},loadUser(){this.viewableUser=null,o.get(`/api/aaa/user/${this.userId}`,{signal:this.requestAbortController.signal}).then(e=>{e.status==204?(this.$router.push({name:L}),this.setWarning(this.$vuetify.locale.t("$vuetify.user_not_found"))):this.viewableUser=e.data})},unlockUser(e){o.post("/api/aaa/user/lock",{userId:e.id,lock:!1},{signal:this.requestAbortController.signal})},lockUser(e){o.post("/api/aaa/user/lock",{userId:e.id,lock:!0},{signal:this.requestAbortController.signal})},unconfirmUser(e){o.post("/api/aaa/user/confirm",{userId:e.id,confirm:!1},{signal:this.requestAbortController.signal})},confirmUser(e){o.post("/api/aaa/user/confirm",{userId:e.id,confirm:!0},{signal:this.requestAbortController.signal})},deleteUser(e){h.emit(X,{buttonName:this.$vuetify.locale.t("$vuetify.delete_btn"),title:this.$vuetify.locale.t("$vuetify.delete_user_title",e.id),text:this.$vuetify.locale.t("$vuetify.delete_user_text",e.login),actionFunction:t=>{t.loading=!0,o.delete("/api/aaa/user",{params:{userId:e.id},signal:this.requestAbortController.signal}).then(()=>{h.emit(Y)}).finally(()=>{t.loading=!1})}})},changeRole(e){h.emit(J,e)},removeSessions(e){o.delete("/api/aaa/sessions",{params:{userId:e.id},signal:this.requestAbortController.signal})},tetATetUser(e){this.tetATet(e.id)},tetATet(e){o.put(`/api/chat/tet-a-tet/${e}`,{signal:this.requestAbortController.signal}).then(t=>{this.$router.push({name:Z,params:{id:t.data.id}})})},onUserStatusChanged(e){e&&(e==null||e.forEach(t=>{t.online!==null&&this.userId==t.userId&&(this.online=t.online),t.isInVideo!==null&&this.userId==t.userId&&(this.isInVideo=t.isInVideo)}))},getUserIdsSubscribeTo(){return[this.userId]},displayShortInfo(e){return b(e.shortInfo)},shouldShowBound(){const e=j(this.viewableUser.oauth2Identifiers);delete e["@class"];let t=!1;for(const v in e)if(b(e[v])){t=!0;break}return t&&(this.chatStore.availableOAuth2Providers.includes("vkontakte")||this.chatStore.availableOAuth2Providers.includes("facebook")||this.chatStore.availableOAuth2Providers.includes("google")||this.chatStore.availableOAuth2Providers.includes("keycloak"))},setMainTitle(){const e=this.$vuetify.locale.t("$vuetify.user_profile");this.chatStore.title=e,M(e)},unsetMainTitle(){this.chatStore.title=null,M(null)},onLoggedOut(){this.graphQlUserStatusUnsubscribe(),this.userProfileEventsSubscription.graphQlUnsubscribe()},onProfileSet(){this.loadUser(),this.graphQlUserStatusSubscribe(),this.userProfileEventsSubscription.graphQlSubscribe(),this.requestStatuses()},doInitialize(){this.initialized||(this.initialized=!0,this.onProfileSet())},doUninitialize(){this.initialized&&(this.onLoggedOut(),this.initialized=!1)},canDrawUsers(){return!!this.chatStore.currentUser},getGraphQlSubscriptionQuery(){return`
                subscription {
                  userAccountEvents(userIdsFilter: ${this.getUserIdsSubscribeTo()}) {
                    userAccountEvent {
                      ${K()},
                      ... on UserDeletedDto {
                        id
                      }
                    }
                    eventType
                  }
                }
            `},onNextSubscriptionElement(e){var v;const t=(v=e.data)==null?void 0:v.userAccountEvents;t.eventType==="user_account_changed"?this.onEditUser(t.userAccountEvent):t.eventType==="user_account_deleted"&&this.onDeleteUser(t.userAccountEvent)},onDeleteUser(e){this.$router.push({name:L})},onEditUser(e){this.viewableUser=e},onFocus(){this.updateLastUpdateDateTime(),this.chatStore.currentUser&&this.requestStatuses()},requestStatuses(){this.$nextTick(()=>{this.triggerUsesStatusesEvents(this.userId,this.requestAbortController.signal)})},onShowContextMenu(e){this.chatStore.currentUser&&this.$refs.contextMenuRef.onShowContextMenu(e,this.viewableUser)},enableUser(e){o.post("/api/aaa/user/enable",{userId:e.id,enable:!0},{signal:this.requestAbortController.signal})},disableUser(e){o.post("/api/aaa/user/enable",{userId:e.id,enable:!1},{signal:this.requestAbortController.signal})},setPassword(e){h.emit(W,{userId:e.id,userName:e.login})}},mounted(){this.userProfileEventsSubscription=G("userProfileEvents",this.getGraphQlSubscriptionQuery,this.setErrorSilent,this.onNextSubscriptionElement),h.on(T,this.doUninitialize),h.on(E,this.doInitialize),this.setMainTitle(),this.canDrawUsers()&&this.doInitialize(),this.installOnFocus()},beforeUnmount(){this.uninstallOnFocus(),this.doUninitialize(),h.off(T,this.doUninitialize),h.off(E,this.doInitialize),this.unsetMainTitle(),this.viewableUser=null,this.online=!1,this.isInVideo=!1,this.userProfileEventsSubscription=null},watch:{"$vuetify.locale.current":{handler:function(e,t){this.setMainTitle()}}}},re={class:"d-flex flex-wrap"},ne=["innerHTML"],le={class:"ml-2 mb-2 d-flex flex-row align-self-end"},oe={key:0,class:"text-grey d-flex flex-row"},ue={class:"ml-1"},ce={key:1,class:"text-grey d-flex flex-row"},de={class:"ml-1"},he=["innerHTML"];function fe(e,t,v,pe,i,u){const U=w("font-awesome-icon"),P=w("UserListContextMenu"),D=w("UserRoleModal");return i.viewableUser?(r(),l(S,{key:0,fluid:""},{default:s(()=>{var x;return[a(S,{class:"d-flex justify-space-around flex-column py-0 user-self-settings-container",fluid:""},{default:s(()=>[a(g,{class:"title px-0 pb-0"},{default:s(()=>[c(d(e.$vuetify.locale.t("$vuetify.user_profile"))+" #"+d(i.viewableUser.id),1)]),_:1}),u.hasAva?(r(),l(B,{key:0,src:u.ava,"max-width":"320",class:"mt-2"},null,8,["src"])):n("",!0),f("span",re,[f("span",{class:"text-h3",style:F(u.getLoginColoredStyle(i.viewableUser)),innerHTML:u.getUserNamePretty()},null,12,ne),f("span",le,[i.online?(r(),m("span",oe,[a(_,{color:e.getUserBadgeColor(this)},{default:s(()=>t[1]||(t[1]=[c("mdi-checkbox-marked-circle")])),_:1},8,["color"]),f("span",ue,d(i.isInVideo?e.$vuetify.locale.t("$vuetify.user_in_video_call"):e.$vuetify.locale.t("$vuetify.user_online")),1)])):(r(),m("span",ce,[a(_,null,{default:s(()=>t[2]||(t[2]=[c("mdi-checkbox-marked-circle")])),_:1}),f("span",de,d(e.$vuetify.locale.t("$vuetify.user_offline")),1)]))])]),i.viewableUser.lastSeenDateTime?(r(),l(H,{key:1,class:"title px-0 pb-0"},{default:s(()=>[c(d(e.$vuetify.locale.t("$vuetify.last_seen_at",u.getHumanReadableDate(i.viewableUser.lastSeenDateTime))),1)]),_:1})):n("",!0),u.displayShortInfo(i.viewableUser)?(r(),m("span",{key:2,class:"mx-0 my-1 force-wrap",innerHTML:i.viewableUser.shortInfo},null,8,he)):n("",!0),a(S,{class:"ma-0 pa-0 pt-1"},{default:s(()=>[a(I,{color:"primary",onClick:t[0]||(t[0]=y=>u.tetATet(i.viewableUser.id))},{prepend:s(()=>[a(_,null,{default:s(()=>t[3]||(t[3]=[c("mdi-message-text-outline")])),_:1})]),default:s(()=>[c(d(e.$vuetify.locale.t("$vuetify.user_open_chat")),1)]),_:1}),a(I,{class:"ml-2",variant:"plain",onClick:u.onShowContextMenu,icon:"mdi-menu"},null,8,["onClick"])]),_:1})]),_:1}),(x=i.viewableUser)!=null&&x.additionalData?(r(),m(C,{key:0},[a(g,{class:"title pb-0 pt-1"},{default:s(()=>[c(d(e.$vuetify.locale.t("$vuetify.roles")),1)]),_:1}),a(A,{class:"mx-2 nominheight"},{default:s(()=>{var y,k;return[(r(!0),m(C,null,Q((k=(y=i.viewableUser)==null?void 0:y.additionalData)==null?void 0:k.roles,(N,be)=>(r(),l(p,{density:"comfortable","text-color":"white"},{default:s(()=>[f("span",null,d(N),1)]),_:2},1024))),256))]}),_:1})],64)):n("",!0),i.viewableUser.ldap?(r(),l(g,{key:1,class:"title pb-0 pt-1"},{default:s(()=>t[4]||(t[4]=[c("LDAP")])),_:1})):n("",!0),i.viewableUser.ldap?(r(),l(p,{key:2,density:"comfortable",class:"mx-4 c-btn-database","text-color":"white"},{prepend:s(()=>[a(U,{icon:{prefix:"fas",iconName:"database"}})]),default:s(()=>t[5]||(t[5]=[f("span",null," Ldap ",-1)])),_:1})):n("",!0),a(g,{class:"title pb-0 pt-1"},{default:s(()=>[c(d(e.$vuetify.locale.t("$vuetify.bound_oauth2_providers")),1)]),_:1}),u.shouldShowBound()?(r(),l(A,{key:3,class:"mx-2"},{default:s(()=>[i.viewableUser.oauth2Identifiers.vkontakteId?(r(),l(p,{key:0,"min-width":"80px",label:"",class:"c-btn-vk py-5","text-color":"white"},{default:s(()=>[a(U,{icon:{prefix:"fab",iconName:"vk"},size:"2x"})]),_:1})):n("",!0),i.viewableUser.oauth2Identifiers.facebookId?(r(),l(p,{key:1,"min-width":"80px",label:"",class:"c-btn-fb py-5","text-color":"white"},{default:s(()=>[a(U,{icon:{prefix:"fab",iconName:"facebook"},size:"2x"})]),_:1})):n("",!0),i.viewableUser.oauth2Identifiers.googleId?(r(),l(p,{key:2,"min-width":"80px",label:"",class:"c-btn-google py-5","text-color":"white"},{default:s(()=>[a(U,{icon:{prefix:"fab",iconName:"google"},size:"2x"})]),_:1})):n("",!0),i.viewableUser.oauth2Identifiers.keycloakId?(r(),l(p,{key:3,"min-width":"80px",label:"",class:"c-btn-keycloak py-5","text-color":"white"},{default:s(()=>[a(U,{icon:{prefix:"fa",iconName:"key"},size:"2x"})]),_:1})):n("",!0)]),_:1})):n("",!0),a(P,{ref:"contextMenuRef",onTetATet:this.tetATetUser,onUnlockUser:this.unlockUser,onLockUser:this.lockUser,onUnconfirmUser:this.unconfirmUser,onConfirmUser:this.confirmUser,onDeleteUser:this.deleteUser,onChangeRole:this.changeRole,onRemoveSessions:this.removeSessions,onEnableUser:this.enableUser,onDisableUser:this.disableUser,onSetPassword:this.setPassword},null,8,["onTetATet","onUnlockUser","onLockUser","onUnconfirmUser","onConfirmUser","onDeleteUser","onChangeRole","onRemoveSessions","onEnableUser","onDisableUser","onSetPassword"]),a(D)]}),_:1})):n("",!0)}const me=V(ae,[["render",fe],["__scopeId","data-v-9f6dbb9a"]]);export{me as default};
