scalar Time
scalar Int64
scalar UUID

type AdditionalData {
    enabled: Boolean!
    expired: Boolean!
    locked: Boolean!
    confirmed: Boolean!
    roles: [String!]!
}

type Participant {
    id:     Int64!
    login:  String!
    avatar: String
    shortInfo:           String
    loginColor: String
    additionalData: AdditionalData
}

type EmbedMessageResponse {
    id:     Int64!
    chatId: Int64
    chatName: String
    text:   String!
    owner:  Participant
    embedType: String!
    isParticipant: Boolean!
}

type Reaction {
    count:    Int64!
    users:    [Participant!]!
    reaction: String!
}

type DisplayMessageDto {
    id:             Int64!
    text:           String!
    chatId:         Int64!
    ownerId:        Int64!
    createDateTime: Time!
    editDateTime:   Time
    owner:          Participant
    canEdit:        Boolean!
    canDelete:      Boolean!
    fileItemUuid:   String
    embedMessage:   EmbedMessageResponse
    pinned:         Boolean!
    blogPost:       Boolean!
    pinnedPromoted: Boolean
    reactions:      [Reaction!]!
    published:      Boolean!
    canPublish:     Boolean!
    canPin:         Boolean!
}

type MessageDeletedDto {
    id:             Int64!
    chatId:             Int64!
}

type ParticipantWithAdmin {
    id:     Int64!
    login:  String!
    avatar: String
    admin: Boolean!
    shortInfo:           String
    loginColor: String
    additionalData: AdditionalData
}

type ChatDto {
    id:             Int64!
    name:                String!
    avatar:              String
    avatarBig:           String
    shortInfo:           String
    lastUpdateDateTime:  Time!
    participantIds:      [Int64!]!
    canEdit:             Boolean
    canDelete:           Boolean
    canLeave:            Boolean
    unreadMessages:      Int64!
    canBroadcast:        Boolean!
    canVideoKick:        Boolean!
    canChangeChatAdmins: Boolean!
    tetATet:             Boolean!
    canAudioMute:        Boolean!
    participants:             [Participant!]!
    participantsCount:        Int!
    canResend:           Boolean!
    availableToSearch:   Boolean!
    isResultFromSearch:  Boolean
    pinned:              Boolean!
    blog:                Boolean!
    loginColor:          String
    regularParticipantCanPublishMessage: Boolean!
    lastSeenDateTime: Time
    regularParticipantCanPinMessage: Boolean!
    blogAbout: Boolean!
    regularParticipantCanWriteMessage: Boolean!
    canWriteMessage: Boolean!
    lastMessagePreview: String
    canReact: Boolean!
    additionalData: AdditionalData
}

type ChatDeletedDto {
    id:             Int64!
}

type UserTypingDto {
    login: String!
    participantId: Int64!
    chatId: Int64!
}

type MessageBroadcastNotification {
    login: String!
    userId: Int64!
    text: String!
}

type PreviewCreatedEvent {
    id: String!
    url: String!
    previewUrl: String
    aType: String
    correlationId: String
}

type PublishedMessageEvent {
    message: PublishedMessageDto!
    count: Int64!
}

type PublishedMessageDto {
    id:             Int64!
    text:           String!
    chatId:         Int64!
    ownerId:        Int64!
    owner:          Participant
    canPublish:     Boolean!
    createDateTime: Time!
}

type PinnedMessageDto {
    id:             Int64!
    text:           String!
    chatId:         Int64!
    ownerId:        Int64!
    owner:          Participant
    pinnedPromoted: Boolean!
    createDateTime: Time!
    canPin:         Boolean!
}

type PinnedMessageEvent {
    message: PinnedMessageDto!
    count: Int64!
}

type FileInfoDto {
    id: String!
    filename: String!
    url: String!
    publishedUrl: String
    previewUrl: String
    size: Int64!
    canDelete:           Boolean!
    canEdit:             Boolean!
    canShare:             Boolean!
    lastModified: Time!
    ownerId: Int64!
    owner:  Participant
    canPlayAsVideo:           Boolean!
    canShowAsImage:  Boolean!
    canPlayAsAudio:  Boolean!
    fileItemUuid: String!
    correlationId: String
    previewable: Boolean!
    aType: String
}

type WrappedFileInfoDto {
    fileInfoDto: FileInfoDto
}

type ReactionChangedEvent {
    messageId: Int64!
    reaction: Reaction!
}

type ChatEvent {
    eventType:                String!
    messageEvent: DisplayMessageDto
    messageDeletedEvent: MessageDeletedDto
    messageBroadcastEvent: MessageBroadcastNotification
    previewCreatedEvent: PreviewCreatedEvent
    participantsEvent: [ParticipantWithAdmin!]
    promoteMessageEvent: PinnedMessageEvent
    fileEvent: WrappedFileInfoDto
    publishedMessageEvent: PublishedMessageEvent
    reactionChangedEvent: ReactionChangedEvent
}

type VideoUserCountChangedDto {
    usersCount: Int64!
    chatId: Int64!
}

type VideoCallScreenShareChangedDto {
    chatId: Int64!
    hasScreenShares: Boolean!
}

type VideoRecordingChangedDto {
    recordInProgress: Boolean!
    chatId: Int64!
}

type VideoCallInvitationDto {
    chatId: Int64!
    chatName: String!
    status: String!
    avatar: String
}

type VideoDialChanged {
    userId: Int64!
    status: String!
}

type VideoDialChanges {
    chatId: Int64!
    dials: [VideoDialChanged!]!
}

type ChatUnreadMessageChanged {
    chatId: Int64!
    unreadMessages: Int64!
    lastUpdateDateTime:  Time!
}

type AllUnreadMessages {
    allUnreadMessages: Int64!
}

type WrapperNotificationDto {
    count: Int64!
    notificationDto: NotificationDto!
}

type NotificationDto {
    id: Int64!
    chatId: Int64!
    messageId: Int64
    notificationType: String!
    description: String!
    createDateTime: Time!
    byUserId: Int64!
    byLogin:  String!
    byAvatar: String
    chatTitle: String!
}

type ForceLogoutEvent {
    reasonType: String!
}

type HasUnreadMessagesChangedEvent {
    hasUnreadMessages: Boolean!
}

type BrowserNotification {
    chatId: Int64!
    chatName: String!
    chatAvatar: String
    messageId: Int64!
    messageText: String!
    ownerId: Int64!
    ownerLogin: String!
}

type GlobalEvent {
    eventType:                String!
    chatEvent: ChatDto
    chatDeletedEvent: ChatDeletedDto
    coChattedParticipantEvent: Participant
    videoUserCountChangedEvent: VideoUserCountChangedDto
    videoRecordingChangedEvent: VideoRecordingChangedDto
    videoCallInvitation: VideoCallInvitationDto
    videoParticipantDialEvent: VideoDialChanges
    unreadMessagesNotification: ChatUnreadMessageChanged
    allUnreadMessagesNotification: AllUnreadMessages
    notificationEvent: WrapperNotificationDto
    videoCallScreenShareChangedDto: VideoCallScreenShareChangedDto
    forceLogout: ForceLogoutEvent
    hasUnreadMessagesChanged: HasUnreadMessagesChangedEvent
    browserNotification: BrowserNotification
    userTypingEvent: UserTypingDto
}

type UserStatusEvent {
    userId:     Int64!
    online:     Boolean
    isInVideo:  Boolean
    lastSeenDateTime: Time
    eventType:  String!
}

type OAuth2Identifiers {
    facebookId: String
    vkontakteId: String
    googleId: String
    keycloakId: String
}

type UserDeletedDto {
    id:         Int64!
}

type UserAccountExtendedDto {
    id:         Int64!
    login:      String!
    email:      String # not null only in case myself account
    awaitingForConfirmEmailChange: Boolean # not null only in case myself account
    avatar:     String # url
    avatarBig:  String # url
    shortInfo: String
    lastSeenDateTime: Time
    oauth2Identifiers: OAuth2Identifiers
    additionalData: AdditionalData
    canLock: Boolean!
    canEnable: Boolean!
    canDelete: Boolean!
    canChangeRole: Boolean!
    canConfirm: Boolean!
    loginColor:  String
    canRemoveSessions: Boolean!
    ldap: Boolean!
    canSetPassword: Boolean!
}

union UserAccountEventDto = UserAccountExtendedDto | UserDeletedDto

type UserAccountEvent {
    eventType:  String!
    userAccountEvent: UserAccountEventDto
}

type Query {
    ping: Boolean
}

type Subscription {
    chatEvents(chatId: Int64!): ChatEvent!
    globalEvents: GlobalEvent!
    userStatusEvents(userIds: [Int64!]!): [UserStatusEvent!]!
    userAccountEvents(userIdsFilter: [Int64!]): UserAccountEvent!
}
