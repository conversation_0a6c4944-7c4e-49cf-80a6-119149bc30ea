# This file used for both developer and demo purposes.
# It contains environment
version: '3.7'

services:
  # https://habr.com/en/amp/post/552346/
  # https://keycloak.discourse.group/t/issue-on-userinfo-endpoint-at-keycloak-20/18461/4
  keycloak:
    # https://quay.io/repository/keycloak/keycloak
    image: quay.io/keycloak/keycloak:26.1.2-0
    restart: unless-stopped
    volumes:
      - ./docker/keycloak/realm-export.json:/opt/keycloak/data/import/realm-export.json:ro,z
    # https://www.keycloak.org/server/containers
    environment:
      KC_BOOTSTRAP_ADMIN_USERNAME: keycloak_admin
      KC_BOOTSTRAP_ADMIN_PASSWORD: admin_password
    ports:
      - "8484:8080"
      - "9484:9000"
    # https://www.keycloak.org/server/importExport
    command: [
      "start-dev",
      "--import-realm",
      # https://www.keycloak.org/server/health
      "--health-enabled=true"
      # "--log-level=DEBUG"
    ]
