# https://docs.traefik.io/reference/static-configuration/file/
global:
  checkNewVersion: false
  sendAnonymousUsage: false
entryPoints:
  http:
    address: :8081
  traefik:
    address: :8080
providers:
  providersThrottleDuration: 2s
  docker:
    watch: true
    exposedByDefault: false
  file:
    directory: /etc/traefik/dynamic
api:
  insecure: true
  dashboard: false
log:
  level: INFO
  format: json
accessLog:
  format: json
  fields:
    defaultMode: keep
    headers:
      defaultMode: drop
      names:
        User-Agent: keep
        uber-trace-id: keep
tracing:
  sampleRate: 1.0
  otlp:
    http:
      endpoint: http://jaeger:4318/v1/traces
