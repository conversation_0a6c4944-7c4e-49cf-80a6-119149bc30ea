import{_ as u,m,c_ as d,c as s,w as i,b as l,a as f,c$ as h,bW as c,j as p,e as y,t as $,k as v,d0 as V,aZ as b,s as o,a2 as T,d5 as S,T as _,A as g,B as n}from"./appMain-DAGYo8tP.js";const w={mixins:[d()],data:()=>({email:null,error:""}),computed:{...m(g),showError(){return _(this.error)}},methods:{onSubmit(){T.post("/api/aaa/resend-confirmation-email",null,{params:{email:this.email,language:this.$vuetify.locale.current}}).then(()=>{this.$router.push({name:S})}).catch(e=>{this.error=e.message})},hideAlert(){this.error=""},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.resending_confirmation_email"),o(this.$vuetify.locale.t("$vuetify.resending_confirmation_email"))}},watch:{"$vuetify.locale.current":{handler:function(e,t){this.setTopTitle()}}},mounted(){this.setTopTitle()},beforeUnmount(){this.chatStore.title=null,o(null),this.error=""}};function k(e,t,x,A,B,a){return n(),s(b,{"max-width":"640",class:"px-2 pt-2"},{default:i(()=>[l(V,{"fast-fail":"",onSubmit:t[2]||(t[2]=v(r=>a.onSubmit(),["prevent"]))},{default:i(()=>[l(h,{onInput:t[0]||(t[0]=r=>a.hideAlert()),modelValue:e.email,"onUpdate:modelValue":t[1]||(t[1]=r=>e.email=r),label:e.$vuetify.locale.t("$vuetify.email"),rules:[e.rules.required,e.rules.email],variant:"underlined"},null,8,["modelValue","label","rules"]),a.showError?(n(),s(c,{key:0,density:"compact",type:"error",text:e.error},null,8,["text"])):f("",!0),l(p,{type:"submit",color:"primary",block:"",class:"mt-2"},{default:i(()=>[y($(e.$vuetify.locale.t("$vuetify.request_resend_confirmation_email")),1)]),_:1})]),_:1})]),_:1})}const E=u(w,[["render",k]]);export{E as default};
