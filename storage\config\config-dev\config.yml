server:
  address: ":1236"
  shutdown.timeout: "10s"
  body.limit: "100G"

logger:
  level: info
  dir: "log"
  filename: "file.log"
  writeToFile: true

# Rest client
http:
  maxIdleConns: 2
  idleConnTimeout: '10s'
  disableCompression: false

auth:
  exclude:
    - "^/api/storage/public.*"
    - "^/internal.*"

types:
  image:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".webp"
    - ".gif"
  video:
    - ".mp4"
    - ".webm"
    - ".mov"
    - ".m4v"
  audio:
    - ".mp3"
    - ".m4a"
    - ".ogg"
  plainText:
    - ".txt"

rabbitmq:
  url: "amqp://videoChat:videoChatPazZw0rd@127.0.0.1:36672"

otlp:
  endpoint: "localhost:34317"

minio:
  secured: false
  internalEndpoint: 127.0.0.1:39000
  interContainerUrl: http://minio:9000
  externalS3UrlPrefix: /api/s3
  presignDownloadTtl: 24h
  presignUploadTtl: 24h
  accessKeyId: AKIAIOSFODNN7EXAMPLE
  secretAccessKey: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
  location: "europe-east"
  multipart:
    # https://www.altostra.com/blog/multipart-uploads-with-s3-presigned-url
    # 100MB
    chunkSize: 100000000
    expire: 24h
  bucket:
    userAvatar: "user-avatar"
    chatAvatar: "chat-avatar"
    files: "files"
    filesPreview: "files-preview"

chat:
  url:
    base: "http://localhost:1235"
    access: "/internal/access"
    removeFileItem: "/internal/remove-file-item"
    checkChatExistsPath: "/internal/does-chats-exist"
    chatParticipants: "/internal/participant-ids"

aaa:
  url:
    base: "http://localhost:8060"
    getUsers: "/internal/user/list"

limits:
  enabled: false
  default:
    # 512 megabytes
    all.users.limit: 536870912

selfUrls: "http://localhost:8081,"

postgresql:
  # https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING
  url: "postgres://postgres:postgresqlPassword@localhost:45401/storage?sslmode=disable&application_name=storage-app"
  maxOpenConnections: 16
  maxIdleConnections: 4
  maxLifetime: 30s

redis:
  address: :36379
  password: ""
  db: 3
  maxRetries: 10000

schedulers:
  cleanFilesOfDeletedChatTask:
    enabled: true
    cron: "0 * * * * *"
    batchChats: 20
    expiration: "30m"
  actualizeGeneratedFilesTask:
    enabled: true
    cron: "0 * * * * *"
    expiration: "30m"
  actualizeMetadataCacheTask:
    enabled: true
    cron: "0 */5 * * * *"
    expiration: "30m"

response:
  cache:
    preview: 24h
    avatar: 48h
    delta: 30m

# should be aligned with message.maxMedias in chat
viewList:
  maxSize: 100

ulid:
  topYear: 3000

converting:
  ffmpegPath: "ffmpeg"
  tempDir: "/tmp/video"
  presignedDuration: 30m
  maxDuration: 1h
  removeOriginal: true

# generating previews for images, videos, ...
preview:
  ffmpegPath: "ffmpeg"
  presignedDuration: 10m
