[SERVICE]
    log_level info
    Parsers_File parsers.conf

[INPUT]
    Name forward
    Unix_Path /var/run/fluent-bit/socket
    Unix_Perm 0777

# for debug purposes
# [FILTER]
#     Name  stdout
#     Match *

[FILTER]
    Name parser
    Match videochat.app.*
    Parser just_json
    Reserve_Data on
    Key_Name log

[FILTER]
    Name parser
    Match videochat.infra.traefik
    Parser just_json
    Reserve_Data on
    Key_Name log

[FILTER]
    Name modify
    Match videochat.infra.traefik
    Rename msg message
    Rename TraceId trace_id
    Rename SpanId span_id
    Rename StartUTC @timestamp
    Rename request_User-Agent user_agent
    Add service traefik

# [OUTPUT]
#     Name stdout
#     Match **
[OUTPUT]
    Name opensearch
    Match **
    Host opensearch
    Port 9200
    # When Logstash_Format is enabled, the Index name is composed using a prefix and the date
    Logstash_Format True
    Logstash_Prefix log
#    HTTP_User admin
#    HTTP_Passwd admin
#    tls On
#    tls.verify Off
    Suppress_Type_Name On
    Include_Tag_Key True
    Generate_ID True
    # https://github.com/fluent/fluent-bit/issues/309#issuecomment-311685689
    Retry_Limit 100
    Trace_Error On
    # Alternative time key, useful if your log entries contain an @timestamp field that is used by Elasticsearch
    # Time_Key es_time
    Time_Key_Nanos On
