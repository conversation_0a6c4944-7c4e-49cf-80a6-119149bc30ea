import{_ as n,m as d,c_ as m,c as o,w as s,b as l,a as h,c$ as p,bW as f,j as c,e as y,t as $,k as v,d0 as V,aZ as w,s as i,a2 as b,d5 as T,T as S,A as k,B as u}from"./appMain-DAGYo8tP.js";const _={mixins:[m()],data:()=>({email:null,error:""}),computed:{...d(k),showError(){return S(this.error)}},methods:{onSubmit(){b.post("/api/aaa/request-password-reset",null,{params:{email:this.email,language:this.$vuetify.locale.current}}).then(()=>{this.$router.push({name:T})}).catch(e=>{this.error=e.message})},hideAlert(){this.error=""},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.password_restoration"),i(this.$vuetify.locale.t("$vuetify.password_restoration"))}},watch:{"$vuetify.locale.current":{handler:function(e,t){this.setTopTitle()}}},mounted(){this.setTopTitle()},beforeUnmount(){this.chatStore.title=null,i(null),this.hideAlert()}};function g(e,t,x,A,B,a){return u(),o(w,{"max-width":"640",class:"px-2 pt-2"},{default:s(()=>[l(V,{"fast-fail":"",onSubmit:t[2]||(t[2]=v(r=>a.onSubmit(),["prevent"]))},{default:s(()=>[l(p,{onInput:t[0]||(t[0]=r=>a.hideAlert()),modelValue:e.email,"onUpdate:modelValue":t[1]||(t[1]=r=>e.email=r),label:e.$vuetify.locale.t("$vuetify.email"),rules:[e.rules.required,e.rules.email],variant:"underlined"},null,8,["modelValue","label","rules"]),a.showError?(u(),o(f,{key:0,density:"compact",type:"error",text:e.error},null,8,["text"])):h("",!0),l(c,{type:"submit",color:"primary",block:"",class:"mt-2"},{default:s(()=>[y($(e.$vuetify.locale.t("$vuetify.request_password_reset")),1)]),_:1})]),_:1})]),_:1})}const C=n(_,[["render",g]]);export{C as default};
