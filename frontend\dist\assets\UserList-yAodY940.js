import{_ as q,T as b,g as $,m as z,h as G,aa as Q,bV as W,ab as K,c as d,w as s,b as o,f as l,G as y,a as m,a8 as k,a7 as L,aZ as Z,e as C,t as _,b2 as j,a5 as U,n as A,p as X,z as n,cq as x,cM as I,ah as R,ai as w,al as D,s as M,ao as J,c6 as Y,cv as ee,U as N,cN as te,a2 as a,cL as se,aO as ie,aP as re,a9 as oe,cJ as ne,aX as F,aY as ae,aL as O,cK as le,a3 as he,a4 as ce,cO as ue,cP as de,aV as me,aU as fe,cQ as pe,cR as V,cS as ge,a0 as Se,A as Ie,B as h,b4 as be,k as v,cD as Ue,cE as ve,cF as Te,cI as f,bX as Ee,j as ye,l as ke}from"./appMain-DAGYo8tP.js";import{i as Le,h as Ce,a as _e,V as Ae}from"./hashMixin-OoEehrvU.js";import{a as xe,U as Re}from"./UserRoleModal-COewB_-i.js";const T=40,B=200,E="UserList",we={components:{UserListContextMenu:Re,UserRoleModal:xe},mixins:[Le(E),Ce(),G(),Q(I),W("userStatusInUserList"),K()],data(){return{markInstance:null,userEventsSubscription:null,isLoading:!1,initialized:!1}},computed:{...z(Ie),itemIds(){return this.getUserIdsSubscribeTo()}},methods:{getLoginColoredStyle:$,getUserNameOverride(e){return Se(e)?"<s>"+this.getUserName(e)+"</s>":this.getUserName(e)},hasLength:b,getMaxItemsLength(){return 240},getReduceToLength(){return 80},reduceBottom(){this.items=this.items.slice(0,this.getReduceToLength()),this.startingFromItemIdBottom=this.findBottomElementId()},reduceTop(){this.items=this.items.slice(-this.getReduceToLength()),this.startingFromItemIdTop=this.findTopElementId()},enableHashInRoute(){return!1},convertLoadedFromStoreHash(e){return ge+e},extractIdFromElementForStoring(e){return this.getIdFromRouteHash(e.id)},saveScroll(e){this.preservedScroll=e?this.findTopElementId():this.findBottomElementId(),console.log("Saved scroll",this.preservedScroll,"in ",E)},initialDirection(){return _e},async scrollTop(){return V(),await this.$nextTick(()=>{this.scrollerDiv.scrollTop=0})},async onFirstLoad(e){await this.doScrollOnFirstLoad(),e===!0&&V()},async doDefaultScroll(){await this.scrollTop()},getPositionFromStore(){return pe()},async fetchItems(e,t,i){const p=(await a.get("/api/aaa/user/search",{params:{startingFromItemId:e,includeStartingFrom:!!i,size:T,reverse:t,searchString:this.searchString}},{signal:this.requestAbortController.signal})).data.items;return console.log("Get items in ",E,p,"page",this.startingFromItemIdTop,this.startingFromItemIdBottom),p.forEach(u=>{this.transformItem(u)}),p},async load(){if(!this.canDrawUsers())return Promise.resolve();this.chatStore.incrementProgressCount(),this.isLoading=!0;const{startingFromItemId:e,hasHash:t}=this.prepareHashesForRequest();try{let i=await this.fetchItems(e,this.isTopDirection());return t&&(i=(await this.fetchItems(e,!this.isTopDirection(),!0)).reverse().concat(i)),this.isTopDirection()?me(this.items,i):fe(this.items,i),this.updateTopAndBottomIds(),this.isFirstLoad||await this.clearRouteHash(),this.graphQlUserStatusSubscribe(),this.performMarking(),this.requestStatuses(),Promise.resolve(!0)}finally{this.chatStore.decrementProgressCount(),this.isLoading=!1}},afterScrollRestored(e){var t;(t=e==null?void 0:e.parentElement)==null||t.scrollBy({top:this.isTopDirection()?-10:10,behavior:"instant"})},bottomElementSelector(){return".user-last-element"},topElementSelector(){return".user-first-element"},getItemId(e){return de+e},scrollerSelector(){return".my-user-scroller"},reset(){this.resetInfiniteScrollVars(),this.startingFromItemIdTop=null,this.startingFromItemIdBottom=null},async onSearchStringChanged(){this.isReady()&&await this.reloadItems()},async onProfileSet(){await this.initializeHashVariablesAndReloadItems(),this.userEventsSubscription.graphQlSubscribe()},async doInitialize(){this.initialized||(this.initialized=!0,await this.onProfileSet())},conditionToSaveLastVisible(){return!this.isScrolledToTop()},itemSelector(){return".user-item-root"},setPositionToStore(e){ue(e)},beforeUnload(){this.saveLastVisibleElement()},onLoggedOut(){this.beforeUnload(),this.reset(),this.graphQlUserStatusUnsubscribe(),this.userEventsSubscription.graphQlUnsubscribe()},doUninitialize(){this.initialized&&(this.onLoggedOut(),this.initialized=!1)},canDrawUsers(){return!!this.chatStore.currentUser},openUser(e){this.$router.push({name:ce,params:{id:e.id}})},getLink(e){return he+"/"+e.id},setTopTitle(){this.chatStore.title=this.$vuetify.locale.t("$vuetify.users"),M(this.$vuetify.locale.t("$vuetify.users"))},performMarking(){this.$nextTick(()=>{b(this.searchString)&&(this.markInstance.unmark(),this.markInstance.mark(this.searchString))})},isScrolledToTop(){return this.scrollerDiv?Math.abs(this.scrollerDiv.scrollTop)<B:!1},isScrolledToBottom(){return this.scrollerDiv?Math.abs(this.scrollerDiv.scrollHeight-this.scrollerDiv.scrollTop-this.scrollerDiv.clientHeight)<B:!1},updateTopAndBottomIds(){this.startingFromItemIdTop=this.findTopElementId(),this.startingFromItemIdBottom=this.findBottomElementId()},getUserIdsSubscribeTo(){return this.items.map(e=>e.id)},onUserStatusChanged(e){e&&this.items.forEach(t=>{e.forEach(i=>{i.online!==null&&t.id==i.userId&&(t.online=i.online),i.isInVideo!==null&&t.id==i.userId&&(t.isInVideo=i.isInVideo)})})},getGraphQlSubscriptionQuery(){return`
                subscription {
                  userAccountEvents {
                    userAccountEvent {
                      ${le()},
                      ... on UserDeletedDto {
                        id
                      }
                    }
                    eventType
                  }
                }
            `},onNextSubscriptionElement(e){var i;const t=(i=e.data)==null?void 0:i.userAccountEvents;if(t.eventType==="user_account_changed"){const c=O(t.userAccountEvent);this.transformItem(c),this.onEditUser(c)}else if(t.eventType==="user_account_deleted")this.onDeleteUser(t.userAccountEvent);else if(t.eventType==="user_account_created"){const c=O(t.userAccountEvent);this.transformItem(c),this.onNewUser(c)}},hasItem(e){return F(this.items,e)!==-1},addItem(e){console.log("Adding item",e),this.transformItem(e),this.items.unshift(e),this.reduceListAfterAdd(!1),this.updateTopAndBottomIds()},changeItem(e){console.log("Replacing item",e),this.transformItem(e),ae(this.items,e)},removeItem(e){if(this.hasItem(e)){console.log("Removing item",e);const t=F(this.items,e);this.items.splice(t,1),this.updateTopAndBottomIds()}else console.log("Item was not be removed",e)},onNewUser(e){const t=this.isScrolledToTop(),i=!b(this.searchString);t&&i?(this.addItem(e),this.performMarking(),this.scrollTop()):t?a.post("/api/aaa/user/filter",{searchString:this.searchString,userId:e.id},{signal:this.requestAbortController.signal}).then(({data:c})=>{c.found&&(this.addItem(e),this.performMarking(),this.scrollTop())}):console.log("Skipping",e,t,i)},onDeleteUser(e){this.removeItem(e)},onEditUser(e){this.changeItem(e),this.performMarking()},onShowContextMenu(e,t){this.$refs.contextMenuRef.onShowContextMenu(e,t)},unlockUser(e){a.post("/api/aaa/user/lock",{userId:e.id,lock:!1},{signal:this.requestAbortController.signal})},lockUser(e){a.post("/api/aaa/user/lock",{userId:e.id,lock:!0},{signal:this.requestAbortController.signal})},enableUser(e){a.post("/api/aaa/user/enable",{userId:e.id,enable:!0},{signal:this.requestAbortController.signal})},disableUser(e){a.post("/api/aaa/user/enable",{userId:e.id,enable:!1},{signal:this.requestAbortController.signal})},setPassword(e){n.emit(ne,{userId:e.id,userName:e.login})},tetATet(e){a.put(`/api/chat/tet-a-tet/${e.id}`,null,{signal:this.requestAbortController.signal}).then(t=>{this.$router.push({name:oe,params:{id:t.data.id}})})},unconfirmUser(e){a.post("/api/aaa/user/confirm",{userId:e.id,confirm:!1},{signal:this.requestAbortController.signal})},confirmUser(e){a.post("/api/aaa/user/confirm",{userId:e.id,confirm:!0},{signal:this.requestAbortController.signal})},deleteUser(e){n.emit(ie,{buttonName:this.$vuetify.locale.t("$vuetify.delete_btn"),title:this.$vuetify.locale.t("$vuetify.delete_user_title",e.id),text:this.$vuetify.locale.t("$vuetify.delete_user_text",e.login),actionFunction:t=>{t.loading=!0,a.delete("/api/aaa/user",{params:{userId:e.id},signal:this.requestAbortController.signal}).then(()=>{n.emit(re)}).finally(()=>{t.loading=!1})}})},changeRole(e){n.emit(se,e)},removeSessions(e){a.delete("/api/aaa/sessions",{params:{userId:e.id},signal:this.requestAbortController.signal})},onFocus(){if(this.chatStore.currentUser&&this.items&&(this.requestStatuses(),this.isScrolledToTop())){const e=this.items.slice(0,T);a.post("/api/aaa/user/fresh",e,{params:{size:T,searchString:this.searchString},signal:this.requestAbortController.signal}).then(t=>{t.data.ok?console.log("No need to update users"):(console.log("Need to update users"),this.reloadItems())})}},onWsRestoredRefresh(){this.saveLastVisibleElement(),this.doOnFocus()},requestStatuses(){this.$nextTick(()=>{const t=this.items.map(i=>i.id).join(",");this.triggerUsesStatusesEvents(t,this.requestAbortController.signal)})},findBottomElementId(){var e;return(e=this.items[this.items.length-1])==null?void 0:e.id},findTopElementId(){var e;return(e=this.items[0])==null?void 0:e.id},isAppropriateHash(e){return te(e)}},created(){this.onSearchStringChanged=N(this.onSearchStringChanged,700,{leading:!1,trailing:!0}),this.onWsRestoredRefresh=N(this.onWsRestoredRefresh,300,{leading:!1,trailing:!0})},watch:{"$vuetify.locale.current":{handler:function(e,t){this.setTopTitle()}},itemIds:function(e,t){t.length!==0&&e.length===0?this.graphQlUserStatusUnsubscribe():ee(t,e)||this.graphQlUserStatusSubscribe()}},async mounted(){this.markInstance=new J("div#user-list-items .user-name"),this.setTopTitle(),this.chatStore.isShowSearch=!0,this.chatStore.searchType=I,this.userEventsSubscription=Y("userAccountEvents",this.getGraphQlSubscriptionQuery,this.setErrorSilent,this.onNextSubscriptionElement),addEventListener("beforeunload",this.beforeUnload),n.on(x+"."+I,this.onSearchStringChanged),n.on(R,this.doInitialize),n.on(w,this.doUninitialize),n.on(D,this.onWsRestoredRefresh),this.canDrawUsers()&&await this.doInitialize(),this.installOnFocus()},beforeUnmount(){this.saveLastVisibleElement(),this.doUninitialize(),this.uninstallOnFocus(),this.markInstance.unmark(),this.markInstance=null,removeEventListener("beforeunload",this.beforeUnload),this.uninstallScroller(),n.off(x+"."+I,this.onSearchStringChanged),n.off(R,this.doInitialize),n.off(w,this.doUninitialize),n.off(D,this.onWsRestoredRefresh),M(null),this.chatStore.title=null,this.chatStore.isShowSearch=!1,this.userEventsSubscription=null}},De={class:"item-avatar"},Me=["src"],Ne=["innerHTML"];function Fe(e,t,i,c,p,u){const g=U("font-awesome-icon"),P=U("UserListContextMenu"),H=U("UserRoleModal");return h(),d(X,{style:A(e.heightWithoutAppBar),fluid:"",class:"ma-0 pa-0"},{default:s(()=>[o(j,{id:"user-list-items",class:"my-user-scroller",onScrollPassive:e.onScroll},{default:s(()=>[t[6]||(t[6]=l("div",{class:"user-first-element",style:{"min-height":"1px",background:"white"}},null,-1)),(h(!0),y(L,null,k(e.items,(r,Oe)=>(h(),d(be,{key:r.id,id:u.getItemId(r.id),class:"list-item-prepend-spacer pb-2 user-item-root",onContextmenu:v(S=>u.onShowContextMenu(S,r),["stop"]),onClick:v(S=>u.openUser(r),["prevent"]),href:u.getLink(r)},Ue({default:s(()=>[o(ve,null,{default:s(()=>[l("span",{class:"user-name",style:A(u.getLoginColoredStyle(r)),innerHTML:u.getUserNameOverride(r)},null,12,Ne)]),_:2},1024),o(Te,null,{default:s(()=>[r.oauth2Identifiers.vkontakteId?(h(),d(f,{key:0,density:"comfortable",class:"mr-1 c-btn-vk","text-color":"white"},{prepend:s(()=>[o(g,{icon:{prefix:"fab",iconName:"vk"}})]),default:s(()=>t[0]||(t[0]=[l("span",{class:"ml-1"}," Vkontakte ",-1)])),_:1})):m("",!0),r.oauth2Identifiers.facebookId?(h(),d(f,{key:1,density:"comfortable",class:"mr-1 c-btn-fb","text-color":"white"},{prepend:s(()=>[o(g,{icon:{prefix:"fab",iconName:"facebook"}})]),default:s(()=>t[1]||(t[1]=[l("span",{class:"ml-1"}," Facebook ",-1)])),_:1})):m("",!0),r.oauth2Identifiers.googleId?(h(),d(f,{key:2,density:"comfortable",class:"mr-1 c-btn-google","text-color":"white"},{prepend:s(()=>[o(g,{icon:{prefix:"fab",iconName:"google"}})]),default:s(()=>t[2]||(t[2]=[l("span",{class:"ml-1"}," Google ",-1)])),_:1})):m("",!0),r.oauth2Identifiers.keycloakId?(h(),d(f,{key:3,density:"comfortable",class:"mr-1 c-btn-keycloak","text-color":"white"},{prepend:s(()=>[o(g,{icon:{prefix:"fa",iconName:"key"}})]),default:s(()=>t[3]||(t[3]=[l("span",{class:"ml-1"}," Keycloak ",-1)])),_:1})):m("",!0),r.ldap?(h(),d(f,{key:4,density:"comfortable",class:"mr-1 c-btn-database","text-color":"white"},{prepend:s(()=>[o(g,{icon:{prefix:"fas",iconName:"database"}})]),default:s(()=>t[4]||(t[4]=[l("span",{class:"ml-1"}," Ldap ",-1)])),_:1})):m("",!0),r.additionalData?(h(!0),y(L,{key:5},k(r.additionalData.roles,(S,Ve)=>(h(),d(f,{density:"comfortable",class:"mr-1","text-color":"white"},{default:s(()=>[l("span",null,_(S),1)]),_:2},1024))),256)):m("",!0)]),_:2},1024)]),_:2},[u.hasLength(r.avatar)?{name:"prepend",fn:s(()=>[o(Ee,{color:e.getUserBadgeColor(r),dot:"",location:"right bottom",overlap:"",bordered:"","model-value":r.online},{default:s(()=>[l("span",De,[l("img",{src:r.avatar},null,8,Me)])]),_:2},1032,["color","model-value"])]),key:"0"}:void 0,e.isMobile()?void 0:{name:"append",fn:s(()=>[o(Ae,null,{default:s(()=>[o(ye,{variant:"flat",icon:"",onClick:v(S=>u.tetATet(r),["stop","prevent"]),title:e.$vuetify.locale.t("$vuetify.user_open_chat")},{default:s(()=>[o(ke,{size:"large"},{default:s(()=>t[5]||(t[5]=[C("mdi-message-text-outline")])),_:1})]),_:2},1032,["onClick","title"])]),_:2},1024)]),key:"1"}]),1032,["id","onContextmenu","onClick","href"]))),128)),e.items.length==0&&!p.isLoading?(h(),d(Z,{key:0,class:"mx-2"},{default:s(()=>[C(_(e.$vuetify.locale.t("$vuetify.users_not_found")),1)]),_:1})):m("",!0),t[7]||(t[7]=l("div",{class:"user-last-element",style:{"min-height":"1px",background:"white"}},null,-1))]),_:1},8,["onScrollPassive"]),o(P,{ref:"contextMenuRef",onTetATet:this.tetATet,onUnlockUser:this.unlockUser,onLockUser:this.lockUser,onUnconfirmUser:this.unconfirmUser,onConfirmUser:this.confirmUser,onDeleteUser:this.deleteUser,onChangeRole:this.changeRole,onRemoveSessions:this.removeSessions,onEnableUser:this.enableUser,onDisableUser:this.disableUser,onSetPassword:this.setPassword},null,8,["onTetATet","onUnlockUser","onLockUser","onUnconfirmUser","onConfirmUser","onDeleteUser","onChangeRole","onRemoveSessions","onEnableUser","onDisableUser","onSetPassword"]),o(H)]),_:1},8,["style"])}const qe=q(we,[["render",Fe],["__scopeId","data-v-38801195"]]);export{qe as default};
